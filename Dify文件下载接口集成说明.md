# Dify文件下载接口集成说明

## 概述

本次修改将财务知识库的文档下载功能改为使用Dify提供的文件下载接口，以获取更准确的文件下载链接。

## 修改内容

### 1. IDifyService接口修改

**文件**: `backend/src/main/java/org/springblade/modules/yjzb/service/IDifyService.java`

新增方法：
```java
/**
 * 获取文档上传文件信息
 *
 * @param datasetId 知识库ID
 * @param documentId 文档ID
 * @return 文件信息JSON字符串
 */
String getDocumentUploadFile(String datasetId, String documentId);
```

### 2. DifyServiceImpl实现类修改

**文件**: `backend/src/main/java/org/springblade/modules/yjzb/service/impl/DifyServiceImpl.java`

新增方法实现：
```java
@Override
public String getDocumentUploadFile(String datasetId, String documentId) {
    try {
        String url = difyBaseUrl + "/v1/datasets/" + datasetId + "/documents/" + documentId + "/upload-file";

        HttpHeaders headers = createHeaders();
        HttpEntity<Void> entity = new HttpEntity<>(headers);

        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

        if (response.getStatusCode() == HttpStatus.OK) {
            return response.getBody();
        }

        log.warn("获取Dify文档上传文件信息失败，响应状态: {}", response.getStatusCode());
        return null;
    } catch (Exception e) {
        log.error("获取Dify文档上传文件信息异常", e);
        return null;
    }
}
```

### 3. IFinanceDocumentService接口修改

**文件**: `backend/src/main/java/org/springblade/modules/yjzb/service/IFinanceDocumentService.java`

新增方法：
```java
/**
 * 获取文档下载URL
 *
 * @param id 文档ID
 * @return 下载URL
 */
String getDocumentDownloadUrl(Long id);
```

### 4. FinanceDocumentServiceImpl实现类修改

**文件**: `backend/src/main/java/org/springblade/modules/yjzb/service/impl/FinanceDocumentServiceImpl.java`

新增导入：
```java
import com.fasterxml.jackson.databind.JsonNode;
```

新增方法实现：
```java
@Override
public String getDocumentDownloadUrl(Long id) {
    try {
        FinanceDocumentEntity document = getById(id);
        if (document == null) {
            log.error("文档不存在，ID: {}", id);
            return null;
        }

        // 如果文档已同步到Dify，优先从Dify获取下载链接
        if (Func.isNotBlank(document.getDocumentId())) {
            // 获取知识库信息
            FinanceKnowledgeEntity knowledge = financeKnowledgeService.getById(document.getKnowledgeId());
            if (knowledge != null && Func.isNotBlank(knowledge.getDatasetId())) {
                // 调用Dify接口获取文件信息
                String fileInfo = difyService.getDocumentUploadFile(knowledge.getDatasetId(), document.getDocumentId());
                if (Func.isNotBlank(fileInfo)) {
                    try {
                        JsonNode jsonNode = objectMapper.readTree(fileInfo);
                        if (jsonNode.has("download_url")) {
                            // 增加下载次数
                            incrementDownloadCount(id);
                            return jsonNode.get("download_url").asText();
                        }
                    } catch (Exception e) {
                        log.error("解析Dify文件信息失败", e);
                    }
                }
            }
        }

        // 如果从Dify获取失败，返回本地文件路径
        if (Func.isNotBlank(document.getFilePath())) {
            // 增加下载次数
            incrementDownloadCount(id);
            return document.getFilePath();
        }

        return null;
    } catch (Exception e) {
        log.error("获取文档下载URL失败，ID: {}", id, e);
        return null;
    }
}
```

### 5. FinanceDocumentController控制器修改

**文件**: `backend/src/main/java/org/springblade/modules/yjzb/controller/FinanceDocumentController.java`

修改下载方法：
```java
@GetMapping("/download/{id}")
@ApiOperationSupport(order = 10)
@Operation(summary = "下载文档", description = "传入文档ID")
public void download(@PathVariable Long id, HttpServletResponse response) throws IOException {
    String downloadUrl = financeDocumentService.getDocumentDownloadUrl(id);
    if (Func.isNotBlank(downloadUrl)) {
        // 如果是URL，重定向到下载地址
        if (downloadUrl.startsWith("http://") || downloadUrl.startsWith("https://")) {
            response.sendRedirect(downloadUrl);
        } else {
            // 如果是本地文件路径，设置下载头
            response.setHeader("Content-Disposition", "attachment; filename=" + downloadUrl);
            // 实际的文件下载逻辑需要根据具体的存储方式实现
            // 这里可以添加从MinIO或OSS下载文件的逻辑
        }
    } else {
        response.setStatus(HttpServletResponse.SC_NOT_FOUND);
    }
}
```

## Dify API接口说明

### 请求格式
```bash
curl --location --request GET 'http://localhost/v1/datasets/{dataset_id}/documents/{document_id}/upload-file' \
--header 'Authorization: Bearer {api_key}' \
--header 'Content-Type: application/json'
```

### 响应格式
```json
{
  "id": "file_id",
  "name": "file_name",
  "size": 1024,
  "extension": "txt",
  "url": "preview_url",
  "download_url": "download_url",
  "mime_type": "text/plain",
  "created_by": "user_id",
  "created_at": 1728734540
}
```

## 功能特点

1. **优先使用Dify下载链接**: 如果文档已同步到Dify，优先从Dify获取下载链接
2. **降级处理**: 如果Dify接口调用失败，自动降级到本地文件路径
3. **统计功能**: 无论使用哪种下载方式，都会正确统计下载次数
4. **错误处理**: 完善的异常处理和日志记录
5. **兼容性**: 保持与现有前端接口的兼容性

## 测试建议

1. 测试已同步到Dify的文档下载
2. 测试未同步到Dify的文档下载
3. 测试Dify接口异常时的降级处理
4. 测试下载次数统计功能
5. 测试不存在文档的错误处理

## 注意事项

1. 确保Dify API配置正确
2. 确保网络连接正常
3. 监控Dify接口调用的性能和稳定性
4. 考虑添加缓存机制以提高性能
