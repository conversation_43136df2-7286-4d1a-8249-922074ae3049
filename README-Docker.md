# AI指标管控平台 Docker Compose 部署指南

## 概述

本项目提供了完整的 Docker Compose 部署方案，包含以下服务：

- **后端服务**: Spring Boot 应用 (端口: 8080)
- **前端服务**: Vue.js + Nginx (端口: 80)
- **数据库**: PostgreSQL 15 (端口: 5432)
- **缓存**: Redis 7 (端口: 6379)
- **对象存储**: MinIO (API端口: 9000, 控制台端口: 9001)

## 系统要求

- Docker 20.10+
- Docker Compose 2.0+
- 系统内存: 最少 4GB，推荐 8GB+
- 磁盘空间: 最少 10GB 可用空间

## 快速开始

### 1. 克隆项目并进入目录

```bash
cd ai-indicator-control-platform
```

### 2. 配置环境变量

复制并编辑环境变量文件：

```bash
cp .env.example .env
# 编辑 .env 文件，修改相关配置
```

### 3. 部署服务

#### 开发环境部署

```bash
# 使用部署脚本
chmod +x deploy.sh
./deploy.sh dev

# 或者直接使用 docker-compose
docker-compose up --build -d
```

#### 生产环境部署

```bash
# 使用部署脚本
./deploy.sh prod

# 或者直接使用 docker-compose
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up --build -d
```

## 环境变量配置

### 数据库配置

```env
POSTGRES_DB=zj_db
POSTGRES_USER=root
POSTGRES_PASSWORD=Ystech@2025
POSTGRES_PORT=5432
```

### Redis 配置

```env
REDIS_PASSWORD=
REDIS_PORT=6379
```

### MinIO 配置

```env
MINIO_ROOT_USER=admin
MINIO_ROOT_PASSWORD=admin123456
MINIO_API_PORT=9000
MINIO_CONSOLE_PORT=9001
MINIO_BUCKET_NAME=images
```

### 应用配置

```env
SPRING_PROFILES_ACTIVE=prod
BACKEND_PORT=8080
FRONTEND_PORT=80
SERVER_IP=localhost
```

## 服务管理

### 查看服务状态

```bash
./deploy.sh status [dev|prod]
# 或
docker-compose ps
```

### 查看日志

```bash
# 查看所有服务日志
./deploy.sh logs [dev|prod]

# 查看特定服务日志
./deploy.sh logs dev backend
./deploy.sh logs prod frontend

# 或直接使用 docker-compose
docker-compose logs -f [service_name]
```

### 重启服务

```bash
# 重启所有服务
./deploy.sh restart [dev|prod]

# 重启特定服务
docker-compose restart [service_name]
```

### 停止服务

```bash
./deploy.sh stop
# 或
docker-compose down
```

## 访问地址

### 开发环境

- 前端应用: http://localhost
- 后端API: http://localhost:8080
- MinIO控制台: http://localhost:9001
- 数据库: localhost:5432
- Redis: localhost:6379

### 生产环境

- 前端应用: http://your-server-ip
- 后端API: http://your-server-ip:8080
- MinIO控制台: http://your-server-ip:9001

## 数据持久化

项目使用 Docker 卷来持久化数据：

- `postgres_data`: PostgreSQL 数据
- `redis_data`: Redis 数据
- `minio_data`: MinIO 对象存储数据
- `backend_logs`: 后端应用日志

## 备份与恢复

### 数据备份

```bash
# 使用部署脚本备份
./deploy.sh backup

# 手动备份数据库
docker-compose exec postgres pg_dump -U root zj_db > backup.sql

# 手动备份 MinIO 数据
docker run --rm -v ai-indicator-control-platform_minio_data:/data -v $(pwd):/backup alpine tar czf /backup/minio_backup.tar.gz -C /data .
```

### 数据恢复

```bash
# 恢复数据库
docker-compose exec -T postgres psql -U root zj_db < backup.sql

# 恢复 MinIO 数据
docker run --rm -v ai-indicator-control-platform_minio_data:/data -v $(pwd):/backup alpine tar xzf /backup/minio_backup.tar.gz -C /data
```

## 监控与健康检查

所有服务都配置了健康检查：

- **PostgreSQL**: 检查数据库连接
- **Redis**: 检查 Redis ping
- **MinIO**: 检查健康端点
- **Backend**: 检查 Spring Boot Actuator 健康端点
- **Frontend**: 检查 Nginx 响应

查看健康状态：

```bash
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
```

## 性能优化

### 生产环境优化

1. **数据库优化**: 已配置 PostgreSQL 性能参数
2. **Redis优化**: 配置内存限制和淘汰策略
3. **Nginx优化**: 启用 Gzip 压缩、静态资源缓存
4. **JVM优化**: 配置 G1GC 和内存参数

### 资源限制

生产环境已配置资源限制：

- PostgreSQL: 1GB 内存，1 CPU
- Redis: 512MB 内存，0.5 CPU
- MinIO: 1GB 内存，1 CPU
- Backend: 2GB 内存，2 CPU
- Frontend: 256MB 内存，0.5 CPU

## 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :8080
   # 修改 .env 文件中的端口配置
   ```

2. **内存不足**
   ```bash
   # 检查系统内存
   free -h
   # 调整 JVM 参数或减少服务资源限制
   ```

3. **数据库连接失败**
   ```bash
   # 检查数据库日志
   docker-compose logs postgres
   # 检查网络连接
   docker-compose exec backend ping postgres
   ```

4. **MinIO 访问问题**
   ```bash
   # 检查 MinIO 日志
   docker-compose logs minio
   # 重新初始化存储桶
   docker-compose exec minio mc mb myminio/images
   ```

### 日志分析

```bash
# 查看错误日志
docker-compose logs --tail=100 | grep ERROR

# 查看特定时间段的日志
docker-compose logs --since="2024-01-01T00:00:00" --until="2024-01-01T23:59:59"
```

## 安全配置

### 生产环境安全建议

1. **修改默认密码**: 更改数据库、Redis、MinIO 的默认密码
2. **网络隔离**: 使用防火墙限制端口访问
3. **SSL/TLS**: 配置 HTTPS 证书
4. **定期备份**: 设置自动备份计划
5. **监控告警**: 配置服务监控和告警

### 防火墙配置

```bash
# 开放必要端口
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 8080/tcp
sudo ufw allow 9001/tcp
```

## 更新升级

### 应用更新

```bash
# 拉取最新代码
git pull

# 重新构建并部署
./deploy.sh prod
```

### 镜像更新

```bash
# 拉取最新镜像
docker-compose pull

# 重新启动服务
docker-compose up -d
```

## 脚本工具

项目提供了多个便捷的脚本工具：

### 快速启动脚本
```bash
chmod +x quick-start.sh
./quick-start.sh
```
用于首次部署和快速启动，提供交互式菜单。

### 部署管理脚本
```bash
chmod +x deploy.sh
./deploy.sh [dev|prod|stop|restart|logs|status|backup]
```
完整的部署管理工具，支持开发和生产环境。

### 配置检查脚本
```bash
chmod +x check-config.sh
./check-config.sh
```
检查生产环境配置是否正确，包括环境变量、系统资源、Docker环境等。

### 监控脚本
```bash
chmod +x monitor.sh
./monitor.sh [status|resources|errors|full|watch]
```
监控服务健康状态、资源使用情况和错误日志。

## 部署流程建议

### 首次部署流程

1. **环境准备**
   ```bash
   # 检查系统要求
   ./check-config.sh docker

   # 配置环境变量
   cp .env.example .env
   # 编辑 .env 文件，修改密码和配置
   ```

2. **配置检查**
   ```bash
   # 执行完整配置检查
   ./check-config.sh
   ```

3. **部署服务**
   ```bash
   # 开发环境
   ./quick-start.sh  # 选择选项1

   # 或生产环境
   ./quick-start.sh  # 选择选项2
   ```

4. **验证部署**
   ```bash
   # 检查服务状态
   ./monitor.sh status

   # 查看日志
   ./deploy.sh logs
   ```

### 日常运维流程

1. **服务监控**
   ```bash
   # 持续监控模式
   ./monitor.sh watch

   # 检查资源使用
   ./monitor.sh resources
   ```

2. **日志管理**
   ```bash
   # 查看所有日志
   ./deploy.sh logs

   # 查看特定服务日志
   ./deploy.sh logs prod backend
   ```

3. **数据备份**
   ```bash
   # 执行备份
   ./deploy.sh backup
   ```

4. **服务重启**
   ```bash
   # 重启所有服务
   ./deploy.sh restart prod

   # 重启特定服务
   docker-compose restart backend
   ```

## 文件结构说明

```
ai-indicator-control-platform/
├── docker-compose.yml              # 主配置文件
├── docker-compose.prod.yml         # 生产环境覆盖配置
├── docker-compose.override.yml     # 开发环境覆盖配置
├── .env                            # 环境变量配置
├── .env.example                    # 环境变量模板
├── .dockerignore                   # Docker构建忽略文件
├── deploy.sh                       # 部署管理脚本
├── quick-start.sh                  # 快速启动脚本
├── monitor.sh                      # 监控脚本
├── check-config.sh                 # 配置检查脚本
├── README-Docker.md                # 部署文档
├── nginx/                          # Nginx配置目录
│   ├── nginx.conf                  # 通用配置
│   ├── nginx.dev.conf              # 开发环境配置
│   └── nginx.prod.conf             # 生产环境配置
├── init-scripts/                   # 初始化脚本目录
│   ├── 01-init.sql                 # 数据库初始化脚本
│   └── init-minio.sh               # MinIO初始化脚本
├── backend/                        # 后端代码和Dockerfile
├── frontend/                       # 前端代码和Dockerfile
├── logs/                           # 日志目录 (自动创建)
├── backups/                        # 备份目录 (自动创建)
├── config/                         # 配置目录 (自动创建)
└── data/                           # 数据目录 (自动创建)
    ├── postgres/
    ├── redis/
    └── minio/
```

## 联系支持

如有问题，请联系技术支持或查看项目文档。

### 常用命令速查

```bash
# 快速启动
./quick-start.sh

# 检查配置
./check-config.sh

# 部署生产环境
./deploy.sh prod

# 查看服务状态
./monitor.sh status

# 查看日志
./deploy.sh logs

# 备份数据
./deploy.sh backup

# 停止服务
./deploy.sh stop
```
