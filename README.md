# 阳江AI指标管控系统

## 1. 项目简介

本项目是为阳江烟草打造的基于AI大模型技术的财务指标管控系统。项目基于 BladeX 框架进行扩展开发，融合了自然语言处理、机器学习、智能分析等先进技术，旨在为企业提供智能化的财务数据分析和管控能力。

### 核心特性
- 🤖 **AI问数助手**：支持自然语言查询财务指标，多轮对话交互
- 📊 **智能分析**：基于AI的深度财务分析和异常检测
- 📈 **可视化报表**：动态图表展示，支持资产负债表、利润表等多维度分析  
- ⚠️ **实时预警**：智能异常检测和多渠道预警通知
- 🔐 **安全可控**：完善的权限管理和数据安全保障
- 🚀 **高性能**：大小模型结合，兼顾准确性和响应速度

这个 README 文件记录了项目的完整规划、架构设计和实施进展，是项目开发和维护的重要参考文档。

## 2. 项目架构

### 2.1 技术栈
- **后端**：Spring Boot 3.x + Spring Cloud + Spring AI + TypeScript + MyBatis-Plus + MySQL
- **前端**：Vue 3.x + TypeScript + Element Plus + ECharts + Avue
- **AI技术**：Spring AI智能体 + 自研AutoML平台 + 大模型(通义千问/GPT-4) + 小模型(XGBoost/LSTM)
- **数据存储**：MySQL + Redis + InfluxDB + MinIO + 向量数据库(Milvus)
- **消息队列**：RabbitMQ
- **搜索引擎**：Elasticsearch

### 2.2 系统架构
```·
前端展示层 (Vue.js + Element UI + ECharts)
         ↕
API网关层 (Spring Cloud Gateway)
         ↕
Spring AI智能体层 (问数智能体 + 分析智能体 + 管理智能体)
         ↕
业务服务层 (AI问数服务 + 指标管控服务 + 系统管理服务)
         ↕
AI能力层 (大模型服务 + 自研AutoML平台 + 向量数据库)
         ↕
数据存储层 (关系数据库 + 时序数据库 + 文件存储)
```

## 3. 核心功能模块

### 3.1 AI问数助手
- 自然语言查询财务指标
- 多轮对话上下文管理
- 智能意图识别和结果展示
- 查询历史记录管理

### 3.2 财务指标管控
- 多源数据导入和处理
- 费用分析和预测建模
- 资产负债表和利润表管理
- 税务数据管理和合规检查
- 深度分析报告生成
- 按指标类型分页查询和管理
- 指标数值统计分析功能

### 3.3 智能分析
- 基于机器学习的异常检测
- 实时预警和通知机制
- AI驱动的趋势分析
- 可视化报表和仪表板

### 3.4 系统管理
- 用户权限和角色管理
- 指标类型和元数据管理
- 大模型配置和监控
- 操作日志和审计

## 4. 开发计划

### 第一期：基础平台搭建 (4周)
- [x] 需求分析和架构设计
- [ ] 微服务框架搭建
- [ ] 数据库设计和初始化
- [ ] 基础权限管理完善

### 第二期：AI能力集成 (6周)
- [ ] Spring AI框架集成
- [ ] 三层智能体架构搭建
- [ ] Function Calling机制实现
- [ ] 自研AutoML平台开发
- [ ] 大模型接入和管理
- [ ] 自然语言查询功能

### 第三期：核心功能开发 (8周)
- [ ] 财务分析模块开发
- [ ] AutoML模型训练和部署
- [ ] 智能体协作流程优化
- [ ] 智能分析和预警功能
- [ ] 可视化报表系统
- [ ] 深度分析报告生成

### 第四期：系统完善 (4周)
- [ ] 系统集成测试
- [ ] 性能优化和调优
- [ ] 安全加固和部署
- [ ] 用户培训和交付

## 5. 如何运行项目

### 5.1 环境要求
- Java 17+
- Node.js 16+
- MySQL 8.0+
- Redis 7.x
- Python 3.8+ (AutoML平台)
- Milvus 2.x (向量数据库)
- Spring AI 1.0+ (智能体框架)

### 5.2 后端服务启动
```bash
cd backend
mvn clean install
mvn spring-boot:run
```

### 5.3 前端服务启动
```bash
cd frontend
npm install
npm run dev
```

### 5.4 AutoML平台启动
```bash
cd automl-platform
pip install -r requirements.txt
python automl_server.py
```

### 5.5 向量数据库启动
```bash
# 使用Docker启动Milvus
docker-compose up -d milvus-standalone
```

## 6. 项目文档

- 📋 [需求分析](设计/xuqiu.md) - 详细的功能需求和非功能需求
- 🏗️ [项目构建思路](设计/项目构建思路.md) - 完整的技术架构和实施方案
- 📐 [系统设计](设计/base.md) - 系统设计和技术细节
- 📊 [项目规划](设计/guihua.md) - 项目规划和里程碑

## 7. 技术特色

### 7.1 Spring AI智能体架构
- **三层智能体设计**：问数智能体、分析智能体、管理智能体分工协作
- **Function Calling机制**：智能体可直接调用Java业务方法
- **对话上下文管理**：内置记忆管理，支持复杂多轮对话
- **统一编程模型**：与Spring Boot深度集成，简化AI应用开发

### 7.2 自研AutoML平台
- **端到端自动化**：从数据预处理到模型部署全流程自动化
- **财务领域优化**：专门针对财务数据分析场景定制算法
- **多模型集成**：自动选择和组合最优模型，提升预测准确性
- **MLOps集成**：完整的模型生命周期管理和监控

### 7.3 大小模型结合
- **大模型**：处理复杂的自然语言理解和生成任务
- **小模型**：负责高频简单任务和实时计算
- **智能路由**：根据任务复杂度自动选择合适的模型
- **AutoML集成**：自动训练和优化小模型，持续提升性能

### 7.4 多层次异常检测
- 统计方法 + 机器学习 + 深度学习多算法融合
- 动态阈值调整和分级预警机制
- 业务规则与AI算法相结合
- AutoML自动优化检测模型

### 7.5 知识增强检索
- 构建财务领域知识图谱
- 基于RAG的智能问答
- 向量数据库支持语义检索
- Spring AI原生集成向量存储

## 8. 开发规范

### 8.1 代码规范
- 后端使用TypeScript，遵循ESLint规范
- 前端使用Vue 3 Composition API + TypeScript
- 统一使用Prettier进行代码格式化
- 所有接口必须添加详细注释

### 8.2 数据库规范
- 表名使用下划线命名法
- 字段名采用驼峰命名法
- 必须添加创建时间和更新时间字段
- 敏感数据必须加密存储

### 8.3 API规范
- 遵循RESTful设计原则
- 统一返回格式：`{code, data, message}`
- 使用标准HTTP状态码
- 接口版本化管理

## 9. 部署架构

### 9.1 开发环境
- 单机部署，所有服务在同一台机器
- 使用Docker Compose进行服务编排
- 支持热重载和调试

### 9.2 生产环境
- 微服务分布式部署
- Kubernetes容器化管理
- 负载均衡和自动扩缩容
- 完整的监控和日志收集

## 10. 风险控制

### 10.1 技术风险
- 大模型效果不达预期：准备多套备选方案
- 性能瓶颈：提前压测和性能调优
- 数据安全：多重加密和权限控制

### 10.2 项目风险
- 需求变更：采用敏捷开发方法
- 进度延期：关键路径监控和预警
- 人员风险：知识文档化和交接机制

## 11. 联系方式

- 项目负责人：[待确定]
- 技术负责人：[待确定]
- 产品负责人：[待确定]

---

**项目状态**：✅ 需求分析完成，架构设计完成，准备进入开发阶段

本项目致力于为阳江烟草提供一流的AI驱动财务指标管控解决方案，通过技术创新驱动业务价值。 