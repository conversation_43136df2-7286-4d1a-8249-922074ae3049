/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.yjzb.mapper;

import org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity;
import org.springblade.modules.yjzb.pojo.vo.IndicatorValuesVO;
import org.springblade.modules.yjzb.excel.IndicatorValuesExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

/**
 * 指标数据 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public interface IndicatorValuesMapper extends BaseMapper<IndicatorValuesEntity> {

    /**
     * 自定义分页
     *
     * @param page            分页参数
     * @param indicatorValues 查询参数
     * @return List<IndicatorValuesVO>
     */
    List<IndicatorValuesVO> selectIndicatorValuesPage(IPage<IndicatorValuesVO> page, IndicatorValuesVO indicatorValues);

    /**
     * 获取导出数据
     *
     * @param queryWrapper 查询条件
     * @return List<IndicatorValuesExcel>
     */
    List<IndicatorValuesExcel> exportIndicatorValues(@Param("ew") Wrapper<IndicatorValuesEntity> queryWrapper);

    /**
     * 根据指标类型分页查询指标数据
     *
     * @param page            分页参数
     * @param indicatorTypeId 指标类型ID
     * @param period          数据期间（可选）
     * @param dataSource      数据来源（可选）
     * @return List<IndicatorValuesVO>
     */
    List<IndicatorValuesVO> selectIndicatorValuesByType(IPage<IndicatorValuesVO> page,
            @Param("indicatorTypeId") Long indicatorTypeId,
            @Param("period") String period,
            @Param("dataSource") String dataSource);

    /**
     * 统计指定月份和指标类型的指标数值
     *
     * @param period          数据期间（格式：YYYY-MM）
     * @param indicatorTypeId 指标类型ID
     * @return 统计结果（总数、总金额、平均值等）
     */
    Map<String, Object> getIndicatorStatistics(@Param("period") String period,
            @Param("indicatorTypeId") Long indicatorTypeId);

}
