/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 */
package org.springblade.modules.yjzb.service.impl;

import org.springblade.modules.yjzb.pojo.entity.IndicatorAiAnalysisEntity;
import org.springblade.modules.yjzb.mapper.IndicatorAiAnalysisMapper;
import org.springblade.modules.yjzb.service.IIndicatorAiAnalysisService;
import org.springblade.modules.yjzb.service.IDifyService;
import org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity;
import org.springblade.modules.yjzb.pojo.entity.IndicatorEntity;
import org.springblade.modules.yjzb.service.IIndicatorValuesService;
import org.springblade.modules.yjzb.service.IIndicatorService;
import org.springblade.modules.yjzb.service.IIndicatorAnnualBudgetService;
import org.springblade.modules.yjzb.pojo.entity.IndicatorAnnualBudgetEntity;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springblade.core.mp.base.BaseServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.yjzb.pojo.entity.IndicatorTypesEntity;
import org.springblade.modules.yjzb.service.IIndicatorTypesService;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 指标AI解读分析 服务实现类
 *
 * <AUTHOR> Assistant
 * @since 2025-08-10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IndicatorAiAnalysisServiceImpl
        extends BaseServiceImpl<IndicatorAiAnalysisMapper, IndicatorAiAnalysisEntity>
        implements IIndicatorAiAnalysisService {

    private final IIndicatorValuesService indicatorValuesService;
    private final IIndicatorService indicatorService;
    private final IIndicatorAnnualBudgetService indicatorAnnualBudgetService;
    private final org.springblade.modules.yjzb.service.IIndicatorForecastService indicatorForecastService;
    private final IIndicatorTypesService indicatorTypesService;
    @Value("${dify.api.agentkey.totalanalysisagent:}")
    private String totalAnalysisApiKey;
    private final IDifyService difyService;
    private final ObjectMapper objectMapper;

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    @Value("${dify.api.agentkey.analysisagent:}")
    private String difyApiKey;

    @Override
    public Long restartAnalysisForIndicatorValue(Long indicatorValueId) {
        IndicatorValuesEntity indicatorValue = indicatorValuesService.getById(indicatorValueId);
        if (indicatorValue == null) {
            log.error("未找到指标数据，id={}", indicatorValueId);
            return null;
        }

        // 1) 组装输入参数：以指标数值为核心，可扩展period等上下文
        Map<String, Object> inputs = new HashMap<>();
        BigDecimal value = indicatorValue.getValue();
        inputs.put("value", value == null ? null : indicatorValue.getValue() + "");
        String period = indicatorValue.getPeriod();
        inputs.put("period", period);
        inputs.put("indicatorId", indicatorValue.getIndicatorId() + "");

        // 附加：指标基础信息（名称、单位）
        try {
            IndicatorEntity indicator = indicatorService.getById(indicatorValue.getIndicatorId());
            if (indicator != null) {
                inputs.put("zhibiao", indicator.getName());
                inputs.put("unit", indicator.getUnit());
                inputs.put("year", period.substring(0, 4));
                inputs.put("month", period.substring(5, 7));
            }
        } catch (Exception ignore) {
        }

        // 附加：zhibiaodata - 最近两年(月度)指标数值 + 年度预算信息（CSV文本）
        try {
            String csv = buildTwoYearsCsv(indicatorValue.getIndicatorId(), period);
            inputs.put("zhibiaodata", csv);
        } catch (Exception e) {
            log.warn("构建两年CSV失败, indicatorId={}, period={}, err= {}", indicatorValue.getIndicatorId(), period,
                    e.getMessage());
        }

        // 附加：小模型预测值 与 年度累计预测结果（实际YTD + 预测剩余月份）
        try {
            java.math.BigDecimal mlPred = indicatorForecastService
                    .predictByIndicatorAndPeriod(indicatorValue.getIndicatorId(), period);
            inputs.put("ml_predicted_value", mlPred == null ? null : mlPred.toPlainString());

            // 计算年度累计预测
            String yearStr = period.substring(0, 4);
            int year = Integer.parseInt(yearStr);
            int month = Integer.parseInt(period.substring(5, 7));
            java.math.BigDecimal ytdActual = java.math.BigDecimal.ZERO;
            for (int m = 1; m <= month; m++) {
                String p = year + "-" + String.format("%02d", m);
                java.math.BigDecimal v = getActualValue(indicatorValue.getIndicatorId(), p);
                if (v != null)
                    ytdActual = ytdActual.add(v);
            }
            java.math.BigDecimal remainPred = java.math.BigDecimal.ZERO;
            for (int m = month + 1; m <= 12; m++) {
                String p = year + "-" + String.format("%02d", m);
                java.math.BigDecimal v = indicatorForecastService
                        .predictByIndicatorAndPeriod(indicatorValue.getIndicatorId(), p);
                if (v != null)
                    remainPred = remainPred.add(v);
            }
            java.math.BigDecimal annualForecast = ytdActual.add(remainPred);
            inputs.put("annual_forecast_cumulative", annualForecast.toPlainString());
        } catch (Exception e) {
            log.warn("附加小模型预测或年度累计预测失败, indicatorId={}, period={}, err= {}",
                    indicatorValue.getIndicatorId(), period, e.getMessage());
        }

        // 2) 启动Dify工作流（streaming，只取第一个事件拿到workflow_run_id）
        // 读取API-KEY：从配置或数据库。这里沿用DifyServiceImpl内部已读的配置；接口需要显式传入。
        // 为复用现有签名，这里从环境变量或系统属性读取；若为空将回退到application配置。
        String apiKey = (difyApiKey == null || difyApiKey.isBlank())
                ? System.getProperty("dify.api.key", System.getenv("DIFY_API_KEY"))
                : difyApiKey;
        String workflowRunId = difyService.startWorkflowStreaming(inputs, "yjzb", apiKey);
        log.info("启动AI分析：indicatorValueId={}, indicatorId={}, period={}, workflowRunId={}", indicatorValueId,
                indicatorValue.getIndicatorId(), indicatorValue.getPeriod(), workflowRunId);
        if (workflowRunId == null) {
            log.error("启动Dify工作流失败，indicatorValueId={}，请检查 Dify 配置与权限", indicatorValueId);
            return null;
        }

        // 3) 写入AI解读分析表（RUNNING）
        IndicatorAiAnalysisEntity ai = new IndicatorAiAnalysisEntity();
        ai.setIndicatorId(indicatorValue.getIndicatorId());
        ai.setPeriod(indicatorValue.getPeriod());
        try {
            ai.setInputParams(objectMapper.writeValueAsString(inputs));
        } catch (Exception e) {
            ai.setInputParams(String.valueOf(inputs));
        }
        ai.setExecuteTime(new Date());
        ai.setExecuteStatus("RUNNING");
        ai.setWorkflowRunId(workflowRunId);
        save(ai);

        Long aiId = ai.getId();

        // 4) 启动后台轮询任务：每3分钟查询一次执行结果
        log.info("[AI分析-轮询] 启动轮询任务: aiId={}, workflowRunId={}, period={}, indicatorId={}, 间隔=3分钟",
                aiId, workflowRunId, indicatorValue.getPeriod(), indicatorValue.getIndicatorId());
        final String finalApiKey = apiKey;
        final long aiRecordIdFinal = aiId;
        final String workflowRunIdFinal = workflowRunId;
        final AtomicReference<ScheduledFuture<?>> futureRef = new AtomicReference<>();
        ScheduledFuture<?> scheduled = scheduler.scheduleAtFixedRate(() -> {
            try {
                log.debug("[AI分析-轮询] tick: aiId={}, workflowRunId={}", aiRecordIdFinal, workflowRunIdFinal);
                IndicatorAiAnalysisEntity current = getById(aiRecordIdFinal);
                if (current == null || !"RUNNING".equalsIgnoreCase(current.getExecuteStatus())) {
                    // 已非运行状态，结束轮询
                    ScheduledFuture<?> f = futureRef.get();
                    if (f != null) {
                        f.cancel(false);
                    }
                    log.info("[AI分析-轮询] 停止轮询: aiId={}, 当前状态={}", aiRecordIdFinal,
                            current == null ? "null" : current.getExecuteStatus());
                    return;
                }
                String detail = difyService.getWorkflowRunDetail(workflowRunIdFinal, finalApiKey);
                if (detail == null || detail.isBlank()) {
                    log.warn("[AI分析-轮询] 未获取到工作流详情: aiId={}, workflowRunId={}", aiRecordIdFinal,
                            workflowRunIdFinal);
                    return;
                }
                // 从返回内容中提取 status（兼容 data 为 JSON 字符串的情况）
                String status = extractStatus(detail);
                String lower = status == null ? null : status.toLowerCase();
                log.info("[AI分析-轮询] 查询结果: aiId={}, workflowRunId={}, status={}", aiRecordIdFinal,
                        workflowRunIdFinal, lower);
                if ("succeeded".equals(lower) || "completed".equals(lower)) {
                    // 仅存 outputs.text，前端直接展示；避免大量转义与嵌套JSON
                    String outputText = extractOutputText(detail);
                    current.setExecuteStatus("COMPLETED");
                    current.setResult(outputText != null ? outputText : detail);
                    updateById(current);
                    ScheduledFuture<?> f = futureRef.get();
                    if (f != null) {
                        f.cancel(false);
                    }
                    log.info("[AI分析-轮询] 已完成: aiId={}, workflowRunId={}", aiRecordIdFinal, workflowRunIdFinal);
                } else if ("failed".equals(lower) || "error".equals(lower)) {
                    current.setExecuteStatus("FAILED");
                    current.setResult(detail);
                    updateById(current);
                    ScheduledFuture<?> f = futureRef.get();
                    if (f != null) {
                        f.cancel(false);
                    }
                    log.info("[AI分析-轮询] 已失败: aiId={}, workflowRunId={}", aiRecordIdFinal, workflowRunIdFinal);
                }
            } catch (Exception ex) {
                log.error("轮询Dify工作流结果异常, workflowRunId={}", workflowRunIdFinal, ex);
            }
        }, 0, 10, TimeUnit.SECONDS);
        futureRef.set(scheduled);

        return aiId;
    }

    private java.math.BigDecimal getActualValue(Long indicatorId, String period) {
        try {
            org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity q = new org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity();
            q.setIndicatorId(indicatorId);
            q.setPeriod(period);
            org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity e = indicatorValuesService
                    .getOne(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>(q));
            return e == null ? null : e.getValue();
        } catch (Exception ignore) {
            return null;
        }
    }

    /**
     * 提取工作流状态字段，兼容以下返回结构：
     * 1) { status: "succeeded" }
     * 2) { data: { status: "succeeded" } }
     * 3) { data: "{\"status\":\"succeeded\", ... }" }
     */
    private String extractStatus(String detailJson) {
        try {
            com.fasterxml.jackson.databind.JsonNode node = objectMapper.readTree(detailJson);
            if (node == null)
                return null;
            // 直接在根查找
            if (node.has("status")) {
                return node.get("status").asText();
            }
            // 在 data 节点查找
            if (node.has("data")) {
                com.fasterxml.jackson.databind.JsonNode data = node.get("data");
                if (data.isObject() && data.has("status")) {
                    return data.get("status").asText();
                }
                if (data.isTextual()) {
                    // data 是一个 JSON 字符串，再次反序列化
                    String dataText = data.asText();
                    com.fasterxml.jackson.databind.JsonNode dataObj = objectMapper.readTree(dataText);
                    if (dataObj != null && dataObj.has("status")) {
                        return dataObj.get("status").asText();
                    }
                }
            }
        } catch (Exception ignore) {
        }
        return null;
    }

    /**
     * 提取 outputs.text 内容；若 outputs 为字符串JSON，会先反序列化再取 text
     */
    private String extractOutputText(String detailJson) {
        try {
            com.fasterxml.jackson.databind.JsonNode node = objectMapper.readTree(detailJson);
            if (node == null)
                return null;
            com.fasterxml.jackson.databind.JsonNode outputs = node.get("outputs");
            if (outputs == null)
                return null;
            if (outputs.isObject()) {
                com.fasterxml.jackson.databind.JsonNode text = outputs.get("text");
                return text != null ? text.asText() : outputs.toString();
            }
            if (outputs.isTextual()) {
                String outputsText = outputs.asText();
                com.fasterxml.jackson.databind.JsonNode outObj = objectMapper.readTree(outputsText);
                if (outObj != null && outObj.has("text")) {
                    return outObj.get("text").asText();
                }
                return outputsText;
            }
        } catch (Exception ignore) {
        }
        return null;
    }

    /**
     * 构建最近两年该指标的月度数据 + 年度预算信息，输出为CSV文本。
     * 字段：year,month,period,indicator_id,indicator_name,value,unit,annual_budget,annual_reported,annual_used
     */
    private String buildTwoYearsCsv(Long indicatorId, String currentPeriod) {
        StringBuilder sb = new StringBuilder();
        sb.append(
                "year,month,period,indicator_id,indicator_name,value,unit,annual_budget,annual_reported,annual_used\n");
        try {
            // 解析当前 period
            int year = 0, month = 0;
            if (currentPeriod != null && currentPeriod.matches("\\d{4}-\\d{2}")) {
                year = Integer.parseInt(currentPeriod.substring(0, 4));
                month = Integer.parseInt(currentPeriod.substring(5, 7));
            } else {
                java.time.LocalDate now = java.time.LocalDate.now();
                year = now.getYear();
                month = now.getMonthValue();
            }

            // 准备指标基础信息
            String indicatorName = "";
            String unit = "";
            try {
                IndicatorEntity ind = indicatorService.getById(indicatorId);
                if (ind != null) {
                    indicatorName = ind.getName() == null ? "" : ind.getName();
                    unit = ind.getUnit() == null ? "" : ind.getUnit();
                }
            } catch (Exception ignore) {
            }

            // 生成最近24个月period列表（含当前月，倒序到24个月前）
            java.util.List<String> periods = new java.util.ArrayList<>();
            int y = year, m = month;
            for (int i = 0; i < 24; i++) {
                periods.add(String.format("%04d-%02d", y, m));
                m -= 1;
                if (m <= 0) {
                    m = 12;
                    y -= 1;
                }
            }
            java.util.Collections.reverse(periods); // 从旧到新

            // 准备年度预算查找缓存：year -> budget entity
            java.util.Map<Integer, IndicatorAnnualBudgetEntity> budgetCache = new java.util.HashMap<>();

            for (String p : periods) {
                int py = Integer.parseInt(p.substring(0, 4));
                // 获取该年的年度预算
                IndicatorAnnualBudgetEntity budget = budgetCache.computeIfAbsent(py, k -> {
                    try {
                        return indicatorAnnualBudgetService.findByIndicatorAndYear(indicatorId, k);
                    } catch (Exception e) {
                        return null;
                    }
                });
                String annualBudget = "";
                String annualReported = "";
                String annualUsed = "";
                if (budget != null) {
                    java.math.BigDecimal mid = budget.getMidyearBudget();
                    java.math.BigDecimal initB = budget.getInitialBudget();
                    java.math.BigDecimal used = budget.getCurrentUsed();
                    java.math.BigDecimal reportedInit = budget.getInitialReported();
                    java.math.BigDecimal budgetValue = (mid != null && mid.signum() != 0) ? mid : initB;
                    annualBudget = budgetValue == null ? "" : budgetValue.toPlainString();
                    annualReported = reportedInit == null ? "" : reportedInit.toPlainString();
                    annualUsed = used == null ? "" : used.toPlainString();
                }

                // 查询该月的指标值
                String valStr = "";
                try {
                    // 直接通过现有服务分页接口不方便，这里简单走Mapper条件查询
                    // 但为保持低侵入性，这里调用 service 的 lambdaQuery
                    IndicatorValuesEntity one = indicatorValuesService.lambdaQuery()
                            .eq(IndicatorValuesEntity::getIndicatorId, indicatorId)
                            .eq(IndicatorValuesEntity::getPeriod, p)
                            .last("limit 1")
                            .one();
                    if (one != null && one.getValue() != null) {
                        valStr = one.getValue().toPlainString();
                    }
                } catch (Exception ignore) {
                }

                sb.append(py).append(',')
                        .append(p.substring(5, 7)).append(',')
                        .append(p).append(',')
                        .append(indicatorId).append(',')
                        .append(escapeCsv(indicatorName)).append(',')
                        .append(valStr).append(',')
                        .append(escapeCsv(unit)).append(',')
                        .append(annualBudget).append(',')
                        .append(annualReported).append(',')
                        .append(annualUsed)
                        .append('\n');
            }
        } catch (Exception e) {
            log.warn("构建两年CSV异常: {}", e.getMessage());
        }
        return sb.toString();
    }

    private String escapeCsv(String s) {
        if (s == null)
            return "";
        if (s.contains(",") || s.contains("\"") || s.contains("\n")) {
            return '"' + s.replace("\"", "\"\"") + '"';
        }
        return s;
    }

    @Override
    public IndicatorAiAnalysisEntity getLatestByIndicatorValueId(Long indicatorValueId) {
        IndicatorValuesEntity indicatorValue = indicatorValuesService.getById(indicatorValueId);
        if (indicatorValue == null) {
            return null;
        }
        // 按 indicatorId + period 倒序取最新一条
        return lambdaQuery()
                .eq(IndicatorAiAnalysisEntity::getIndicatorId, indicatorValue.getIndicatorId())
                .eq(IndicatorAiAnalysisEntity::getPeriod, indicatorValue.getPeriod())
                .orderByDesc(IndicatorAiAnalysisEntity::getCreateTime)
                .last("limit 1")
                .one();
    }

    @Override
    public Long restartTypeMonthlyAnalysis(Long indicatorTypeId, String period) {
        try {
            // 1) 聚合同类型当月数据
            var records = indicatorValuesService.listByTypeAndPeriod(indicatorTypeId, period);
            StringBuilder csv = new StringBuilder();
            csv.append("indicator_id,indicator_name,period,value\n");
            java.math.BigDecimal monthlyTotal = java.math.BigDecimal.ZERO;
            for (var v : records) {
                String val = v.getValue() == null ? "" : v.getValue().toString();
                if (v.getValue() != null)
                    monthlyTotal = monthlyTotal.add(new java.math.BigDecimal(val));
                csv.append(v.getIndicatorId()).append(',')
                        .append('"')
                        .append((v.getIndicatorName() == null ? "" : v.getIndicatorName()).replace("\"", "\"\""))
                        .append('"').append(',')
                        .append(period).append(',')
                        .append(val)
                        .append('\n');
            }

            // 2) 计算当年累计
            String year = period.substring(0, 4);
            java.math.BigDecimal yearBudget = java.math.BigDecimal.ZERO;
            for (int m = 1; m <= 12; m++) {
                String pm = String.format("%s-%02d", year, m);
                var listM = indicatorValuesService.listByTypeAndPeriod(indicatorTypeId, pm);
                for (var v : listM) {
                    if (v.getValue() != null)
                        yearBudget = yearBudget.add(new java.math.BigDecimal(v.getValue().toString()));
                }
            }

            // 3) 组装inputs并调用Dify(streaming，仅获取workflow_run_id)
            java.util.Map<String, Object> inputs = new java.util.HashMap<>();
            IndicatorTypesEntity indicatorType = indicatorTypesService.getById(indicatorTypeId);
            if (indicatorType != null) {
                inputs.put("indicatorTypeName", indicatorType.getTypeName());
            }
            inputs.put("period", period);
            inputs.put("monthlyTotal", monthlyTotal.toPlainString());
            inputs.put("yearBudget", yearBudget.toPlainString());
            inputs.put("detailData", csv.toString());

            String apiKey = (totalAnalysisApiKey == null || totalAnalysisApiKey.isBlank()) ? null : totalAnalysisApiKey;
            String workflowRunId = difyService.startWorkflowStreaming(inputs, "yjzb", apiKey);
            log.info("启动类型总体AI解读：indicatorTypeId={}, period={}, workflowRunId={}", indicatorTypeId, period,
                    workflowRunId);
            if (workflowRunId == null) {
                log.error("类型总体AI解读启动失败, indicatorTypeId={}, period={}", indicatorTypeId, period);
                return null;
            }

            // 4) 存储RUNNING记录（indicatorId=indicatorTypeId）
            IndicatorAiAnalysisEntity ai = new IndicatorAiAnalysisEntity();
            ai.setIndicatorId(indicatorTypeId);
            ai.setPeriod(period);
            ai.setInputParams("{\"scope\":\"type-monthly\"}");
            ai.setExecuteTime(new java.util.Date());
            ai.setExecuteStatus("RUNNING");
            ai.setWorkflowRunId(workflowRunId);
            save(ai);

            Long aiId = ai.getId();

            // 5) 启动轮询，与单条一致（当前为每10秒）
            final String finalApiKey = apiKey;
            final long aiRecordIdFinal = aiId;
            final String workflowRunIdFinal = workflowRunId;
            final AtomicReference<ScheduledFuture<?>> futureRef = new AtomicReference<>();
            ScheduledFuture<?> scheduled = scheduler.scheduleAtFixedRate(() -> {
                try {
                    log.debug("[AI类型-轮询] tick: aiId={}, workflowRunId={}", aiRecordIdFinal, workflowRunIdFinal);
                    IndicatorAiAnalysisEntity current = getById(aiRecordIdFinal);
                    if (current == null || !"RUNNING".equalsIgnoreCase(current.getExecuteStatus())) {
                        ScheduledFuture<?> f = futureRef.get();
                        if (f != null)
                            f.cancel(false);
                        log.info("[AI类型-轮询] 停止轮询: aiId={}, 当前状态={}", aiRecordIdFinal,
                                current == null ? "null" : current.getExecuteStatus());
                        return;
                    }
                    String detail = difyService.getWorkflowRunDetail(workflowRunIdFinal, finalApiKey);
                    if (detail == null || detail.isBlank()) {
                        log.warn("[AI类型-轮询] 未获取到工作流详情: aiId={}, workflowRunId={}", aiRecordIdFinal, workflowRunIdFinal);
                        return;
                    }
                    String status = extractStatus(detail);
                    String lower = status == null ? null : status.toLowerCase();
                    log.info("[AI类型-轮询] 查询结果: aiId={}, workflowRunId={}, status={}", aiRecordIdFinal,
                            workflowRunIdFinal, lower);
                    if ("succeeded".equals(lower) || "completed".equals(lower)) {
                        String outputText = extractOutputText(detail);
                        current.setExecuteStatus("COMPLETED");
                        current.setResult(outputText != null ? outputText : detail);
                        updateById(current);
                        ScheduledFuture<?> f = futureRef.get();
                        if (f != null)
                            f.cancel(false);
                        log.info("[AI类型-轮询] 已完成: aiId={}, workflowRunId={}", aiRecordIdFinal, workflowRunIdFinal);
                    } else if ("failed".equals(lower) || "error".equals(lower)) {
                        current.setExecuteStatus("FAILED");
                        current.setResult(detail);
                        updateById(current);
                        ScheduledFuture<?> f = futureRef.get();
                        if (f != null)
                            f.cancel(false);
                        log.info("[AI类型-轮询] 已失败: aiId={}, workflowRunId={}", aiRecordIdFinal, workflowRunIdFinal);
                    }
                } catch (Exception ex) {
                    log.error("[AI类型-轮询] 异常, workflowRunId={}", workflowRunIdFinal, ex);
                }
            }, 0, 10, TimeUnit.SECONDS);
            futureRef.set(scheduled);

            return aiId;
        } catch (Exception e) {
            log.error("类型总体AI解读异常, indicatorTypeId={}, period={}", indicatorTypeId, period, e);
            return null;
        }
    }

    @Override
    public IndicatorAiAnalysisEntity getLatestByIndicatorAndPeriod(Long indicatorId, String period) {
        return lambdaQuery()
                .eq(IndicatorAiAnalysisEntity::getIndicatorId, indicatorId)
                .eq(IndicatorAiAnalysisEntity::getPeriod, period)
                .orderByDesc(IndicatorAiAnalysisEntity::getCreateTime)
                .last("limit 1")
                .one();
    }
}
