/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.yjzb.service.impl;

import org.springblade.modules.yjzb.pojo.entity.IndicatorValuesEntity;
import org.springblade.modules.yjzb.pojo.vo.IndicatorValuesVO;
import org.springblade.modules.yjzb.excel.IndicatorValuesExcel;
import org.springblade.modules.yjzb.mapper.IndicatorValuesMapper;
import org.springblade.modules.yjzb.service.IIndicatorValuesService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;
import java.util.Map;

/**
 * 指标数据 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Service
public class IndicatorValuesServiceImpl extends BaseServiceImpl<IndicatorValuesMapper, IndicatorValuesEntity>
        implements IIndicatorValuesService {

    @Override
    public IPage<IndicatorValuesVO> selectIndicatorValuesPage(IPage<IndicatorValuesVO> page,
            IndicatorValuesVO indicatorValues) {
        return page.setRecords(baseMapper.selectIndicatorValuesPage(page, indicatorValues));
    }

    @Override
    public List<IndicatorValuesExcel> exportIndicatorValues(Wrapper<IndicatorValuesEntity> queryWrapper) {
        List<IndicatorValuesExcel> indicatorValuesList = baseMapper.exportIndicatorValues(queryWrapper);
        // indicatorValuesList.forEach(indicatorValues -> {
        // indicatorValues.setTypeName(DictCache.getValue(DictEnum.YES_NO,
        // IndicatorValues.getType()));
        // });
        return indicatorValuesList;
    }

    @Override
    public IPage<IndicatorValuesVO> selectIndicatorValuesByType(IPage<IndicatorValuesVO> page, Long indicatorTypeId,
            String period, String dataSource) {
        return page.setRecords(baseMapper.selectIndicatorValuesByType(page, indicatorTypeId, period, dataSource));
    }

    @Override
    public Map<String, Object> getIndicatorStatistics(String period, Long indicatorTypeId) {
        return baseMapper.getIndicatorStatistics(period, indicatorTypeId);
    }

    @Override
    public java.util.List<org.springblade.modules.yjzb.pojo.vo.IndicatorValuesVO> listByTypeAndPeriod(
            Long indicatorTypeId, String period) {
        // 直接复用已存在的 Mapper 自定义查询（若无则简单分页取大页）
        com.baomidou.mybatisplus.core.metadata.IPage<org.springblade.modules.yjzb.pojo.vo.IndicatorValuesVO> page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(
                1, 10000);
        return baseMapper.selectIndicatorValuesByType(page, indicatorTypeId, period, null);
    }

}
