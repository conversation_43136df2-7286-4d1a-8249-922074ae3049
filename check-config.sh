#!/bin/bash

# AI指标管控平台配置检查脚本
# 用于检查生产环境配置是否正确

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查结果统计
CHECKS_PASSED=0
CHECKS_FAILED=0
CHECKS_WARNING=0

# 日志函数
log_pass() {
    echo -e "${GREEN}✓ $1${NC}"
    ((CHECKS_PASSED++))
}

log_fail() {
    echo -e "${RED}✗ $1${NC}"
    ((CHECKS_FAILED++))
}

log_warn() {
    echo -e "${YELLOW}⚠ $1${NC}"
    ((CHECKS_WARNING++))
}

log_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# 检查文件是否存在
check_file_exists() {
    local file=$1
    local description=$2
    
    if [ -f "$file" ]; then
        log_pass "$description 存在"
        return 0
    else
        log_fail "$description 不存在: $file"
        return 1
    fi
}

# 检查环境变量
check_env_var() {
    local var_name=$1
    local description=$2
    local is_required=${3:-true}
    
    if [ -f .env ]; then
        local value=$(grep "^$var_name=" .env | cut -d'=' -f2- | tr -d '"' | tr -d "'")
        
        if [ -n "$value" ] && [ "$value" != "请修改为安全密码" ] && [ "$value" != "请配置实际的API密钥" ] && [ "$value" != "请设置Redis密码" ]; then
            log_pass "$description 已配置"
            return 0
        else
            if [ "$is_required" = "true" ]; then
                log_fail "$description 未正确配置: $var_name"
                return 1
            else
                log_warn "$description 未配置 (可选): $var_name"
                return 2
            fi
        fi
    else
        log_fail ".env 文件不存在"
        return 1
    fi
}

# 检查密码强度
check_password_strength() {
    local var_name=$1
    local description=$2
    
    if [ -f .env ]; then
        local password=$(grep "^$var_name=" .env | cut -d'=' -f2- | tr -d '"' | tr -d "'")
        
        if [ ${#password} -ge 8 ]; then
            log_pass "$description 密码长度符合要求 (≥8位)"
        else
            log_warn "$description 密码长度不足 (<8位)"
        fi
        
        if [[ "$password" =~ [A-Z] ]] && [[ "$password" =~ [a-z] ]] && [[ "$password" =~ [0-9] ]]; then
            log_pass "$description 密码复杂度符合要求"
        else
            log_warn "$description 密码复杂度不足 (建议包含大小写字母和数字)"
        fi
    fi
}

# 检查端口冲突
check_port_conflict() {
    local port=$1
    local service=$2
    
    if command -v netstat >/dev/null 2>&1; then
        if netstat -tuln | grep -q ":$port "; then
            log_warn "$service 端口 $port 可能被占用"
            return 1
        else
            log_pass "$service 端口 $port 可用"
            return 0
        fi
    else
        log_info "无法检查端口冲突 (netstat 未安装)"
        return 0
    fi
}

# 检查系统资源
check_system_resources() {
    log_info "检查系统资源..."
    
    # 检查内存
    if command -v free >/dev/null 2>&1; then
        local total_mem=$(free -m | awk 'NR==2{printf "%.0f", $2}')
        if [ "$total_mem" -ge 4096 ]; then
            log_pass "系统内存充足 (${total_mem}MB ≥ 4GB)"
        elif [ "$total_mem" -ge 2048 ]; then
            log_warn "系统内存较少 (${total_mem}MB, 建议 ≥ 4GB)"
        else
            log_fail "系统内存不足 (${total_mem}MB < 2GB)"
        fi
    fi
    
    # 检查磁盘空间
    if command -v df >/dev/null 2>&1; then
        local available_space=$(df . | awk 'NR==2 {print $4}')
        local available_gb=$((available_space / 1024 / 1024))
        
        if [ "$available_gb" -ge 10 ]; then
            log_pass "磁盘空间充足 (${available_gb}GB ≥ 10GB)"
        elif [ "$available_gb" -ge 5 ]; then
            log_warn "磁盘空间较少 (${available_gb}GB, 建议 ≥ 10GB)"
        else
            log_fail "磁盘空间不足 (${available_gb}GB < 5GB)"
        fi
    fi
}

# 检查 Docker 环境
check_docker_environment() {
    log_info "检查 Docker 环境..."
    
    if command -v docker >/dev/null 2>&1; then
        log_pass "Docker 已安装"
        
        # 检查 Docker 版本
        local docker_version=$(docker --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
        log_info "Docker 版本: $docker_version"
        
        # 检查 Docker 服务状态
        if docker info >/dev/null 2>&1; then
            log_pass "Docker 服务运行正常"
        else
            log_fail "Docker 服务未运行"
        fi
    else
        log_fail "Docker 未安装"
    fi
    
    if command -v docker-compose >/dev/null 2>&1; then
        log_pass "Docker Compose 已安装"
        
        # 检查 Docker Compose 版本
        local compose_version=$(docker-compose --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
        log_info "Docker Compose 版本: $compose_version"
    else
        log_fail "Docker Compose 未安装"
    fi
}

# 检查网络连接
check_network_connectivity() {
    log_info "检查网络连接..."
    
    # 检查外网连接
    if ping -c 1 8.8.8.8 >/dev/null 2>&1; then
        log_pass "外网连接正常"
    else
        log_warn "外网连接异常 (可能影响镜像下载)"
    fi
    
    # 检查 Docker Hub 连接
    if ping -c 1 registry-1.docker.io >/dev/null 2>&1; then
        log_pass "Docker Hub 连接正常"
    else
        log_warn "Docker Hub 连接异常 (可能影响镜像下载)"
    fi
}

# 主检查函数
main_check() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "      AI指标管控平台配置检查"
    echo "=================================================="
    echo -e "${NC}"
    
    # 检查必要文件
    log_info "检查必要文件..."
    check_file_exists "docker-compose.yml" "Docker Compose 主配置文件"
    check_file_exists "docker-compose.prod.yml" "生产环境配置文件"
    check_file_exists ".env" "环境变量配置文件"
    check_file_exists "backend/Dockerfile" "后端 Dockerfile"
    check_file_exists "frontend/Dockerfile" "前端 Dockerfile"
    
    echo ""
    
    # 检查环境变量配置
    log_info "检查环境变量配置..."
    check_env_var "POSTGRES_PASSWORD" "数据库密码"
    check_env_var "MINIO_ROOT_PASSWORD" "MinIO 密码"
    check_env_var "REDIS_PASSWORD" "Redis 密码" false
    check_env_var "SERVER_IP" "服务器IP地址"
    check_env_var "SPRING_AI_OPENAI_API_KEY" "AI API密钥" false
    
    echo ""
    
    # 检查密码强度
    log_info "检查密码强度..."
    check_password_strength "POSTGRES_PASSWORD" "数据库"
    check_password_strength "MINIO_ROOT_PASSWORD" "MinIO"
    
    echo ""
    
    # 检查端口冲突
    log_info "检查端口冲突..."
    check_port_conflict 80 "前端服务"
    check_port_conflict 8080 "后端服务"
    check_port_conflict 5432 "PostgreSQL"
    check_port_conflict 6379 "Redis"
    check_port_conflict 9000 "MinIO API"
    check_port_conflict 9001 "MinIO 控制台"
    
    echo ""
    
    # 检查系统资源
    check_system_resources
    
    echo ""
    
    # 检查 Docker 环境
    check_docker_environment
    
    echo ""
    
    # 检查网络连接
    check_network_connectivity
    
    echo ""
    
    # 显示检查结果汇总
    echo -e "${BLUE}=================================================="
    echo "                检查结果汇总"
    echo "==================================================${NC}"
    echo -e "${GREEN}通过: $CHECKS_PASSED${NC}"
    echo -e "${YELLOW}警告: $CHECKS_WARNING${NC}"
    echo -e "${RED}失败: $CHECKS_FAILED${NC}"
    
    echo ""
    
    if [ $CHECKS_FAILED -eq 0 ]; then
        if [ $CHECKS_WARNING -eq 0 ]; then
            echo -e "${GREEN}✓ 配置检查全部通过，可以进行部署！${NC}"
            exit 0
        else
            echo -e "${YELLOW}⚠ 配置检查基本通过，但有一些警告项需要注意${NC}"
            exit 0
        fi
    else
        echo -e "${RED}✗ 配置检查发现问题，请修复后再进行部署${NC}"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "AI指标管控平台配置检查脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [command]"
    echo ""
    echo "命令:"
    echo "  check     执行完整配置检查 (默认)"
    echo "  env       仅检查环境变量配置"
    echo "  docker    仅检查 Docker 环境"
    echo "  network   仅检查网络连接"
    echo "  help      显示帮助信息"
}

# 主函数
case "${1:-check}" in
    "check"|"")
        main_check
        ;;
    "env")
        log_info "检查环境变量配置..."
        check_env_var "POSTGRES_PASSWORD" "数据库密码"
        check_env_var "MINIO_ROOT_PASSWORD" "MinIO 密码"
        check_env_var "REDIS_PASSWORD" "Redis 密码" false
        check_env_var "SERVER_IP" "服务器IP地址"
        ;;
    "docker")
        check_docker_environment
        ;;
    "network")
        check_network_connectivity
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        echo -e "${RED}未知命令: $1${NC}"
        show_help
        exit 1
        ;;
esac
