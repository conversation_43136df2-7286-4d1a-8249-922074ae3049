#!/bin/bash

# AI指标管控平台 Docker Compose 部署脚本
# 使用方法: ./deploy.sh [dev|prod|stop|restart|logs|status]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 和 Docker Compose
check_requirements() {
    log_info "检查系统要求..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "系统要求检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p logs
    mkdir -p backups
    mkdir -p config
    mkdir -p data/{postgres,minio,redis}
    
    # 设置权限
    chmod +x init-scripts/init-minio.sh
    
    log_success "目录创建完成"
}

# 开发环境部署
deploy_dev() {
    log_info "开始部署开发环境..."
    
    check_requirements
    create_directories
    
    # 停止现有服务
    docker-compose down --remove-orphans
    
    # 构建并启动服务
    docker-compose up --build -d
    
    log_success "开发环境部署完成！"
    log_info "前端访问地址: http://localhost"
    log_info "后端API地址: http://localhost:8080"
    log_info "MinIO控制台: http://localhost:9001"
    log_info "PostgreSQL端口: 5432"
    log_info "Redis端口: 6379"
}

# 生产环境部署
deploy_prod() {
    log_info "开始部署生产环境..."
    
    check_requirements
    create_directories
    
    # 检查环境变量文件
    if [ ! -f .env ]; then
        log_error ".env 文件不存在，请先配置环境变量"
        exit 1
    fi
    
    # 停止现有服务
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml down --remove-orphans
    
    # 构建并启动服务
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml up --build -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    # 初始化 MinIO
    log_info "初始化 MinIO..."
    docker-compose exec -T minio sh -c "
        mc alias set myminio http://localhost:9000 \$MINIO_ROOT_USER \$MINIO_ROOT_PASSWORD
        mc mb myminio/images || true
        mc policy set public myminio/images || true
    "
    
    log_success "生产环境部署完成！"
    log_info "请确保防火墙已开放相应端口"
}

# 停止服务
stop_services() {
    log_info "停止所有服务..."
    
    docker-compose down --remove-orphans
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml down --remove-orphans
    
    log_success "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    
    if [ "$1" = "prod" ]; then
        docker-compose -f docker-compose.yml -f docker-compose.prod.yml restart
    else
        docker-compose restart
    fi
    
    log_success "服务已重启"
}

# 查看日志
view_logs() {
    if [ -n "$2" ]; then
        # 查看特定服务的日志
        if [ "$1" = "prod" ]; then
            docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs -f "$2"
        else
            docker-compose logs -f "$2"
        fi
    else
        # 查看所有服务的日志
        if [ "$1" = "prod" ]; then
            docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs -f
        else
            docker-compose logs -f
        fi
    fi
}

# 查看服务状态
check_status() {
    log_info "检查服务状态..."
    
    if [ "$1" = "prod" ]; then
        docker-compose -f docker-compose.yml -f docker-compose.prod.yml ps
    else
        docker-compose ps
    fi
    
    echo ""
    log_info "容器健康状态:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
}

# 备份数据
backup_data() {
    log_info "开始备份数据..."
    
    BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # 备份数据库
    docker-compose exec -T postgres pg_dump -U root zj_db > "$BACKUP_DIR/database.sql"
    
    # 备份 MinIO 数据
    docker run --rm -v ai-indicator-control-platform_minio_data:/data -v "$(pwd)/$BACKUP_DIR":/backup alpine tar czf /backup/minio_data.tar.gz -C /data .
    
    log_success "数据备份完成: $BACKUP_DIR"
}

# 显示帮助信息
show_help() {
    echo "AI指标管控平台 Docker Compose 部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 dev              部署开发环境"
    echo "  $0 prod             部署生产环境"
    echo "  $0 stop             停止所有服务"
    echo "  $0 restart [env]    重启服务 (env: dev|prod)"
    echo "  $0 logs [env] [service]  查看日志"
    echo "  $0 status [env]     查看服务状态"
    echo "  $0 backup           备份数据"
    echo "  $0 help             显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 dev              # 部署开发环境"
    echo "  $0 prod             # 部署生产环境"
    echo "  $0 logs dev backend # 查看开发环境后端日志"
    echo "  $0 status prod      # 查看生产环境状态"
}

# 主函数
main() {
    case "$1" in
        "dev")
            deploy_dev
            ;;
        "prod")
            deploy_prod
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services "$2"
            ;;
        "logs")
            view_logs "$2" "$3"
            ;;
        "status")
            check_status "$2"
            ;;
        "backup")
            backup_data
            ;;
        "help"|"--help"|"-h")
            show_help
            ;;
        *)
            log_error "无效的参数: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
