version: '3.8'

# 生产环境配置覆盖
services:
  postgres:
    # 生产环境数据库配置
    environment:
      POSTGRES_SHARED_PRELOAD_LIBRARIES: pg_stat_statements
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
      -c work_mem=4MB
      -c min_wal_size=1GB
      -c max_wal_size=4GB
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
      - ./data/backups:/backups
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  redis:
    # 生产环境Redis配置
    command: >
      redis-server
      --appendonly yes
      --requirepass "${REDIS_PASSWORD:-}"
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  minio:
    # 生产环境MinIO配置
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
      MINIO_BROWSER_REDIRECT_URL: http://${SERVER_IP}:${MINIO_CONSOLE_PORT}
    volumes:
      - ./data/minio:/data
      - ./minio-config:/root/.minio
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  backend:
    # 生产环境后端配置
    environment:
      SPRING_PROFILES_ACTIVE: prod
      JAVA_OPTS: ${JAVA_OPTS}
      SPRING_DATASOURCE_URL: *******************************/${POSTGRES_DB}
      SPRING_DATASOURCE_USERNAME: ${POSTGRES_USER}
      SPRING_DATASOURCE_PASSWORD: ${POSTGRES_PASSWORD}
      SPRING_DATA_REDIS_HOST: redis
      SPRING_DATA_REDIS_PORT: 6379
      SPRING_DATA_REDIS_PASSWORD: ${REDIS_PASSWORD}
      OSS_ENDPOINT: http://minio:9000
      OSS_ACCESS_KEY: ${MINIO_ROOT_USER}
      OSS_SECRET_KEY: ${MINIO_ROOT_PASSWORD}
      OSS_BUCKET_NAME: ${MINIO_BUCKET_NAME}
      DIFY_API_BASE_URL: ${DIFY_API_BASE_URL}
      DIFY_API_KEY: ${DIFY_API_KEY}
      SPRING_AI_OPENAI_BASE_URL: ${SPRING_AI_OPENAI_BASE_URL}
      SPRING_AI_OPENAI_API_KEY: ${SPRING_AI_OPENAI_API_KEY}
      LOGGING_LEVEL_ROOT: ${LOG_LEVEL}
    volumes:
      - ./data/logs:/blade/logs
      - ./config:/blade/config
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"

  frontend:
    # 生产环境前端配置
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"
