<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <avue-crud
      :option="option"
      v-model:search="search"
      v-model:page="page"
      v-model="form"
      :table-loading="loading"
      :data="data"
      :permission="permissionList"
      :before-open="beforeOpen"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template #menu-left>
        <el-button
          type="danger"
          icon="el-icon-delete"
          plain
          v-if="permission.indicatorValues_delete"
          @click="handleDelete"
        >
          删 除
        </el-button>
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          @click="handleExport"
        >
          导 出
        </el-button>
      </template>
    </avue-crud>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { getList, getDetail, add, update, remove } from "@/api/yjzb/indicatorValues";
import option from "@/option/yjzb/indicatorValues";
import { mapGetters } from "vuex";
import { exportBlob } from "@/api/common";
import { getToken } from '@/utils/auth';
import { downloadXls } from "@/utils/util";
import { dateNow } from "@/utils/date";
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';

export default {
  name: "IndicatorValuesDialog",
  props: {
    // 控制弹窗显示/隐藏
    modelValue: {
      type: Boolean,
      default: false
    },
    // 指标ID
    indicatorId: {
      type: [String, Number],
      default: null
    },
    // 指标名称，用于弹窗标题
    indicatorName: {
      type: String,
      default: ""
    }
  },
  emits: ['update:modelValue'],
  data() {
    return {
      form: {},
      query: {},
      search: {},
      loading: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: option,
      data: []
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    visible: {
      get() {
        return this.modelValue;
      },
      set(value) {
        this.$emit('update:modelValue', value);
      }
    },
    dialogTitle() {
      return this.indicatorName ? `${this.indicatorName} - 指标数据` : "指标数据";
    },
    permissionList() {
      return {
        addBtn: this.validData(this.permission.indicatorValues_add, false),
        viewBtn: this.validData(this.permission.indicatorValues_view, false),
        delBtn: this.validData(this.permission.indicatorValues_delete, false),
        editBtn: this.validData(this.permission.indicatorValues_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  watch: {
    visible(newVal) {
      if (newVal && this.indicatorId) {
        // 弹窗打开时加载数据
        this.initData();
      }
    },
    indicatorId(newVal) {
      if (newVal && this.visible) {
        // 指标ID变化时重新加载数据
        this.initData();
      }
    }
  },
  methods: {
    // 初始化数据
    initData() {
      this.query = { indicatorId: this.indicatorId };
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    
    // 关闭弹窗
    handleClose() {
      this.visible = false;
      // 清空数据
      this.data = [];
      this.query = {};
      this.selectionList = [];
    },

    rowSave(row, done, loading) {
      // 确保指标ID正确设置
      row.indicatorId = this.indicatorId;
      add(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        console.log(error);
      });
    },

    rowUpdate(row, index, done, loading) {
      update(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        console.log(error);
      });
    },

    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },

    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },

    handleExport() {
      let downloadUrl = `/yjzb/indicatorValues/export-indicatorValues?${this.website.tokenHeader}=${getToken()}`;
      let values = {
        indicatorId: this.indicatorId,
      };
      this.$confirm("是否导出数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        NProgress.start();
        exportBlob(downloadUrl, values).then(res => {
          downloadXls(res.data, `指标数据${dateNow()}.xlsx`);
          NProgress.done();
        })
      });
    },

    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      } else if (type === "add") {
        // 新增时设置指标ID
        this.form.indicatorId = this.indicatorId;
      }
      done();
    },

    searchReset() {
      this.query = { indicatorId: this.indicatorId };
      this.onLoad(this.page);
    },

    searchChange(params, done) {
      // 保持指标ID筛选
      this.query = { ...params, indicatorId: this.indicatorId };
      this.page.currentPage = 1;
      this.onLoad(this.page, this.query);
      done();
    },

    selectionChange(list) {
      this.selectionList = list;
    },

    selectionClear() {
      this.selectionList = [];
      if (this.$refs.crud) {
        this.$refs.crud.toggleSelection();
      }
    },

    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },

    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },

    refreshChange() {
      this.onLoad(this.page, this.query);
    },

    onLoad(page, params = {}) {
      if (!this.indicatorId) {
        return;
      }
      
      this.loading = true;
      
      // 确保始终按指标ID筛选
      let values = {
        ...params,
        indicatorId: this.indicatorId,
      };

      getList(page.currentPage, page.pageSize, values).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      }).catch(error => {
        console.error("加载指标数据失败:", error);
        this.loading = false;
        this.$message.error("加载数据失败");
      });
    }
  }
};
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
