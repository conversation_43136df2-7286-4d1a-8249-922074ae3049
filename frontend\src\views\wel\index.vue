<template>
  <basic-container>
    <!-- 管控指标统计区 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-title">管控指标总数</div>
          <div class="metric-value">128</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-title">指标预警数</div>
          <div class="metric-value">5</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-title">问数助手对话次数</div>
          <div class="metric-value">648</div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 财务分析报告列表 -->
    <el-card class="dashboard-section" style="margin-bottom: 20px;">
      <div class="section-title">财务分析报告列表</div>
      <el-table :data="reportList" size="small" border style="width: 100%;">
        <el-table-column prop="name" label="报告名称" />
        <el-table-column prop="date" label="生成时间" width="160" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === '已完成' ? 'success' : 'info'">{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button type="primary" size="mini">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 实时告警/异常 -->
    <el-card class="dashboard-section" style="margin-bottom: 20px;">
      <div class="section-title">实时告警/异常</div>
      <el-table :data="alertList" size="small" border style="width: 100%;">
        <el-table-column prop="time" label="时间" width="160" />
        <el-table-column prop="type" label="类型" width="100" />
        <el-table-column prop="content" label="告警内容" />
        <el-table-column prop="level" label="级别" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.level === '高' ? 'danger' : (scope.row.level === '中' ? 'warning' : 'info')">
              {{ scope.row.level }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 系统日志列表（原财务数据趋势区） -->
    <el-card class="dashboard-section" style="margin-bottom: 20px;">
      <div class="section-title">系统日志列表</div>
      <el-table :data="logList" size="small" border style="width: 100%;">
        <el-table-column prop="time" label="时间" width="160" />
        <el-table-column prop="type" label="类型" width="100" />
        <el-table-column prop="content" label="内容" />
        <el-table-column prop="level" label="级别" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.level === '高' ? 'danger' : (scope.row.level === '中' ? 'warning' : 'info')">
              {{ scope.row.level }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 系统资源监控 & 任务日志 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="dashboard-section">
          <div class="section-title">系统资源监控</div>
          <div id="dashboardResourceChart" style="width: 100%; height: 220px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="dashboard-section">
          <div class="section-title">任务与日志</div>
          <el-table :data="taskList" size="small" border style="width: 100%;">
            <el-table-column prop="time" label="时间" width="160" />
            <el-table-column prop="task" label="任务" width="120" />
            <el-table-column prop="status" label="状态" width="80">
              <template #default="scope">
                <el-tag :type="scope.row.status === '成功' ? 'success' : (scope.row.status === '失败' ? 'danger' : 'info')">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="desc" label="描述" />
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </basic-container>
</template>

<script>
export default {
  name: 'HomeDashboard',
  data() {
    return {
      // 财务分析报告列表静态数据
      reportList: [
        { name: '2024年5月利润表分析', date: '2024-06-01 08:00', status: '已完成' },
        { name: '2024年5月资产负债表分析', date: '2024-06-01 08:10', status: '已完成' },
        { name: '2024年5月税利指标明细', date: '2024-06-01 08:20', status: '已完成' }
      ],
      // 实时告警/异常静态数据
      alertList: [
        { time: '2024-06-01 09:12', type: '费用超支', content: '本月差旅费超200000元', level: '高' },
        { time: '2024-06-01 08:55', type: '数据异常', content: '5月发票数据缺失', level: '中' },
        { time: '2024-05-31 17:20', type: '费用超支', content: '本月车辆运行费超5000元', level: '低' }
      ],
      // 系统日志列表静态数据
      logList: [
        { time: '2024-06-01 09:15', type: '登录', content: '用户admin登录系统', level: '低' },
        { time: '2024-06-01 09:10', type: '操作', content: '导出利润表', level: '低' },
        { time: '2024-06-01 09:05', type: '异常', content: '报表生成失败', level: '中' },
        { time: '2024-06-01 09:00', type: '系统', content: '数据同步完成', level: '低' }
      ],
      // 任务与日志静态数据
      taskList: [
        { time: '2024-06-01 09:00', task: '数据同步', status: '成功', desc: '财务数据同步完成' },
        { time: '2024-06-01 08:30', task: '报表生成', status: '失败', desc: '利润表生成失败' },
        { time: '2024-06-01 08:00', task: '异常检测', status: '成功', desc: '未发现新异常' }
      ]
    }
  },
  mounted() {
    this.renderResourceChart();
  },
  methods: {
    // 系统资源监控图表渲染
    renderResourceChart() {
      if (!window.echarts) return;
      const dom = document.getElementById('dashboardResourceChart');
      if (dom) {
        const chart = window.echarts.init(dom);
        const option = {
          tooltip: {},
          legend: { data: ['CPU', '内存', '磁盘'] },
          xAxis: { type: 'category', data: ['09:00', '09:10', '09:20', '09:30', '09:40', '09:50'] },
          yAxis: { type: 'value', max: 100, min: 0, axisLabel: { formatter: '{value}%' } },
          series: [
            { name: 'CPU', type: 'line', data: [30, 40, 35, 50, 45, 38] },
            { name: '内存', type: 'line', data: [60, 62, 65, 63, 61, 60] },
            { name: '磁盘', type: 'line', data: [50, 52, 55, 53, 51, 50] }
          ],
          grid: { left: 40, right: 20, top: 40, bottom: 40 }
        };
        chart.setOption(option);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.metric-card {
  text-align: center;
  .metric-title {
    color: #909399;
    font-size: 15px;
    margin-bottom: 8px;
  }
  .metric-value {
    font-size: 28px;
    font-weight: bold;
    color: #409eff;
  }
}
.dashboard-section {
  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 12px;
  }
}
</style> 