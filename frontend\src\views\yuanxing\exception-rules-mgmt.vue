<template>
  <!-- 异常检测与预警 - 异常规则管理页面 -->
  <basic-container>
    <!-- 页面标题和操作 -->
    <div class="page-header">
      <h3>异常规则管理</h3>
      <div class="header-actions">
        <el-button type="primary" @click="createRule">
          <el-icon><plus /></el-icon> 新增规则
        </el-button>
        <el-button @click="batchImport">
          <el-icon><upload /></el-icon> 批量导入
        </el-button>
        <el-button @click="testRules">
          <el-icon><operation /></el-icon> 规则测试
        </el-button>
        <el-button @click="refreshData">
          <el-icon><refresh /></el-icon> 刷新
        </el-button>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-panel">
      <el-form inline label-width="80px">
        <el-form-item label="规则名称">
          <el-input
            v-model="searchKeyword"
            placeholder="输入规则名称搜索"
            clearable
            style="width: 200px;"
          />
        </el-form-item>
        <el-form-item label="规则类型">
          <el-select v-model="typeFilter" placeholder="选择类型" clearable style="width: 150px;">
            <el-option label="全部" value="" />
            <el-option label="阈值异常" value="threshold" />
            <el-option label="趋势异常" value="trend" />
            <el-option label="同比异常" value="comparison" />
            <el-option label="组合异常" value="combination" />
          </el-select>
        </el-form-item>
        <el-form-item label="严重级别">
          <el-select v-model="severityFilter" placeholder="选择级别" clearable style="width: 120px;">
            <el-option label="全部" value="" />
            <el-option label="低" value="low" />
            <el-option label="中" value="medium" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="critical" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="statusFilter" placeholder="选择状态" clearable style="width: 100px;">
            <el-option label="全部" value="" />
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 规则表格 -->
    <div class="table-container">
      <el-table
        :data="ruleList"
        v-loading="loading"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" label="序号" width="60" />
        
        <el-table-column prop="name" label="规则名称" min-width="180" show-overflow-tooltip />
        
        <el-table-column prop="type" label="规则类型" width="120">
          <template #default="scope">
            <el-tag :type="getRuleTypeTag(scope.row.type)" size="small">
              {{ getRuleTypeName(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="indicators" label="监控指标" min-width="150" show-overflow-tooltip>
          <template #default="scope">
            <span v-if="scope.row.indicators && scope.row.indicators.length > 0">
              {{ scope.row.indicators.map(item => item.name).join(', ') }}
            </span>
            <span v-else style="color: #c0c4cc;">-</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="condition" label="触发条件" min-width="200" show-overflow-tooltip />
        
        <el-table-column prop="severity" label="严重级别" width="100">
          <template #default="scope">
            <el-tag :type="getSeverityTag(scope.row.severity)" size="small">
              {{ getSeverityName(scope.row.severity) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="alertMethods" label="预警方式" width="120">
          <template #default="scope">
            <div class="alert-methods">
              <el-tag v-if="scope.row.alertMethods.includes('system')" size="mini" type="info">系统</el-tag>
              <el-tag v-if="scope.row.alertMethods.includes('dingding')" size="mini" type="primary">钉钉</el-tag>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="triggerCount" label="触发次数" width="100" align="center">
          <template #default="scope">
            <el-link type="primary" @click="viewTriggerHistory(scope.row)">
              {{ scope.row.triggerCount }}
            </el-link>
          </template>
        </el-table-column>
        
        <el-table-column prop="lastTriggerTime" label="最后触发" width="180" />
        
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="toggleStatus(scope.row)"
            />
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right" align="center">
          <template #default="scope">
            <el-button type="text" size="small" @click="viewRule(scope.row)">
              <el-icon><view /></el-icon> 详情
            </el-button>
            <el-button type="text" size="small" @click="editRule(scope.row)">
              <el-icon><edit /></el-icon> 编辑
            </el-button>
            <el-button 
              type="text" 
              size="small" 
              @click="deleteRule(scope.row)"
              style="color: #f56c6c;"
            >
              <el-icon><delete /></el-icon> 删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 批量操作栏 -->
      <div class="batch-actions" v-if="selectedRules.length > 0">
        <span>已选择 {{ selectedRules.length }} 项</span>
        <el-button size="small" @click="batchEnable">批量启用</el-button>
        <el-button size="small" @click="batchDisable">批量禁用</el-button>
        <el-button size="small" @click="batchDelete" type="danger">批量删除</el-button>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        :page-size="pageSize"
        :current-page="currentPage"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑规则对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="900px"
      destroy-on-close
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-tabs v-model="activeTab" type="card">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <el-form-item label="规则名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入规则名称" />
            </el-form-item>
            
            <el-form-item label="规则类型" prop="type">
              <el-select v-model="formData.type" placeholder="选择规则类型">
                <el-option label="阈值异常" value="threshold" />
                <el-option label="组合异常" value="combination" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="监控指标" prop="indicatorIds">
              <el-select
                v-model="formData.indicatorIds"
                multiple
                placeholder="选择要监控的指标"
                filterable
                style="width: 100%;"
              >
                <el-option
                  v-for="indicator in indicatorOptions"
                  :key="indicator.id"
                  :label="`${indicator.name}(${indicator.code})`"
                  :value="indicator.id"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="严重级别" prop="severity">
              <el-select v-model="formData.severity" placeholder="选择严重级别">
                <el-option label="低" value="low" />
                <el-option label="中" value="medium" />
                <el-option label="高" value="high" />
                <el-option label="紧急" value="critical" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="规则描述" prop="description">
              <el-input
                v-model="formData.description"
                type="textarea"
                :rows="3"
                placeholder="请输入规则描述"
              />
            </el-form-item>
            
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="formData.status">
                <el-radio :label="1">启用</el-radio>
                <el-radio :label="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-tab-pane>
          
          <!-- 条件配置 -->
          <el-tab-pane label="条件配置" name="condition">
            <!-- 阈值异常 -->
            <div v-if="formData.type === 'threshold'">
              <el-form-item label="阈值类型" prop="thresholdType">
                <el-radio-group v-model="formData.thresholdType">
                  <el-radio label="greater">大于</el-radio>
                  <el-radio label="less">小于</el-radio>
                  <el-radio label="equal">等于</el-radio>
                  <el-radio label="range">范围</el-radio>
                </el-radio-group>
              </el-form-item>
              
              <el-form-item label="阈值设置" prop="threshold">
                <div v-if="formData.thresholdType === 'range'">
                  <el-input-number v-model="formData.minThreshold" placeholder="最小值" :precision="2" />
                  <span style="margin: 0 10px;">到</span>
                  <el-input-number v-model="formData.maxThreshold" placeholder="最大值" :precision="2" />
                </div>
                <el-input-number v-else v-model="formData.threshold" placeholder="阈值" :precision="2" />
              </el-form-item>
              
              <el-form-item label="持续时间" prop="duration">
                <el-input-number v-model="formData.duration" :min="1" />
                <span style="margin-left: 10px;">分钟（异常持续时间超过此值才触发）</span>
              </el-form-item>
            </div>
            
            <!-- 趋势异常 -->
            <div v-if="formData.type === 'trend'">
              <el-form-item label="趋势类型" prop="trendType">
                <el-radio-group v-model="formData.trendType">
                  <el-radio label="increasing">持续上升</el-radio>
                  <el-radio label="decreasing">持续下降</el-radio>
                  <el-radio label="fluctuation">剧烈波动</el-radio>
                </el-radio-group>
              </el-form-item>
              
              <el-form-item label="监控周期" prop="monitorPeriod">
                <el-select v-model="formData.monitorPeriod">
                  <el-option label="最近1小时" value="1h" />
                  <el-option label="最近6小时" value="6h" />
                  <el-option label="最近24小时" value="24h" />
                  <el-option label="最近7天" value="7d" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="变化幅度" prop="changeRate">
                <el-input-number v-model="formData.changeRate" :precision="1" />
                <span style="margin-left: 10px;">%（变化幅度超过此值才触发）</span>
              </el-form-item>
            </div>
            
            <!-- 同比异常 -->
            <div v-if="formData.type === 'comparison'">
              <el-form-item label="对比类型" prop="comparisonType">
                <el-radio-group v-model="formData.comparisonType">
                  <el-radio label="yoy">同比（与去年同期）</el-radio>
                  <el-radio label="mom">环比（与上月）</el-radio>
                  <el-radio label="wow">周比（与上周）</el-radio>
                </el-radio-group>
              </el-form-item>
              
              <el-form-item label="异常阈值" prop="deviationThreshold">
                <el-input-number v-model="formData.deviationThreshold" :precision="1" />
                <span style="margin-left: 10px;">%（偏差幅度超过此值才触发）</span>
              </el-form-item>
            </div>
            
            <!-- 组合异常 -->
            <div v-if="formData.type === 'combination'">
              <el-form-item label="组合逻辑" prop="combinationLogic">
                <el-radio-group v-model="formData.combinationLogic">
                  <el-radio label="and">所有条件都满足</el-radio>
                  <el-radio label="or">任一条件满足</el-radio>
                </el-radio-group>
              </el-form-item>
              
              <el-form-item label="条件表达式" prop="conditionExpression">
                <el-input
                  v-model="formData.conditionExpression"
                  type="textarea"
                  :rows="4"
                  placeholder="如：A > 100 AND B < 50"
                />
                <div class="form-tip">使用指标编码构建条件表达式，支持 AND、OR、()等逻辑运算符</div>
              </el-form-item>
            </div>
          </el-tab-pane>
          
          <!-- 预警配置 -->
          <el-tab-pane label="预警配置" name="alert">
            <el-form-item label="预警方式" prop="alertMethods">
              <el-checkbox-group v-model="formData.alertMethods">
                <el-checkbox label="system">系统通知</el-checkbox>
                <el-checkbox label="dingding">钉钉通知</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            
            <el-form-item label="通知人员" prop="notifyUsers">
              <el-select
                v-model="formData.notifyUsers"
                multiple
                placeholder="选择通知人员"
                filterable
              >
                <el-option
                  v-for="user in userOptions"
                  :key="user.id"
                  :label="user.name"
                  :value="user.id"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="预警频率" prop="alertFrequency">
              <el-select v-model="formData.alertFrequency" placeholder="选择预警频率">
                <el-option label="立即通知" value="immediate" />
                <el-option label="每5分钟" value="5min" />
                <el-option label="每15分钟" value="15min" />
                <el-option label="每30分钟" value="30min" />
                <el-option label="每1小时" value="1hour" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="静默时间" prop="silentTime">
              <el-time-picker
                v-model="formData.silentTime"
                is-range
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="HH:mm"
                value-format="HH:mm"
              />
              <div class="form-tip">在此时间段内不发送预警通知</div>
            </el-form-item>
            
            <el-form-item label="消息模板" prop="messageTemplate">
              <el-input
                v-model="formData.messageTemplate"
                type="textarea"
                :rows="4"
                placeholder="自定义预警消息模板，可使用变量：{指标名称}、{当前值}、{时间}等"
              />
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确认</el-button>
      </template>
    </el-dialog>

    <!-- 规则详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      :title="`${currentRule?.name} - 详情`"
      width="800px"
      destroy-on-close
    >
      <div v-if="currentRule" class="rule-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="规则名称">{{ currentRule.name }}</el-descriptions-item>
          <el-descriptions-item label="规则类型">{{ getRuleTypeName(currentRule.type) }}</el-descriptions-item>
          <el-descriptions-item label="严重级别">
            <el-tag :type="getSeverityTag(currentRule.severity)">
              {{ getSeverityName(currentRule.severity) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="currentRule.status === 1 ? 'success' : 'danger'">
              {{ currentRule.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="触发次数">{{ currentRule.triggerCount }}</el-descriptions-item>
          <el-descriptions-item label="最后触发">{{ currentRule.lastTriggerTime || '-' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ currentRule.createTime }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ currentRule.updateTime }}</el-descriptions-item>
          <el-descriptions-item label="描述说明" :span="2">{{ currentRule.description || '-' }}</el-descriptions-item>
          <el-descriptions-item label="触发条件" :span="2">{{ currentRule.condition }}</el-descriptions-item>
        </el-descriptions>
        
        <!-- 监控指标 -->
        <div class="indicators-section" style="margin-top: 20px;">
          <h4>监控指标</h4>
          <el-table :data="currentRule.indicators" border size="small">
            <el-table-column prop="name" label="指标名称" />
            <el-table-column prop="code" label="指标编码" />
            <el-table-column prop="unit" label="单位" />
            <el-table-column prop="currentValue" label="当前值" />
          </el-table>
        </div>
      </div>
    </el-dialog>

    <!-- 触发历史对话框 -->
    <el-dialog
      v-model="historyDialogVisible"
      :title="`${currentRule?.name} - 触发历史`"
      width="900px"
      destroy-on-close
    >
      <el-table :data="triggerHistory" border>
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="triggerTime" label="触发时间" width="180" />
        <el-table-column prop="triggerValue" label="触发值" width="100" />
        <el-table-column prop="severity" label="严重级别" width="100">
          <template #default="scope">
            <el-tag :type="getSeverityTag(scope.row.severity)" size="small">
              {{ getSeverityName(scope.row.severity) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="alertSent" label="已通知" width="80" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.alertSent ? 'success' : 'danger'" size="small">
              {{ scope.row.alertSent ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="message" label="触发信息" min-width="200" show-overflow-tooltip />
      </el-table>
    </el-dialog>

    <!-- 规则测试对话框 -->
    <el-dialog
      v-model="testDialogVisible"
      title="规则测试"
      width="600px"
      destroy-on-close
    >
      <div class="test-section">
        <el-form label-width="120px">
          <el-form-item label="测试数据">
            <el-input
              v-model="testData"
              type="textarea"
              :rows="4"
              placeholder="输入测试数据（JSON格式）"
            />
          </el-form-item>
        </el-form>
        
        <div class="test-result" v-if="testResult">
          <h4>测试结果</h4>
          <el-alert
            :title="testResult.title"
            :type="testResult.type"
            :description="testResult.description"
            show-icon
          />
        </div>
      </div>
      
      <template #footer>
        <el-button @click="testDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="runTest">执行测试</el-button>
      </template>
    </el-dialog>
  </basic-container>
</template>

<script>
import { Plus, Upload, Operation, Refresh, View, Edit, Cpu, Delete } from '@element-plus/icons-vue'

export default {
  name: 'ExceptionRulesMgmt',
  components: {
    Plus, Upload, Operation, Refresh, View, Edit, Cpu, Delete
  },
  data() {
    return {
      loading: false,
      searchKeyword: '',
      typeFilter: '',
      severityFilter: '',
      statusFilter: '',
      currentPage: 1,
      pageSize: 20,
      total: 0,
      selectedRules: [],
      
      dialogVisible: false,
      detailDialogVisible: false,
      historyDialogVisible: false,
      testDialogVisible: false,
      currentRule: null,
      activeTab: 'basic',
      testData: '',
      testResult: null,
      
      // 表单数据
      formData: {
        id: null,
        name: '',
        type: '',
        indicatorIds: [],
        severity: '',
        description: '',
        status: 1,
        // 阈值异常相关
        thresholdType: 'greater',
        threshold: null,
        minThreshold: null,
        maxThreshold: null,
        duration: 5,
        // 趋势异常相关
        trendType: 'increasing',
        monitorPeriod: '24h',
        changeRate: 10,
        // 同比异常相关
        comparisonType: 'yoy',
        deviationThreshold: 20,
        // 组合异常相关
        combinationLogic: 'and',
        conditionExpression: '',
        // 预警配置
        alertMethods: ['system'],
        notifyUsers: [],
        alertFrequency: 'immediate',
        silentTime: null,
        messageTemplate: ''
      },
      
      // 表单验证规则
      formRules: {
        name: [
          { required: true, message: '请输入规则名称', trigger: 'blur' },
          { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择规则类型', trigger: 'change' }
        ],
        indicatorIds: [
          { required: true, message: '请选择监控指标', trigger: 'change' }
        ],
        severity: [
          { required: true, message: '请选择严重级别', trigger: 'change' }
        ]
      },
      
      // 选项数据
      indicatorOptions: [
        { id: 1, name: '净利润率', code: 'NET_PROFIT_MARGIN' },
        { id: 2, name: '营业收入增长率', code: 'REVENUE_GROWTH_RATE' },
        { id: 3, name: '流动比率', code: 'CURRENT_RATIO' },
        { id: 4, name: '存货周转率', code: 'INVENTORY_TURNOVER' },
        { id: 5, name: '客户满意度', code: 'CUSTOMER_SATISFACTION' },
        { id: 6, name: '员工流失率', code: 'EMPLOYEE_TURNOVER_RATE' }
      ],
      
      userOptions: [
        { id: 1, name: '张财务' },
        { id: 2, name: '李会计' },
        { id: 3, name: '王税务' },
        { id: 4, name: '陈总监' },
        { id: 5, name: '赵经理' }
      ],
      
      // 规则列表数据
      ruleList: [
        {
          id: 1,
          name: '净利润率异常监控',
          type: 'threshold',
          indicators: [{ id: 1, name: '净利润率', code: 'NET_PROFIT_MARGIN', unit: '%', currentValue: 12.5 }],
          condition: '净利润率 < 10%，持续时间 > 5分钟',
          severity: 'high',
          alertMethods: ['system', 'dingding'],
          triggerCount: 3,
          lastTriggerTime: '2024-01-15 14:30:00',
          status: 1,
          createTime: '2024-01-01 10:00:00',
          updateTime: '2024-01-15 14:30:00',
          description: '监控净利润率异常下降，及时发现盈利能力问题'
        },
        {
          id: 2,
          name: '客户满意度组合预警',
          type: 'combination',
          indicators: [
            { id: 5, name: '客户满意度', code: 'CUSTOMER_SATISFACTION', unit: '%', currentValue: 92.5 },
            { id: 6, name: '员工流失率', code: 'EMPLOYEE_TURNOVER_RATE', unit: '%', currentValue: 5.2 }
          ],
          condition: '客户满意度 < 90% AND 员工流失率 > 8%',
          severity: 'critical',
          alertMethods: ['system', 'dingding'],
          triggerCount: 2,
          lastTriggerTime: '2024-01-13 16:45:00',
          status: 1,
          createTime: '2024-01-01 10:00:00',
          updateTime: '2024-01-13 16:45:00',
          description: '综合监控客户满意度和员工稳定性指标'
        },
        {
          id: 3,
          name: '存货周转率阈值监控',
          type: 'threshold',
          indicators: [{ id: 4, name: '存货周转率', code: 'INVENTORY_TURNOVER', unit: '次', currentValue: 4.6 }],
          condition: '存货周转率 < 3次，持续时间 > 10分钟',
          severity: 'low',
          alertMethods: ['system'],
          triggerCount: 5,
          lastTriggerTime: '2024-01-12 11:20:00',
          status: 0,
          createTime: '2024-01-01 10:00:00',
          updateTime: '2024-01-12 11:20:00',
          description: '监控存货周转效率，预警库存积压风险'
        },
        {
          id: 4,
          name: '营业收入阈值异常',
          type: 'threshold',
          indicators: [{ id: 2, name: '营业收入增长率', code: 'REVENUE_GROWTH_RATE', unit: '%', currentValue: 8.3 }],
          condition: '营业收入增长率 < 5%，持续时间 > 15分钟',
          severity: 'medium',
          alertMethods: ['dingding'],
          triggerCount: 1,
          lastTriggerTime: '2024-01-14 09:15:00',
          status: 1,
          createTime: '2024-01-01 10:00:00',
          updateTime: '2024-01-14 09:15:00',
          description: '监控营业收入增长率异常，及时发现业绩下滑风险'
        },
        {
          id: 5,
          name: '综合财务组合异常',
          type: 'combination',
          indicators: [
            { id: 1, name: '净利润率', code: 'NET_PROFIT_MARGIN', unit: '%', currentValue: 12.5 },
            { id: 2, name: '营业收入增长率', code: 'REVENUE_GROWTH_RATE', unit: '%', currentValue: 8.3 }
          ],
          condition: '净利润率 < 10% OR 营业收入增长率 < 5%',
          severity: 'high',
          alertMethods: ['system', 'dingding'],
          triggerCount: 4,
          lastTriggerTime: '2024-01-10 10:00:00',
          status: 1,
          createTime: '2024-01-01 10:00:00',
          updateTime: '2024-01-10 10:00:00',
          description: '综合监控主要财务指标，及时发现异常'
        }
      ],
      
      // 触发历史数据
      triggerHistory: [
        {
          id: 1,
          triggerTime: '2024-01-15 14:30:00',
          triggerValue: 9.5,
          severity: 'high',
          alertSent: true,
          message: '净利润率跌破10%阈值，当前值9.5%，请及时关注'
        },
        {
          id: 2,
          triggerTime: '2024-01-14 16:15:00',
          triggerValue: 9.8,
          severity: 'high',
          alertSent: true,
          message: '净利润率跌破10%阈值，当前值9.8%，请及时关注'
        },
        {
          id: 3,
          triggerTime: '2024-01-13 10:20:00',
          triggerValue: 9.2,
          severity: 'high',
          alertSent: false,
          message: '净利润率跌破10%阈值，当前值9.2%，请及时关注'
        }
      ]
    }
  },
  computed: {
    dialogTitle() {
      return this.formData.id ? '编辑异常规则' : '新增异常规则'
    }
  },
  methods: {
    // 工具方法
    getRuleTypeTag(type) {
      const tagMap = {
        threshold: 'primary',
        combination: 'danger'
      }
      return tagMap[type] || 'info'
    },
    
    getRuleTypeName(type) {
      const nameMap = {
        threshold: '阈值异常',
        combination: '组合异常'
      }
      return nameMap[type] || type
    },
    
    getSeverityTag(severity) {
      const tagMap = {
        low: 'info',
        medium: 'warning',
        high: 'danger',
        critical: 'danger'
      }
      return tagMap[severity] || 'info'
    },
    
    getSeverityName(severity) {
      const nameMap = {
        low: '低',
        medium: '中',
        high: '高',
        critical: '紧急'
      }
      return nameMap[severity] || severity
    },
    
    // 搜索和筛选
    handleSearch() {
      console.log('搜索条件:', {
        keyword: this.searchKeyword,
        type: this.typeFilter,
        severity: this.severityFilter,
        status: this.statusFilter
      })
    },
    
    resetFilter() {
      this.searchKeyword = ''
      this.typeFilter = ''
      this.severityFilter = ''
      this.statusFilter = ''
      this.handleSearch()
    },
    
    // 数据操作
    refreshData() {
      this.loading = true
      setTimeout(() => {
        this.loading = false
        this.$message.success('刷新成功')
      }, 1000)
    },
    
    // 批量操作
    handleSelectionChange(selection) {
      this.selectedRules = selection
    },
    
    batchEnable() {
      this.$message.success(`已启用 ${this.selectedRules.length} 个规则`)
      this.selectedRules = []
    },
    
    batchDisable() {
      this.$message.success(`已禁用 ${this.selectedRules.length} 个规则`)
      this.selectedRules = []
    },
    
    batchDelete() {
      this.$confirm(`确认删除选中的 ${this.selectedRules.length} 个规则？`, '批量删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('批量删除成功')
        this.selectedRules = []
      })
    },
    
    batchImport() {
      this.$message.info('批量导入功能')
    },
    
    testRules() {
      this.$message.info('规则测试功能')
    },
    
    // 状态切换
    toggleStatus(row) {
      this.$message.success(`${row.name} 状态已${row.status === 1 ? '启用' : '禁用'}`)
    },
    
    // 规则操作
    createRule() {
      this.resetForm()
      this.dialogVisible = true
    },
    
    viewRule(row) {
      this.currentRule = row
      this.detailDialogVisible = true
    },
    
    editRule(row) {
      this.formData = { ...row }
      // 将indicators数组转换为indicatorIds数组
      if (row.indicators) {
        this.formData.indicatorIds = row.indicators.map(item => item.id)
      }
      this.dialogVisible = true
    },
    
    deleteRule(row) {
      this.$confirm(`确认删除规则"${row.name}"？删除后无法恢复。`, '确认删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.ruleList.findIndex(item => item.id === row.id)
        if (index > -1) {
          this.ruleList.splice(index, 1)
          this.$message.success('删除成功')
        }
      })
    },
    
    // 查看触发历史
    viewTriggerHistory(row) {
      this.currentRule = row
      this.historyDialogVisible = true
    },
    
    // 测试规则
    testRule(row) {
      this.currentRule = row
      this.testData = JSON.stringify({
        indicatorId: row.indicators[0]?.id,
        value: 95.5,
        timestamp: new Date().toISOString()
      }, null, 2)
      this.testResult = null
      this.testDialogVisible = true
    },
    
    runTest() {
      try {
        const data = JSON.parse(this.testData)
        // 模拟测试结果
        const isTriggered = Math.random() > 0.5
        this.testResult = {
          title: isTriggered ? '规则触发' : '规则未触发',
          type: isTriggered ? 'warning' : 'success',
          description: isTriggered ? 
            `根据测试数据，规则"${this.currentRule.name}"将被触发，预警信息将发送给相关人员。` :
            `根据测试数据，规则"${this.currentRule.name}"未达到触发条件。`
        }
      } catch (error) {
        this.$message.error('测试数据格式错误，请输入有效的JSON')
      }
    },
    
    // 表单操作
    submitForm() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          if (this.formData.id) {
            // 编辑
            const index = this.ruleList.findIndex(item => item.id === this.formData.id)
            if (index > -1) {
              Object.assign(this.ruleList[index], this.formData)
            }
            this.$message.success('编辑成功')
          } else {
            // 新增
            const newRule = {
              ...this.formData,
              id: Date.now(),
              triggerCount: 0,
              lastTriggerTime: null,
              createTime: new Date().toLocaleString(),
              updateTime: new Date().toLocaleString()
            }
            this.ruleList.push(newRule)
            this.$message.success('新增成功')
          }
          this.dialogVisible = false
        }
      })
    },
    
    resetForm() {
      this.formData = {
        id: null,
        name: '',
        type: '',
        indicatorIds: [],
        severity: '',
        description: '',
        status: 1,
        thresholdType: 'greater',
        threshold: null,
        minThreshold: null,
        maxThreshold: null,
        duration: 5,
        trendType: 'increasing',
        monitorPeriod: '24h',
        changeRate: 10,
        comparisonType: 'yoy',
        deviationThreshold: 20,
        combinationLogic: 'and',
        conditionExpression: '',
        alertMethods: ['system'],
        notifyUsers: [],
        alertFrequency: 'immediate',
        silentTime: null,
        messageTemplate: ''
      }
      this.activeTab = 'basic'
      if (this.$refs.formRef) {
        this.$refs.formRef.clearValidate()
      }
    },
    
    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.handleSearch()
    },
    
    handleCurrentChange(val) {
      this.currentPage = val
      this.handleSearch()
    }
  },
  
  mounted() {
    this.total = this.ruleList.length
  }
}
</script>

<style lang="scss" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h3 {
    margin: 0;
    color: #303133;
  }
  
  .header-actions {
    display: flex;
    gap: 10px;
  }
}

.filter-panel {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #ebeef5;
  padding: 20px;
  margin-bottom: 20px;
}

.table-container {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #ebeef5;
  padding: 20px;
  margin-bottom: 20px;
  
  .alert-methods {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }
  
  .batch-actions {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px;
    background: #f5f7fa;
    border-radius: 4px;
    margin-top: 15px;
    
    span {
      color: #606266;
      font-size: 14px;
    }
  }
}

.pagination-wrapper {
  text-align: center;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.rule-detail {
  .indicators-section {
    h4 {
      margin: 0 0 15px 0;
      color: #303133;
    }
  }
}

.test-section {
  .test-result {
    margin-top: 20px;
    
    h4 {
      margin: 0 0 15px 0;
      color: #303133;
    }
  }
}
</style>