-- AI指标管控平台数据库初始化脚本
-- 创建数据库和基础配置

-- 设置数据库编码
SET client_encoding = 'UTF8';

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- 创建数据库用户（如果不存在）
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'app_user') THEN
        CREATE ROLE app_user WITH LOGIN PASSWORD 'app_password';
    END IF;
END
$$;

-- 授权
GRANT CONNECT ON DATABASE zj_db TO app_user;
GRANT USAGE ON SCHEMA public TO app_user;
GRANT CREATE ON SCHEMA public TO app_user;

-- 创建基础表结构（如果需要的话）
-- 注意：BladeX框架通常会自动创建表结构，这里只是示例

-- 创建系统配置表
CREATE TABLE IF NOT EXISTS sys_config (
    id BIGSERIAL PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT,
    config_desc VARCHAR(500),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入初始配置
INSERT INTO sys_config (config_key, config_value, config_desc) VALUES 
('system.name', 'AI指标管控平台', '系统名称'),
('system.version', '1.0.0', '系统版本'),
('system.init.time', CURRENT_TIMESTAMP::TEXT, '系统初始化时间')
ON CONFLICT (config_key) DO NOTHING;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_sys_config_key ON sys_config(config_key);

-- 设置表注释
COMMENT ON TABLE sys_config IS '系统配置表';
COMMENT ON COLUMN sys_config.config_key IS '配置键';
COMMENT ON COLUMN sys_config.config_value IS '配置值';
COMMENT ON COLUMN sys_config.config_desc IS '配置描述';

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_time_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为sys_config表创建更新时间触发器
DROP TRIGGER IF EXISTS update_sys_config_updated_time ON sys_config;
CREATE TRIGGER update_sys_config_updated_time
    BEFORE UPDATE ON sys_config
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_time_column();

-- 输出初始化完成信息
DO $$
BEGIN
    RAISE NOTICE '数据库初始化完成！';
    RAISE NOTICE '数据库名称: %', current_database();
    RAISE NOTICE '当前时间: %', CURRENT_TIMESTAMP;
END
$$;
