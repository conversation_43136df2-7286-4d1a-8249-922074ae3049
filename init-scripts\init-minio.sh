#!/bin/bash

# MinIO 初始化脚本
# 用于创建默认的存储桶和设置策略

set -e

echo "开始初始化 MinIO..."

# 等待 MinIO 服务启动
echo "等待 MinIO 服务启动..."
until curl -f http://minio:9000/minio/health/live; do
    echo "等待 MinIO 启动..."
    sleep 5
done

echo "MinIO 服务已启动，开始配置..."

# 设置 MinIO 客户端别名
mc alias set myminio http://minio:9000 ${MINIO_ROOT_USER:-admin} ${MINIO_ROOT_PASSWORD:-admin123456}

# 创建存储桶
BUCKETS=("images" "documents" "backups" "temp")

for bucket in "${BUCKETS[@]}"; do
    if mc ls myminio/$bucket > /dev/null 2>&1; then
        echo "存储桶 $bucket 已存在"
    else
        echo "创建存储桶: $bucket"
        mc mb myminio/$bucket
        
        # 设置存储桶策略为公共读取
        if [ "$bucket" = "images" ]; then
            echo "设置 $bucket 存储桶为公共读取"
            mc policy set public myminio/$bucket
        fi
    fi
done

# 创建用户和访问密钥（可选）
echo "MinIO 配置完成！"

# 显示存储桶列表
echo "当前存储桶列表："
mc ls myminio

echo "MinIO 初始化完成！"
