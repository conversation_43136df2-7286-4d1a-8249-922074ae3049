#!/bin/bash

# AI指标管控平台服务监控脚本
# 用于监控各个服务的健康状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 服务列表
SERVICES=("postgres" "redis" "minio" "backend" "frontend")

# 检查服务是否运行
check_service_running() {
    local service=$1
    if docker-compose ps -q $service | grep -q .; then
        return 0
    else
        return 1
    fi
}

# 检查服务健康状态
check_service_health() {
    local service=$1
    local health_status=$(docker inspect --format='{{.State.Health.Status}}' "ai-indicator-$service" 2>/dev/null || echo "no-health-check")
    
    case $health_status in
        "healthy")
            echo -e "${GREEN}✓ 健康${NC}"
            return 0
            ;;
        "unhealthy")
            echo -e "${RED}✗ 不健康${NC}"
            return 1
            ;;
        "starting")
            echo -e "${YELLOW}⚠ 启动中${NC}"
            return 2
            ;;
        "no-health-check")
            # 对于没有健康检查的服务，检查是否运行
            if check_service_running $service; then
                echo -e "${BLUE}- 运行中${NC}"
                return 0
            else
                echo -e "${RED}✗ 未运行${NC}"
                return 1
            fi
            ;;
        *)
            echo -e "${YELLOW}? 未知${NC}"
            return 3
            ;;
    esac
}

# 检查端口是否可访问
check_port() {
    local host=$1
    local port=$2
    local timeout=${3:-5}
    
    if timeout $timeout bash -c "echo >/dev/tcp/$host/$port" 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# 检查HTTP端点
check_http_endpoint() {
    local url=$1
    local timeout=${2:-10}
    
    if curl -f -s --max-time $timeout "$url" >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 显示服务状态表格
show_service_status() {
    echo -e "${BLUE}=================================================="
    echo "           AI指标管控平台服务状态监控"
    echo "==================================================${NC}"
    echo ""
    
    printf "%-15s %-10s %-15s %-20s\n" "服务名称" "运行状态" "健康状态" "端点检查"
    echo "------------------------------------------------------------"
    
    local overall_status=0
    
    for service in "${SERVICES[@]}"; do
        printf "%-15s " "$service"
        
        # 检查运行状态
        if check_service_running $service; then
            printf "${GREEN}%-10s${NC} " "运行中"
            
            # 检查健康状态
            check_service_health $service
            local health_code=$?
            
            # 检查端点
            case $service in
                "postgres")
                    if check_port localhost 5432; then
                        printf "${GREEN}%-20s${NC}" "端口5432可访问"
                    else
                        printf "${RED}%-20s${NC}" "端口5432不可访问"
                        overall_status=1
                    fi
                    ;;
                "redis")
                    if check_port localhost 6379; then
                        printf "${GREEN}%-20s${NC}" "端口6379可访问"
                    else
                        printf "${RED}%-20s${NC}" "端口6379不可访问"
                        overall_status=1
                    fi
                    ;;
                "minio")
                    if check_http_endpoint "http://localhost:9000/minio/health/live"; then
                        printf "${GREEN}%-20s${NC}" "API端点正常"
                    else
                        printf "${RED}%-20s${NC}" "API端点异常"
                        overall_status=1
                    fi
                    ;;
                "backend")
                    if check_http_endpoint "http://localhost:8080/actuator/health"; then
                        printf "${GREEN}%-20s${NC}" "健康检查通过"
                    else
                        printf "${RED}%-20s${NC}" "健康检查失败"
                        overall_status=1
                    fi
                    ;;
                "frontend")
                    if check_http_endpoint "http://localhost:80/health"; then
                        printf "${GREEN}%-20s${NC}" "Web服务正常"
                    else
                        printf "${RED}%-20s${NC}" "Web服务异常"
                        overall_status=1
                    fi
                    ;;
            esac
            
            if [ $health_code -eq 1 ]; then
                overall_status=1
            fi
        else
            printf "${RED}%-10s${NC} " "未运行"
            printf "${RED}%-15s${NC} " "✗ 未运行"
            printf "${RED}%-20s${NC}" "服务未启动"
            overall_status=1
        fi
        
        echo ""
    done
    
    echo ""
    
    # 显示总体状态
    if [ $overall_status -eq 0 ]; then
        echo -e "${GREEN}✓ 所有服务运行正常${NC}"
    else
        echo -e "${RED}✗ 部分服务存在问题${NC}"
    fi
    
    return $overall_status
}

# 显示资源使用情况
show_resource_usage() {
    echo -e "\n${BLUE}资源使用情况:${NC}"
    echo "------------------------------------------------------------"
    
    # Docker 容器资源使用
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}\t{{.NetIO}}\t{{.BlockIO}}" | grep "ai-indicator"
    
    echo ""
    
    # 磁盘使用情况
    echo -e "${BLUE}磁盘使用情况:${NC}"
    df -h | grep -E "(Filesystem|/dev/)"
    
    echo ""
    
    # Docker 卷使用情况
    echo -e "${BLUE}Docker卷使用情况:${NC}"
    docker system df -v | grep -A 10 "Local Volumes"
}

# 显示最近的错误日志
show_recent_errors() {
    echo -e "\n${BLUE}最近的错误日志:${NC}"
    echo "------------------------------------------------------------"
    
    for service in "${SERVICES[@]}"; do
        echo -e "\n${YELLOW}[$service]${NC}"
        docker-compose logs --tail=5 $service 2>/dev/null | grep -i error || echo "无错误日志"
    done
}

# 主函数
main() {
    case "${1:-status}" in
        "status"|"")
            show_service_status
            ;;
        "resources"|"res")
            show_resource_usage
            ;;
        "errors"|"err")
            show_recent_errors
            ;;
        "full")
            show_service_status
            show_resource_usage
            show_recent_errors
            ;;
        "watch")
            while true; do
                clear
                show_service_status
                echo ""
                echo -e "${BLUE}自动刷新中... (Ctrl+C 退出)${NC}"
                sleep 30
            done
            ;;
        "help"|"-h"|"--help")
            echo "AI指标管控平台服务监控脚本"
            echo ""
            echo "使用方法:"
            echo "  $0 [command]"
            echo ""
            echo "命令:"
            echo "  status    显示服务状态 (默认)"
            echo "  resources 显示资源使用情况"
            echo "  errors    显示最近的错误日志"
            echo "  full      显示完整监控信息"
            echo "  watch     持续监控模式"
            echo "  help      显示帮助信息"
            ;;
        *)
            echo -e "${RED}未知命令: $1${NC}"
            echo "使用 '$0 help' 查看帮助信息"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
