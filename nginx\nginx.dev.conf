worker_processes 1;
error_log /var/log/nginx/error.log debug;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 开发环境日志格式
    log_format dev '$remote_addr - $remote_user [$time_local] "$request" '
                   '$status $body_bytes_sent "$http_referer" '
                   '"$http_user_agent" "$http_x_forwarded_for" '
                   'rt=$request_time uct="$upstream_connect_time" '
                   'uht="$upstream_header_time" urt="$upstream_response_time"';

    access_log /var/log/nginx/access.log dev;

    sendfile on;
    keepalive_timeout 65;
    client_max_body_size 100M;

    # 开发环境不启用gzip以便调试
    gzip off;

    # 上游服务器配置
    upstream backend {
        server backend:8080;
    }

    server {
        listen 80;
        server_name localhost;
        root /app/dist;
        index index.html;

        # 开发环境禁用缓存
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";

        # 前端静态资源
        location / {
            try_files $uri $uri/ /index.html;
        }

        # API 代理 - 开发环境详细日志
        location /api/ {
            proxy_pass http://backend/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 开发环境较短的超时时间
            proxy_connect_timeout 10s;
            proxy_send_timeout 10s;
            proxy_read_timeout 10s;
            
            # 禁用缓冲以便实时查看响应
            proxy_buffering off;
        }

        # 认证服务代理
        location /blade-auth/ {
            proxy_pass http://backend/blade-auth/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_connect_timeout 10s;
            proxy_send_timeout 10s;
            proxy_read_timeout 10s;
            proxy_buffering off;
        }

        # 健康检查
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # 错误页面
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /app/dist;
        }
    }
}
