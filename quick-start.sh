#!/bin/bash

# AI指标管控平台快速启动脚本
# 用于首次部署和快速启动

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}"
echo "=================================================="
echo "    AI指标管控平台 Docker Compose 快速启动"
echo "=================================================="
echo -e "${NC}"

# 检查是否为首次运行
if [ ! -f .env ]; then
    echo -e "${YELLOW}检测到首次运行，正在初始化配置...${NC}"
    
    # 复制环境变量模板
    cp .env.example .env
    echo -e "${GREEN}✓ 已创建 .env 配置文件${NC}"
    
    # 提示用户修改配置
    echo -e "${YELLOW}"
    echo "请注意："
    echo "1. 已创建 .env 配置文件，请根据需要修改其中的配置"
    echo "2. 特别是数据库密码、Redis密码、MinIO密码等安全配置"
    echo "3. 如果是生产环境，请修改 SERVER_IP 为实际的服务器IP地址"
    echo -e "${NC}"
    
    read -p "是否现在编辑配置文件？(y/n): " edit_config
    if [ "$edit_config" = "y" ] || [ "$edit_config" = "Y" ]; then
        ${EDITOR:-nano} .env
    fi
fi

# 设置脚本权限
chmod +x deploy.sh

echo -e "${BLUE}选择部署环境:${NC}"
echo "1) 开发环境 (dev) - 适用于本地开发和测试"
echo "2) 生产环境 (prod) - 适用于生产服务器部署"
echo "3) 查看服务状态"
echo "4) 查看日志"
echo "5) 停止所有服务"

read -p "请选择 (1-5): " choice

case $choice in
    1)
        echo -e "${GREEN}开始部署开发环境...${NC}"
        ./deploy.sh dev
        ;;
    2)
        echo -e "${GREEN}开始部署生产环境...${NC}"
        echo -e "${YELLOW}注意：生产环境部署前请确保已正确配置 .env 文件${NC}"
        read -p "确认继续？(y/n): " confirm
        if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
            ./deploy.sh prod
        else
            echo "部署已取消"
            exit 0
        fi
        ;;
    3)
        echo -e "${BLUE}查看服务状态...${NC}"
        ./deploy.sh status
        ;;
    4)
        echo -e "${BLUE}查看服务日志...${NC}"
        ./deploy.sh logs
        ;;
    5)
        echo -e "${YELLOW}停止所有服务...${NC}"
        ./deploy.sh stop
        ;;
    *)
        echo -e "${RED}无效选择${NC}"
        exit 1
        ;;
esac

echo -e "${GREEN}"
echo "=================================================="
echo "                操作完成！"
echo "=================================================="
echo -e "${NC}"

if [ "$choice" = "1" ] || [ "$choice" = "2" ]; then
    echo -e "${BLUE}访问地址:${NC}"
    echo "前端应用: http://localhost"
    echo "后端API: http://localhost:8080"
    echo "MinIO控制台: http://localhost:9001"
    echo ""
    echo -e "${BLUE}管理命令:${NC}"
    echo "./deploy.sh status    # 查看服务状态"
    echo "./deploy.sh logs      # 查看日志"
    echo "./deploy.sh restart   # 重启服务"
    echo "./deploy.sh stop      # 停止服务"
    echo "./deploy.sh backup    # 备份数据"
    echo ""
    echo -e "${YELLOW}提示: 首次启动可能需要几分钟时间来下载镜像和初始化数据库${NC}"
fi
