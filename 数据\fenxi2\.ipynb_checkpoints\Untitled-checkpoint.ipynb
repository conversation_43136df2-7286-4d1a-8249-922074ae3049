{"cells": [{"cell_type": "code", "execution_count": 8, "id": "46eb9aeb-63a6-42b6-97cf-50e2584e61a9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔧 开始新特征工程计算...\n", "📂 读取基础数据...\n", "✅ 数据读取成功: (652, 9)\n", "🧹 数据预处理...\n", "清洗后数据形状: (652, 11)\n", "📊 按月汇总数据...\n", "月度汇总数据形状: (36, 11)\n", "类别特征: ['类别_/_金额', '类别_其他_金额', '类别_办公用品_金额', '类别_印刷费_金额', '类别_计算机耗材_金额', '类别_邮寄费_金额']\n", "📅 创建完整时间序列...\n", "完整特征数据形状: (36, 17)\n", "\n", "🎯 按需求计算特征...\n", "发现类别特征: 6个\n", "📊 1. 计算上个月特征...\n", "📊 2. 计算上2个月特征...\n", "📊 3. 计算上3个月特征...\n", "📊 4. 计算去年同期特征...\n", "📊 5. 计算斜率特征...\n", "📊 6. 计算时间特征...\n", "📊 7. 计算平均值特征...\n", "\n", "🔧 处理缺失值...\n", "\n", "💾 保存新特征工程结果...\n", "✅ 新特征工程完成！\n", "📊 最终数据形状: (36, 65)\n", "📁 输出文件: 新特征工程数据.csv\n", "\n", "📋 新特征统计:\n", "============================================================\n", "基础特征: 7个\n", "上月特征: 10个\n", "上2月特征: 9个\n", "上3月特征: 9个\n", "去年同期特征: 9个\n", "斜率特征: 2个\n", "时间特征: 8个\n", "平均值特征: 2个\n", "增长率特征: 5个\n", "预算特征: 5个\n", "\n", "总特征数: 66\n", "数据时间范围: 2022-01 到 2024-12\n", "\n", "前5行数据预览:\n", "        年月      月度总金额      上月总金额    较上月增长率      前3月斜率\n", "0  2022-01  147810.96       0.00  0.000000      0.000\n", "1  2022-02   93613.40  147810.96 -0.366668      0.000\n", "2  2022-03   79065.35   93613.40 -0.155406 -34372.805\n", "3  2022-04  260733.55   79065.35  2.297697  83560.075\n", "4  2022-05   96994.22  260733.55 -0.627995   8964.435\n", "\n", "✅ 新特征工程脚本执行完成！\n"]}], "source": ["#!/usr/bin/env python\n", "# -*- coding: utf-8 -*-\n", "\"\"\"\n", "办公费用新特征工程脚本\n", "按照具体需求设计的特征计算\n", "\"\"\"\n", "\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"🔧 开始新特征工程计算...\")\n", "\n", "# ==================== 辅助函数 ====================\n", "def calculate_trend_slope(values):\n", "    \"\"\"计算数值序列的线性趋势斜率\"\"\"\n", "    if len(values) < 2 or values.isna().all():\n", "        return 0\n", "    valid_values = values.dropna()\n", "    if len(valid_values) < 2:\n", "        return 0\n", "    x = np.arange(len(valid_values))\n", "    try:\n", "        slope, _ = np.polyfit(x, valid_values, 1)\n", "        return slope\n", "    except:\n", "        return 0\n", "\n", "def calculate_compound_growth_rate(values):\n", "    \"\"\"计算复合月增长率\"\"\"\n", "    if len(values) < 2:\n", "        return 0\n", "    valid_values = values.dropna()\n", "    if len(valid_values) < 2:\n", "        return 0\n", "    \n", "    first_val = valid_values.iloc[0]\n", "    last_val = valid_values.iloc[-1]\n", "    \n", "    if first_val <= 0:\n", "        return 0\n", "    \n", "    periods = len(valid_values) - 1\n", "    if periods <= 0:\n", "        return 0\n", "    \n", "    try:\n", "        compound_rate = (last_val / first_val) ** (1/periods) - 1\n", "        return compound_rate\n", "    except:\n", "        return 0\n", "\n", "def calculate_budget_deviation(actual_amount, budget_amount):\n", "    \"\"\"计算预算偏差\"\"\"\n", "    if budget_amount <= 0:\n", "        return 0\n", "    return (actual_amount - budget_amount) / budget_amount\n", "\n", "# ==================== 读取数据 ====================\n", "print(\"📂 读取基础数据...\")\n", "try:\n", "    df = pd.read_csv('办公费用.csv', encoding='utf-8')\n", "    print(f\"✅ 数据读取成功: {df.shape}\")\n", "except Exception as e:\n", "    print(f\"❌ 数据读取失败: {e}\")\n", "    exit()\n", "\n", "# ==================== 数据预处理 ====================\n", "print(\"🧹 数据预处理...\")\n", "# 处理金额字段\n", "df['金额'] = df['金额'].astype(str).str.replace(',', '').astype(float)\n", "# 处理类别统一\n", "if '类别' in df.columns:\n", "    df['类别'] = df['类别'].replace('邮寄', '邮寄费')\n", "else:\n", "    df['类别'] = '默认类别'\n", "\n", "# 生成标准化日期字段\n", "df['年月'] = df['年'].astype(str) + '-' + df['月'].astype(str).str.zfill(2)\n", "df['日期'] = pd.to_datetime(df['年月'] + '-01')\n", "\n", "print(f\"清洗后数据形状: {df.shape}\")\n", "\n", "# ==================== 按月汇总基础数据 ====================\n", "print(\"📊 按月汇总数据...\")\n", "# 按月汇总总金额\n", "monthly_summary = df.groupby('年月').agg({\n", "    '金额': ['sum', 'mean', 'count', 'std']\n", "}).round(2)\n", "monthly_summary.columns = ['月度总金额', '月度平均金额', '月度交易次数', '月度金额标准差']\n", "\n", "# 按月按类别汇总\n", "category_pivot = df.groupby(['年月', '类别'])['金额'].sum().unstack(fill_value=0)\n", "category_pivot.columns = [f'类别_{col}_金额' for col in category_pivot.columns]\n", "\n", "# 合并数据\n", "monthly_data = monthly_summary.join(category_pivot, how='left').fillna(0)\n", "monthly_data = monthly_data.reset_index()\n", "\n", "print(f\"月度汇总数据形状: {monthly_data.shape}\")\n", "print(\"类别特征:\", [col for col in monthly_data.columns if '类别_' in col])\n", "\n", "# ==================== 创建完整时间序列 ====================\n", "print(\"📅 创建完整时间序列...\")\n", "start_date = monthly_data['年月'].min()\n", "end_date = '2024-12'  # 扩展到2024年12月\n", "date_range = pd.date_range(start=pd.to_datetime(start_date + '-01'), \n", "                          end=pd.to_datetime(end_date + '-01'), \n", "                          freq='MS')\n", "\n", "# 创建基础时间框架\n", "base_df = pd.DataFrame({\n", "    '年月': date_range.strftime('%Y-%m'),\n", "    '年': date_range.year,\n", "    '月': date_range.month,\n", "    '季度': date_range.quarter,\n", "    '日期': date_range\n", "})\n", "\n", "# 合并月度数据\n", "feature_df = base_df.merge(monthly_data, on='年月', how='left')\n", "feature_df = feature_df.fillna(0)\n", "\n", "# 添加年度预算（基于历史数据计算）\n", "yearly_budget = monthly_data.groupby(monthly_data['年月'].str[:4])['月度总金额'].sum().to_dict()\n", "avg_budget = np.mean(list(yearly_budget.values())) if yearly_budget else 1000000\n", "feature_df['年度预算'] = feature_df['年'].map(yearly_budget).fillna(avg_budget)\n", "feature_df['月度预算'] = feature_df['年度预算'] / 12\n", "\n", "print(f\"完整特征数据形状: {feature_df.shape}\")\n", "\n", "# ==================== 按需求计算特征 ====================\n", "print(\"\\n🎯 按需求计算特征...\")\n", "feature_df = feature_df.sort_values('日期')\n", "\n", "# 获取类别特征列\n", "category_cols = [col for col in feature_df.columns if col.startswith('类别_') and col.endswith('_金额')]\n", "print(f\"发现类别特征: {len(category_cols)}个\")\n", "\n", "# 1. 上个月特征\n", "print(\"📊 1. 计算上个月特征...\")\n", "feature_df['上月总金额'] = feature_df['月度总金额'].shift(1)\n", "\n", "# 上个月各类别金额\n", "for col in category_cols:\n", "    new_col_name = col.replace('类别_', '上月_')\n", "    feature_df[new_col_name] = feature_df[col].shift(1)\n", "\n", "# 上个月累计预算偏差\n", "feature_df['上月预算偏差'] = feature_df.apply(\n", "    lambda row: calculate_budget_deviation(row['上月总金额'], row['月度预算']), axis=1\n", ")\n", "\n", "# 上个月复合月增长率\n", "feature_df['上月复合增长率'] = feature_df['月度总金额'].rolling(window=3).apply(\n", "    lambda x: calculate_compound_growth_rate(x), raw=False\n", ").shift(1)\n", "\n", "# 较上个月的增长率\n", "feature_df['较上月增长率'] = feature_df['月度总金额'].pct_change()\n", "\n", "# 2. 上2个月特征\n", "print(\"📊 2. 计算上2个月特征...\")\n", "feature_df['上2月总金额'] = feature_df['月度总金额'].shift(2)\n", "\n", "# 上2个月各类别金额\n", "for col in category_cols:\n", "    new_col_name = col.replace('类别_', '上2月_')\n", "    feature_df[new_col_name] = feature_df[col].shift(2)\n", "\n", "# 上2个月累计预算偏差\n", "feature_df['上2月预算偏差'] = feature_df.apply(\n", "    lambda row: calculate_budget_deviation(row['上2月总金额'], row['月度预算']), axis=1\n", ")\n", "\n", "# 上2个月复合月增长率\n", "feature_df['上2月复合增长率'] = feature_df['月度总金额'].rolling(window=3).apply(\n", "    lambda x: calculate_compound_growth_rate(x), raw=False\n", ").shift(2)\n", "\n", "# 3. 上3个月特征\n", "print(\"📊 3. 计算上3个月特征...\")\n", "feature_df['上3月总金额'] = feature_df['月度总金额'].shift(3)\n", "\n", "# 上3个月各类别金额\n", "for col in category_cols:\n", "    new_col_name = col.replace('类别_', '上3月_')\n", "    feature_df[new_col_name] = feature_df[col].shift(3)\n", "\n", "# 上3个月累计预算偏差\n", "feature_df['上3月预算偏差'] = feature_df.apply(\n", "    lambda row: calculate_budget_deviation(row['上3月总金额'], row['月度预算']), axis=1\n", ")\n", "\n", "# 上3个月复合月增长率\n", "feature_df['上3月复合增长率'] = feature_df['月度总金额'].rolling(window=3).apply(\n", "    lambda x: calculate_compound_growth_rate(x), raw=False\n", ").shift(3)\n", "\n", "# 4. 去年同期特征\n", "print(\"📊 4. 计算去年同期特征...\")\n", "feature_df['去年同期总金额'] = feature_df['月度总金额'].shift(12)\n", "\n", "# 去年同期各类别金额\n", "for col in category_cols:\n", "    new_col_name = col.replace('类别_', '去年同期_')\n", "    feature_df[new_col_name] = feature_df[col].shift(12)\n", "\n", "# 较去年同期增长率\n", "feature_df['较去年同期增长率'] = ((feature_df['月度总金额'] - feature_df['去年同期总金额']) / \n", "                              (feature_df['去年同期总金额'] + 1e-8))\n", "\n", "# 5. 斜率特征\n", "print(\"📊 5. 计算斜率特征...\")\n", "# 前3个月总金额的斜率\n", "feature_df['前3月斜率'] = feature_df['月度总金额'].rolling(window=3).apply(\n", "    lambda x: calculate_trend_slope(x), raw=False\n", ")\n", "\n", "# 前6个月总金额的斜率\n", "feature_df['前6月斜率'] = feature_df['月度总金额'].rolling(window=6).apply(\n", "    lambda x: calculate_trend_slope(x), raw=False\n", ")\n", "\n", "# 6. 时间特征\n", "print(\"📊 6. 计算时间特征...\")\n", "feature_df['是否年初'] = (feature_df['月'] <= 3).astype(int)\n", "feature_df['是否年中'] = ((feature_df['月'] >= 4) & (feature_df['月'] <= 9)).astype(int)\n", "feature_df['是否年末'] = (feature_df['月'] >= 10).astype(int)\n", "feature_df['是否上半年'] = (feature_df['月'] <= 6).astype(int)\n", "feature_df['是否第一季度'] = (feature_df['季度'] == 1).astype(int)\n", "feature_df['是否第二季度'] = (feature_df['季度'] == 2).astype(int)\n", "feature_df['是否第三季度'] = (feature_df['季度'] == 3).astype(int)\n", "feature_df['是否第四季度'] = (feature_df['季度'] == 4).astype(int)\n", "\n", "# 7. 平均值特征\n", "print(\"📊 7. 计算平均值特征...\")\n", "# 前3个月总金额平均值\n", "feature_df['前3月平均值'] = feature_df['月度总金额'].rolling(window=3).mean()\n", "\n", "# 去年同期三个月平均值\n", "feature_df['去年同期3月平均值'] = feature_df['前3月平均值'].shift(12)\n", "\n", "# ==================== 处理缺失值 ====================\n", "print(\"\\n🔧 处理缺失值...\")\n", "numeric_cols = feature_df.select_dtypes(include=[np.number]).columns\n", "feature_df[numeric_cols] = feature_df[numeric_cols].fillna(0)\n", "\n", "# ==================== 输出结果 ====================\n", "print(\"\\n💾 保存新特征工程结果...\")\n", "output_file = '新特征工程数据.csv'\n", "feature_df.to_csv(output_file, index=False, encoding='utf-8-sig')\n", "\n", "print(f\"✅ 新特征工程完成！\")\n", "print(f\"📊 最终数据形状: {feature_df.shape}\")\n", "print(f\"📁 输出文件: {output_file}\")\n", "\n", "# ==================== 特征统计 ====================\n", "print(\"\\n📋 新特征统计:\")\n", "print(\"=\" * 60)\n", "\n", "feature_categories = {\n", "    '基础特征': ['年月', '年', '月', '季度', '月度总金额', '月度平均金额', '月度交易次数'],\n", "    '上月特征': [col for col in feature_df.columns if '上月' in col],\n", "    '上2月特征': [col for col in feature_df.columns if '上2月' in col],\n", "    '上3月特征': [col for col in feature_df.columns if '上3月' in col],\n", "    '去年同期特征': [col for col in feature_df.columns if '去年同期' in col],\n", "    '斜率特征': [col for col in feature_df.columns if '斜率' in col],\n", "    '时间特征': [col for col in feature_df.columns if '是否' in col],\n", "    '平均值特征': [col for col in feature_df.columns if '平均值' in col],\n", "    '增长率特征': [col for col in feature_df.columns if '增长率' in col],\n", "    '预算特征': [col for col in feature_df.columns if '预算' in col]\n", "}\n", "\n", "total_features = 0\n", "for category, features in feature_categories.items():\n", "    if features:\n", "        print(f\"{category}: {len(features)}个\")\n", "        total_features += len(features)\n", "\n", "print(f\"\\n总特征数: {total_features}\")\n", "print(f\"数据时间范围: {feature_df['年月'].min()} 到 {feature_df['年月'].max()}\")\n", "\n", "print(\"\\n前5行数据预览:\")\n", "print(feature_df[['年月', '月度总金额', '上月总金额', '较上月增长率', '前3月斜率']].head())\n", "\n", "print(\"\\n✅ 新特征工程脚本执行完成！\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "9626fed5-3cc9-4cb8-b7e1-a07ff55b64cf", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}