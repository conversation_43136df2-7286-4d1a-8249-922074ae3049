{"cells": [{"cell_type": "code", "execution_count": 8, "id": "46eb9aeb-63a6-42b6-97cf-50e2584e61a9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔧 开始新特征工程计算...\n", "📂 读取基础数据...\n", "✅ 数据读取成功: (652, 9)\n", "🧹 数据预处理...\n", "清洗后数据形状: (652, 11)\n", "📊 按月汇总数据...\n", "月度汇总数据形状: (36, 11)\n", "类别特征: ['类别_/_金额', '类别_其他_金额', '类别_办公用品_金额', '类别_印刷费_金额', '类别_计算机耗材_金额', '类别_邮寄费_金额']\n", "📅 创建完整时间序列...\n", "完整特征数据形状: (36, 17)\n", "\n", "🎯 按需求计算特征...\n", "发现类别特征: 6个\n", "📊 1. 计算上个月特征...\n", "📊 2. 计算上2个月特征...\n", "📊 3. 计算上3个月特征...\n", "📊 4. 计算去年同期特征...\n", "📊 5. 计算斜率特征...\n", "📊 6. 计算时间特征...\n", "📊 7. 计算平均值特征...\n", "\n", "🔧 处理缺失值...\n", "\n", "💾 保存新特征工程结果...\n", "✅ 新特征工程完成！\n", "📊 最终数据形状: (36, 65)\n", "📁 输出文件: 新特征工程数据.csv\n", "\n", "📋 新特征统计:\n", "============================================================\n", "基础特征: 7个\n", "上月特征: 10个\n", "上2月特征: 9个\n", "上3月特征: 9个\n", "去年同期特征: 9个\n", "斜率特征: 2个\n", "时间特征: 8个\n", "平均值特征: 2个\n", "增长率特征: 5个\n", "预算特征: 5个\n", "\n", "总特征数: 66\n", "数据时间范围: 2022-01 到 2024-12\n", "\n", "前5行数据预览:\n", "        年月      月度总金额      上月总金额    较上月增长率      前3月斜率\n", "0  2022-01  147810.96       0.00  0.000000      0.000\n", "1  2022-02   93613.40  147810.96 -0.366668      0.000\n", "2  2022-03   79065.35   93613.40 -0.155406 -34372.805\n", "3  2022-04  260733.55   79065.35  2.297697  83560.075\n", "4  2022-05   96994.22  260733.55 -0.627995   8964.435\n", "\n", "✅ 新特征工程脚本执行完成！\n"]}], "source": ["#!/usr/bin/env python\n", "# -*- coding: utf-8 -*-\n", "\"\"\"\n", "办公费用新特征工程脚本\n", "按照具体需求设计的特征计算\n", "\"\"\"\n", "\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"🔧 开始新特征工程计算...\")\n", "\n", "# ==================== 辅助函数 ====================\n", "def calculate_trend_slope(values):\n", "    \"\"\"计算数值序列的线性趋势斜率\"\"\"\n", "    if len(values) < 2 or values.isna().all():\n", "        return 0\n", "    valid_values = values.dropna()\n", "    if len(valid_values) < 2:\n", "        return 0\n", "    x = np.arange(len(valid_values))\n", "    try:\n", "        slope, _ = np.polyfit(x, valid_values, 1)\n", "        return slope\n", "    except:\n", "        return 0\n", "\n", "def calculate_compound_growth_rate(values):\n", "    \"\"\"计算复合月增长率\"\"\"\n", "    if len(values) < 2:\n", "        return 0\n", "    valid_values = values.dropna()\n", "    if len(valid_values) < 2:\n", "        return 0\n", "    \n", "    first_val = valid_values.iloc[0]\n", "    last_val = valid_values.iloc[-1]\n", "    \n", "    if first_val <= 0:\n", "        return 0\n", "    \n", "    periods = len(valid_values) - 1\n", "    if periods <= 0:\n", "        return 0\n", "    \n", "    try:\n", "        compound_rate = (last_val / first_val) ** (1/periods) - 1\n", "        return compound_rate\n", "    except:\n", "        return 0\n", "\n", "def calculate_budget_deviation(actual_amount, budget_amount):\n", "    \"\"\"计算预算偏差\"\"\"\n", "    if budget_amount <= 0:\n", "        return 0\n", "    return (actual_amount - budget_amount) / budget_amount\n", "\n", "# ==================== 读取数据 ====================\n", "print(\"📂 读取基础数据...\")\n", "try:\n", "    df = pd.read_csv('办公费用.csv', encoding='utf-8')\n", "    print(f\"✅ 数据读取成功: {df.shape}\")\n", "except Exception as e:\n", "    print(f\"❌ 数据读取失败: {e}\")\n", "    exit()\n", "\n", "# ==================== 数据预处理 ====================\n", "print(\"🧹 数据预处理...\")\n", "# 处理金额字段\n", "df['金额'] = df['金额'].astype(str).str.replace(',', '').astype(float)\n", "# 处理类别统一\n", "if '类别' in df.columns:\n", "    df['类别'] = df['类别'].replace('邮寄', '邮寄费')\n", "else:\n", "    df['类别'] = '默认类别'\n", "\n", "# 生成标准化日期字段\n", "df['年月'] = df['年'].astype(str) + '-' + df['月'].astype(str).str.zfill(2)\n", "df['日期'] = pd.to_datetime(df['年月'] + '-01')\n", "\n", "print(f\"清洗后数据形状: {df.shape}\")\n", "\n", "# ==================== 按月汇总基础数据 ====================\n", "print(\"📊 按月汇总数据...\")\n", "# 按月汇总总金额\n", "monthly_summary = df.groupby('年月').agg({\n", "    '金额': ['sum', 'mean', 'count', 'std']\n", "}).round(2)\n", "monthly_summary.columns = ['月度总金额', '月度平均金额', '月度交易次数', '月度金额标准差']\n", "\n", "# 按月按类别汇总\n", "category_pivot = df.groupby(['年月', '类别'])['金额'].sum().unstack(fill_value=0)\n", "category_pivot.columns = [f'类别_{col}_金额' for col in category_pivot.columns]\n", "\n", "# 合并数据\n", "monthly_data = monthly_summary.join(category_pivot, how='left').fillna(0)\n", "monthly_data = monthly_data.reset_index()\n", "\n", "print(f\"月度汇总数据形状: {monthly_data.shape}\")\n", "print(\"类别特征:\", [col for col in monthly_data.columns if '类别_' in col])\n", "\n", "# ==================== 创建完整时间序列 ====================\n", "print(\"📅 创建完整时间序列...\")\n", "start_date = monthly_data['年月'].min()\n", "end_date = '2024-12'  # 扩展到2024年12月\n", "date_range = pd.date_range(start=pd.to_datetime(start_date + '-01'), \n", "                          end=pd.to_datetime(end_date + '-01'), \n", "                          freq='MS')\n", "\n", "# 创建基础时间框架\n", "base_df = pd.DataFrame({\n", "    '年月': date_range.strftime('%Y-%m'),\n", "    '年': date_range.year,\n", "    '月': date_range.month,\n", "    '季度': date_range.quarter,\n", "    '日期': date_range\n", "})\n", "\n", "# 合并月度数据\n", "feature_df = base_df.merge(monthly_data, on='年月', how='left')\n", "feature_df = feature_df.fillna(0)\n", "\n", "# 添加年度预算（基于历史数据计算）\n", "yearly_budget = monthly_data.groupby(monthly_data['年月'].str[:4])['月度总金额'].sum().to_dict()\n", "avg_budget = np.mean(list(yearly_budget.values())) if yearly_budget else 1000000\n", "feature_df['年度预算'] = feature_df['年'].map(yearly_budget).fillna(avg_budget)\n", "feature_df['月度预算'] = feature_df['年度预算'] / 12\n", "\n", "print(f\"完整特征数据形状: {feature_df.shape}\")\n", "\n", "# ==================== 按需求计算特征 ====================\n", "print(\"\\n🎯 按需求计算特征...\")\n", "feature_df = feature_df.sort_values('日期')\n", "\n", "# 获取类别特征列\n", "category_cols = [col for col in feature_df.columns if col.startswith('类别_') and col.endswith('_金额')]\n", "print(f\"发现类别特征: {len(category_cols)}个\")\n", "\n", "# 1. 上个月特征\n", "print(\"📊 1. 计算上个月特征...\")\n", "feature_df['上月总金额'] = feature_df['月度总金额'].shift(1)\n", "\n", "# 上个月各类别金额\n", "for col in category_cols:\n", "    new_col_name = col.replace('类别_', '上月_')\n", "    feature_df[new_col_name] = feature_df[col].shift(1)\n", "\n", "# 上个月累计预算偏差\n", "feature_df['上月预算偏差'] = feature_df.apply(\n", "    lambda row: calculate_budget_deviation(row['上月总金额'], row['月度预算']), axis=1\n", ")\n", "\n", "# 上个月复合月增长率\n", "feature_df['上月复合增长率'] = feature_df['月度总金额'].rolling(window=3).apply(\n", "    lambda x: calculate_compound_growth_rate(x), raw=False\n", ").shift(1)\n", "\n", "# 较上个月的增长率\n", "feature_df['较上月增长率'] = feature_df['月度总金额'].pct_change()\n", "\n", "# 2. 上2个月特征\n", "print(\"📊 2. 计算上2个月特征...\")\n", "feature_df['上2月总金额'] = feature_df['月度总金额'].shift(2)\n", "\n", "# 上2个月各类别金额\n", "for col in category_cols:\n", "    new_col_name = col.replace('类别_', '上2月_')\n", "    feature_df[new_col_name] = feature_df[col].shift(2)\n", "\n", "# 上2个月累计预算偏差\n", "feature_df['上2月预算偏差'] = feature_df.apply(\n", "    lambda row: calculate_budget_deviation(row['上2月总金额'], row['月度预算']), axis=1\n", ")\n", "\n", "# 上2个月复合月增长率\n", "feature_df['上2月复合增长率'] = feature_df['月度总金额'].rolling(window=3).apply(\n", "    lambda x: calculate_compound_growth_rate(x), raw=False\n", ").shift(2)\n", "\n", "# 3. 上3个月特征\n", "print(\"📊 3. 计算上3个月特征...\")\n", "feature_df['上3月总金额'] = feature_df['月度总金额'].shift(3)\n", "\n", "# 上3个月各类别金额\n", "for col in category_cols:\n", "    new_col_name = col.replace('类别_', '上3月_')\n", "    feature_df[new_col_name] = feature_df[col].shift(3)\n", "\n", "# 上3个月累计预算偏差\n", "feature_df['上3月预算偏差'] = feature_df.apply(\n", "    lambda row: calculate_budget_deviation(row['上3月总金额'], row['月度预算']), axis=1\n", ")\n", "\n", "# 上3个月复合月增长率\n", "feature_df['上3月复合增长率'] = feature_df['月度总金额'].rolling(window=3).apply(\n", "    lambda x: calculate_compound_growth_rate(x), raw=False\n", ").shift(3)\n", "\n", "# 4. 去年同期特征\n", "print(\"📊 4. 计算去年同期特征...\")\n", "feature_df['去年同期总金额'] = feature_df['月度总金额'].shift(12)\n", "\n", "# 去年同期各类别金额\n", "for col in category_cols:\n", "    new_col_name = col.replace('类别_', '去年同期_')\n", "    feature_df[new_col_name] = feature_df[col].shift(12)\n", "\n", "# 较去年同期增长率\n", "feature_df['较去年同期增长率'] = ((feature_df['月度总金额'] - feature_df['去年同期总金额']) / \n", "                              (feature_df['去年同期总金额'] + 1e-8))\n", "\n", "# 5. 斜率特征\n", "print(\"📊 5. 计算斜率特征...\")\n", "# 前3个月总金额的斜率\n", "feature_df['前3月斜率'] = feature_df['月度总金额'].rolling(window=3).apply(\n", "    lambda x: calculate_trend_slope(x), raw=False\n", ")\n", "\n", "# 前6个月总金额的斜率\n", "feature_df['前6月斜率'] = feature_df['月度总金额'].rolling(window=6).apply(\n", "    lambda x: calculate_trend_slope(x), raw=False\n", ")\n", "\n", "# 6. 时间特征\n", "print(\"📊 6. 计算时间特征...\")\n", "feature_df['是否年初'] = (feature_df['月'] <= 3).astype(int)\n", "feature_df['是否年中'] = ((feature_df['月'] >= 4) & (feature_df['月'] <= 9)).astype(int)\n", "feature_df['是否年末'] = (feature_df['月'] >= 10).astype(int)\n", "feature_df['是否上半年'] = (feature_df['月'] <= 6).astype(int)\n", "feature_df['是否第一季度'] = (feature_df['季度'] == 1).astype(int)\n", "feature_df['是否第二季度'] = (feature_df['季度'] == 2).astype(int)\n", "feature_df['是否第三季度'] = (feature_df['季度'] == 3).astype(int)\n", "feature_df['是否第四季度'] = (feature_df['季度'] == 4).astype(int)\n", "\n", "# 7. 平均值特征\n", "print(\"📊 7. 计算平均值特征...\")\n", "# 前3个月总金额平均值\n", "feature_df['前3月平均值'] = feature_df['月度总金额'].rolling(window=3).mean()\n", "\n", "# 去年同期三个月平均值\n", "feature_df['去年同期3月平均值'] = feature_df['前3月平均值'].shift(12)\n", "\n", "# ==================== 处理缺失值 ====================\n", "print(\"\\n🔧 处理缺失值...\")\n", "numeric_cols = feature_df.select_dtypes(include=[np.number]).columns\n", "feature_df[numeric_cols] = feature_df[numeric_cols].fillna(0)\n", "\n", "# ==================== 输出结果 ====================\n", "print(\"\\n💾 保存新特征工程结果...\")\n", "output_file = '新特征工程数据.csv'\n", "feature_df.to_csv(output_file, index=False, encoding='utf-8-sig')\n", "\n", "print(f\"✅ 新特征工程完成！\")\n", "print(f\"📊 最终数据形状: {feature_df.shape}\")\n", "print(f\"📁 输出文件: {output_file}\")\n", "\n", "# ==================== 特征统计 ====================\n", "print(\"\\n📋 新特征统计:\")\n", "print(\"=\" * 60)\n", "\n", "feature_categories = {\n", "    '基础特征': ['年月', '年', '月', '季度', '月度总金额', '月度平均金额', '月度交易次数'],\n", "    '上月特征': [col for col in feature_df.columns if '上月' in col],\n", "    '上2月特征': [col for col in feature_df.columns if '上2月' in col],\n", "    '上3月特征': [col for col in feature_df.columns if '上3月' in col],\n", "    '去年同期特征': [col for col in feature_df.columns if '去年同期' in col],\n", "    '斜率特征': [col for col in feature_df.columns if '斜率' in col],\n", "    '时间特征': [col for col in feature_df.columns if '是否' in col],\n", "    '平均值特征': [col for col in feature_df.columns if '平均值' in col],\n", "    '增长率特征': [col for col in feature_df.columns if '增长率' in col],\n", "    '预算特征': [col for col in feature_df.columns if '预算' in col]\n", "}\n", "\n", "total_features = 0\n", "for category, features in feature_categories.items():\n", "    if features:\n", "        print(f\"{category}: {len(features)}个\")\n", "        total_features += len(features)\n", "\n", "print(f\"\\n总特征数: {total_features}\")\n", "print(f\"数据时间范围: {feature_df['年月'].min()} 到 {feature_df['年月'].max()}\")\n", "\n", "print(\"\\n前5行数据预览:\")\n", "print(feature_df[['年月', '月度总金额', '上月总金额', '较上月增长率', '前3月斜率']].head())\n", "\n", "print(\"\\n✅ 新特征工程脚本执行完成！\")\n"]}, {"cell_type": "code", "execution_count": 2, "id": "9626fed5-3cc9-4cb8-b7e1-a07ff55b64cf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 按二级分类统计办公费用...\n", "\n", "✅ 二级分类特征工程完成！\n", "📊 最终数据形状: (335, 36)\n", "📁 输出文件: 二级分类特征工程数据拆分剔除异常值.csv\n", "\n", "📋 二级分类特征统计:\n", "============================================================\n", "基础特征: 8个\n", "时间特征: 6个\n", "上月特征: 4个\n", "上2月特征: 4个\n", "上3月特征: 3个\n", "去年同期特征: 4个\n", "斜率特征: 1个\n", "平均值特征: 1个\n", "增长率特征: 3个\n", "预算特征: 5个\n", "\n", "总特征数: 39\n", "数据时间范围: 2022-01-01 到 2024-12-01\n", "二级分类数量: 15个\n", "\n", "二级分类列表:\n", "1. 其他办公费\t2. 其他印刷\t3. 其他快递\t4. 其他杂项\t5. 办公用品费\t\n", "6. 印章费\t7. 宣传印刷\t8. 无二级分类\t9. 日用品费用\t10. 水费\t\n", "11. 照片打印\t12. 茶叶费\t13. 视频制作\t14. 邮政快递\t15. 顺丰快递\t\n", "\n", "前5行数据预览:\n", "            年月  二级分类      月度总金额      上月总金额      较上月增长率      前3月斜率     预算完成率  \\\n", "0   2022-01-01  其他杂项   55054.46       0.00    0.000000      0.000  0.207485   \n", "13  2022-02-01  其他杂项    1099.00   55054.46   -0.980038      0.000  0.211627   \n", "22  2022-03-01  其他杂项    1325.00    1099.00    0.205641      0.000  0.216621   \n", "31  2022-04-01  其他杂项  161289.05    1325.00  120.727585  31892.977  0.824476   \n", "41  2022-05-01  其他杂项    6943.00  161289.05   -0.956953  17749.605  0.850642   \n", "\n", "        预算偏差  \n", "0   0.124152  \n", "13  0.044961  \n", "22 -0.033379  \n", "31  0.491143  \n", "41  0.433976  \n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "✅ 二级分类特征工程脚本执行完成！\n"]}], "source": ["# ==================== 按二级分类统计办公费用 ====================\n", "print(\"\\n📊 按二级分类统计办公费用...\")\n", "\n", "# 读取带二级分类的数据\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.linear_model import LinearRegression\n", "import calendar\n", "\n", "# 读取带二级分类的数据\n", "df_with_subcategory = pd.read_csv('办公费用_带二级分类 - 剔除异常值并且分摊一些合并数据.csv')\n", "\n", "# 确保日期列格式正确\n", "df_with_subcategory['年月'] = df_with_subcategory['年月'].astype(str)\n", "\n", "# 按年月和二级分类进行分组统计\n", "monthly_subcategory_stats = df_with_subcategory.groupby(['年月', '二级分类']).agg({\n", "    '金额': ['sum', 'mean', 'count']\n", "}).reset_index()\n", "\n", "# 调整列名\n", "monthly_subcategory_stats.columns = ['年月', '二级分类', '月度总金额', '月度平均金额', '月度交易次数']\n", "\n", "# 创建特征工程函数\n", "def create_subcategory_features(df):\n", "    # 复制数据框\n", "    feature_df = df.copy()\n", "    \n", "    # 创建时间特征\n", "    feature_df['年'] = feature_df['年月'].str[:4].astype(int)\n", "    feature_df['月'] = feature_df['年月'].str[5:7].astype(int)\n", "    feature_df['季度'] = ((feature_df['月'] - 1) // 3 + 1).astype(int)\n", "    \n", "    # 添加更多时间特征\n", "    feature_df['是否月初'] = (feature_df['月'] == 1).astype(int)\n", "    feature_df['是否月末'] = feature_df.apply(lambda x: x['月'] == 12, axis=1).astype(int)\n", "    feature_df['是否季初'] = (feature_df['月'] % 3 == 1).astype(int)\n", "    feature_df['是否季末'] = (feature_df['月'] % 3 == 0).astype(int)\n", "    feature_df['是否上半年'] = (feature_df['月'] <= 6).astype(int)\n", "    feature_df['月天数'] = feature_df.apply(lambda x: calendar.monthrange(x['年'], x['月'])[1], axis=1)\n", "    \n", "    # 创建上月特征\n", "    year_month_subcategory = feature_df[['年月', '二级分类', '月度总金额', '月度平均金额', '月度交易次数']]\n", "    \n", "    # 创建所有年月和二级分类的组合\n", "    all_year_months = sorted(feature_df['年月'].unique())\n", "    all_subcategories = sorted(feature_df['二级分类'].unique())\n", "    \n", "    # 创建上月、上2月、上3月特征\n", "    for i, ym in enumerate(all_year_months):\n", "        year, month = int(ym.split('-')[0]), int(ym.split('-')[1])\n", "        \n", "        # 上月\n", "        if i > 0:\n", "            prev_ym = all_year_months[i-1]\n", "            prev_data = year_month_subcategory[year_month_subcategory['年月'] == prev_ym]\n", "            \n", "            for _, row in prev_data.iterrows():\n", "                mask = (feature_df['年月'] == ym) & (feature_df['二级分类'] == row['二级分类'])\n", "                feature_df.loc[mask, '上月总金额'] = row['月度总金额']\n", "                feature_df.loc[mask, '上月平均金额'] = row['月度平均金额']\n", "                feature_df.loc[mask, '上月交易次数'] = row['月度交易次数']\n", "        \n", "        # 上2月\n", "        if i > 1:\n", "            prev2_ym = all_year_months[i-2]\n", "            prev2_data = year_month_subcategory[year_month_subcategory['年月'] == prev2_ym]\n", "            \n", "            for _, row in prev2_data.iterrows():\n", "                mask = (feature_df['年月'] == ym) & (feature_df['二级分类'] == row['二级分类'])\n", "                feature_df.loc[mask, '上2月总金额'] = row['月度总金额']\n", "                feature_df.loc[mask, '上2月平均金额'] = row['月度平均金额']\n", "                feature_df.loc[mask, '上2月交易次数'] = row['月度交易次数']\n", "        \n", "        # 上3月\n", "        if i > 2:\n", "            prev3_ym = all_year_months[i-3]\n", "            prev3_data = year_month_subcategory[year_month_subcategory['年月'] == prev3_ym]\n", "            \n", "            for _, row in prev3_data.iterrows():\n", "                mask = (feature_df['年月'] == ym) & (feature_df['二级分类'] == row['二级分类'])\n", "                feature_df.loc[mask, '上3月总金额'] = row['月度总金额']\n", "                feature_df.loc[mask, '上3月平均金额'] = row['月度平均金额']\n", "                feature_df.loc[mask, '上3月交易次数'] = row['月度交易次数']\n", "        \n", "        # 去年同期\n", "        last_year_ym = f\"{year-1}-{month:02d}\"\n", "        if last_year_ym in all_year_months:\n", "            last_year_data = year_month_subcategory[year_month_subcategory['年月'] == last_year_ym]\n", "            \n", "            for _, row in last_year_data.iterrows():\n", "                mask = (feature_df['年月'] == ym) & (feature_df['二级分类'] == row['二级分类'])\n", "                feature_df.loc[mask, '去年同期总金额'] = row['月度总金额']\n", "                feature_df.loc[mask, '去年同期平均金额'] = row['月度平均金额']\n", "                feature_df.loc[mask, '去年同期交易次数'] = row['月度交易次数']\n", "        else:\n", "            feature_df['去年同期总金额'] = 0\n", "            feature_df['去年同期平均金额'] = 0\n", "            feature_df['去年同期交易次数'] = 0\n", "    # 计算增长率特征\n", "    feature_df['较上月增长率'] = (feature_df['月度总金额'] - feature_df['上月总金额']) / feature_df['上月总金额'].replace(0, np.nan)\n", "    feature_df['较上2月增长率'] = (feature_df['月度总金额'] - feature_df['上2月总金额']) / feature_df['上2月总金额'].replace(0, np.nan)\n", "    feature_df['较去年同期增长率'] = (feature_df['月度总金额'] - feature_df['去年同期总金额']) / feature_df['去年同期总金额'].replace(0, np.nan)\n", "    \n", "    # 计算3个月平均值\n", "    feature_df['前3月平均值'] = feature_df[['月度总金额', '上月总金额', '上2月总金额']].mean(axis=1, skipna=True)\n", "    \n", "    # 计算斜率特征（趋势）\n", "    def calculate_slope(row):\n", "        y = [row['上3月总金额'], row['上2月总金额'], row['上月总金额'], row['月度总金额']]\n", "        x = np.arange(len(y)).reshape(-1, 1)\n", "        y = np.array(y).reshape(-1, 1)\n", "        if np.isnan(y).any():\n", "            return np.nan\n", "        model = LinearRegression()\n", "        model.fit(x, y)\n", "        return model.coef_[0][0]\n", "    \n", "    # 只对有足够历史数据的行计算斜率\n", "    mask = feature_df['上3月总金额'].notna()\n", "    feature_df.loc[mask, '前3月斜率'] = feature_df[mask].apply(calculate_slope, axis=1)\n", "    \n", "    # 计算预算特征\n", "    # 1. 计算每年每个二级分类的年度预算（当年累计金额）\n", "    yearly_budget = feature_df.groupby(['年', '二级分类'])['月度总金额'].sum().reset_index()\n", "    yearly_budget.rename(columns={'月度总金额': '年度预算'}, inplace=True)\n", "    \n", "    # 将年度预算合并回原数据\n", "    feature_df = pd.merge(feature_df, yearly_budget, on=['年', '二级分类'], how='left')\n", "    \n", "    # 2. 计算到当前月为止的累计金额\n", "    # 首先创建一个辅助列，用于排序\n", "    feature_df['年月排序'] = feature_df['年'].astype(str) + feature_df['月'].astype(str).str.zfill(2)\n", "    \n", "    # 对每个年份和二级分类，计算累计金额\n", "    cumulative_amounts = []\n", "    \n", "    for year in feature_df['年'].unique():\n", "        for category in feature_df['二级分类'].unique():\n", "            # 获取该年该分类的所有数据，并按月份排序\n", "            year_cat_data = feature_df[(feature_df['年'] == year) & (feature_df['二级分类'] == category)].sort_values('月')\n", "            \n", "            if not year_cat_data.empty:\n", "                # 计算累计金额\n", "                year_cat_data['当年累计金额'] = year_cat_data['月度总金额'].cumsum()\n", "                cumulative_amounts.append(year_cat_data)\n", "    \n", "    if cumulative_amounts:\n", "        feature_df = pd.concat(cumulative_amounts)\n", "    \n", "    # 3. 计算预算完成率\n", "    feature_df['预算完成率'] = feature_df['当年累计金额'] / feature_df['年度预算'].replace(0, np.nan)\n", "    \n", "    # 4. 计算预算偏差（实际进度与理论进度的差异）\n", "    feature_df['理论进度'] = feature_df['月'] / 12\n", "    feature_df['预算偏差'] = feature_df['预算完成率'] - feature_df['理论进度']\n", "    \n", "    # 删除辅助列\n", "    feature_df.drop('年月排序', axis=1, inplace=True)\n", "    \n", "    # 处理缺失值\n", "    numeric_cols = feature_df.select_dtypes(include=[np.number]).columns\n", "    feature_df[numeric_cols] = feature_df[numeric_cols].fillna(0)\n", "    \n", "    return feature_df\n", "\n", "# 创建特征\n", "subcategory_features = create_subcategory_features(monthly_subcategory_stats)\n", "\n", "# 保存结果\n", "output_file = '二级分类特征工程数据拆分剔除异常值.csv'\n", "subcategory_features.to_csv(output_file, index=False, encoding='utf-8-sig')\n", "\n", "# 输出统计信息\n", "print(f\"\\n✅ 二级分类特征工程完成！\")\n", "print(f\"📊 最终数据形状: {subcategory_features.shape}\")\n", "print(f\"📁 输出文件: {output_file}\")\n", "\n", "# 特征统计\n", "print(\"\\n📋 二级分类特征统计:\")\n", "print(\"=\" * 60)\n", "\n", "feature_categories = {\n", "    '基础特征': ['年月', '二级分类', '年', '月', '季度', '月度总金额', '月度平均金额', '月度交易次数'],\n", "    '时间特征': [col for col in subcategory_features.columns if '是否' in col] + ['月天数'],\n", "    '上月特征': [col for col in subcategory_features.columns if '上月' in col],\n", "    '上2月特征': [col for col in subcategory_features.columns if '上2月' in col],\n", "    '上3月特征': [col for col in subcategory_features.columns if '上3月' in col],\n", "    '去年同期特征': [col for col in subcategory_features.columns if '去年同期' in col],\n", "    '斜率特征': [col for col in subcategory_features.columns if '斜率' in col],\n", "    '平均值特征': [col for col in subcategory_features.columns if '平均值' in col],\n", "    '增长率特征': [col for col in subcategory_features.columns if '增长率' in col],\n", "    '预算特征': ['年度预算', '当年累计金额', '预算完成率', '理论进度', '预算偏差']\n", "}\n", "\n", "total_features = 0\n", "for category, features in feature_categories.items():\n", "    if features:\n", "        print(f\"{category}: {len(features)}个\")\n", "        total_features += len(features)\n", "\n", "print(f\"\\n总特征数: {total_features}\")\n", "print(f\"数据时间范围: {subcategory_features['年月'].min()} 到 {subcategory_features['年月'].max()}\")\n", "print(f\"二级分类数量: {subcategory_features['二级分类'].nunique()}个\")\n", "\n", "# 显示二级分类列表\n", "print(\"\\n二级分类列表:\")\n", "for i, category in enumerate(sorted(subcategory_features['二级分类'].unique()), 1):\n", "    print(f\"{i}. {category}\", end=\"\\t\")\n", "    if i % 5 == 0:\n", "        print()\n", "if len(subcategory_features['二级分类'].unique()) % 5 != 0:\n", "    print()\n", "\n", "# 显示前5行数据预览\n", "print(\"\\n前5行数据预览:\")\n", "preview_cols = ['年月', '二级分类', '月度总金额', '上月总金额', '较上月增长率', '前3月斜率', '预算完成率', '预算偏差']\n", "available_cols = [col for col in preview_cols if col in subcategory_features.columns]\n", "print(subcategory_features[available_cols].head())\n", "\n", "# 绘制二级分类金额分布图\n", "plt.figure(figsize=(12, 8))\n", "top_categories = subcategory_features.groupby('二级分类')['月度总金额'].sum().nlargest(10)\n", "sns.barplot(x=top_categories.index, y=top_categories.values)\n", "plt.title('前10个二级分类总金额')\n", "plt.xticks(rotation=45, ha='right')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"\\n✅ 二级分类特征工程脚本执行完成！\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}