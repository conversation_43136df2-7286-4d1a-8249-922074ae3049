#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
为办公费用数据创建二级分类
根据类别和摘要内容进行智能分类
"""

import pandas as pd
import numpy as np

def create_subcategory(row):
    """为所有费用类别创建二级分类"""
    category = row['类别']
    summary = str(row['摘要'])
    
    # 办公用品二级分类
    if category == '办公用品':
        if '茶叶' in summary:
            return '茶叶费'
        elif '水' in summary:
            return '水费'
        elif '日用品' in summary:
            return '日用品费用'
        elif '办公用品' in summary:
            return '办公用品费'
        elif '刻章' in summary or '印章' in summary:
            return '印章费'
        else:
            return '其他办公用品'
    
    # 邮寄费二级分类
    elif category == '邮寄费':
        if '顺丰' in summary:
            return '顺丰快递'
        elif '邮政' in summary:
            return '邮政快递'
        else:
            return '其他快递'
    
    # 印刷费二级分类
    elif category == '印刷费':
        if '宣传' in summary:
            return '宣传印刷'
        elif '照片' in summary or '相片' in summary:
            return '照片打印'
        else:
            return '其他印刷'
    
    # 其他费用二级分类
    elif category == '其他':
        if '档案' in summary:
            return '档案整理'
        elif '视频' in summary:
            return '视频制作'
        elif '党' in summary:
            return '党建相关'
        else:
            return '其他杂项'
    
    # 差旅费二级分类（如果有的话）
    elif category == '差旅费':
        if '交通' in summary:
            return '交通费'
        elif '住宿' in summary:
            return '住宿费'
        elif '餐饮' in summary or '伙食' in summary:
            return '餐饮费'
        else:
            return '其他差旅'
    
    # 计算机耗材二级分类
    elif category == '计算机耗材':
        if '硒鼓' in summary or '墨盒' in summary:
            return '打印耗材'
        elif '电脑' in summary or '计算机' in summary:
            return '计算机设备'
        else:
            return '其他耗材'
    
    # 默认返回原类别
    else:
        return f'其他{category}'

def process_office_expenses():
    """处理办公费用数据，添加二级分类"""
    
    print("🔧 开始处理办公费用数据...")
    
    # 读取CSV文件
    try:
        df = pd.read_csv('办公费用.csv', encoding='utf-8')
        print(f"✅ 成功读取数据: {df.shape}")
    except Exception as e:
        print(f"❌ 读取数据失败: {e}")
        return
    
    # 显示原始数据信息
    print(f"\n📊 原始数据信息:")
    print(f"- 数据形状: {df.shape}")
    print(f"- 列名: {list(df.columns)}")
    print(f"- 时间范围: {df['年'].min()}年{df['月'].min()}月 到 {df['年'].max()}年{df['月'].max()}月")
    
    # 处理金额字段（去除逗号）
    if '金额' in df.columns:
        df['金额'] = df['金额'].astype(str).str.replace(',', '').astype(float)
    
    # 显示一级分类统计
    print(f"\n📋 一级分类统计:")
    category_stats = df['类别'].value_counts()
    for category, count in category_stats.items():
        total_amount = df[df['类别'] == category]['金额'].sum()
        print(f"- {category}: {count}笔, 总金额: {total_amount:,.2f}")
    
    # 应用二级分类函数
    print(f"\n🏷️ 创建二级分类...")
    df['二级分类'] = df.apply(create_subcategory, axis=1)
    
    # 显示二级分类统计
    print(f"\n📊 二级分类统计:")
    subcategory_stats = df['二级分类'].value_counts()
    print(f"总共创建了 {len(subcategory_stats)} 个二级分类")
    
    # 按一级分类显示二级分类详情
    categories = df['类别'].unique()
    for category in sorted(categories):
        print(f"\n{category}的二级分类:")
        subcategory_data = df[df['类别'] == category].groupby('二级分类')['金额'].agg(['count', 'sum', 'mean'])
        subcategory_data = subcategory_data.sort_values('sum', ascending=False)
        subcategory_data.columns = ['笔数', '总金额', '平均金额']
        
        for subcategory, stats in subcategory_data.iterrows():
            print(f"  - {subcategory}: {stats['笔数']}笔, 总金额: {stats['总金额']:,.2f}, 平均: {stats['平均金额']:,.2f}")
    
    # 创建年月字段
    df['年月'] = df['年'].astype(str) + '-' + df['月'].astype(str).str.zfill(2)
    
    # 保存包含二级分类的新CSV文件
    output_file = '办公费用_含二级分类.csv'
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n💾 已保存到文件: {output_file}")
    
    # 创建二级分类汇总表
    print(f"\n📈 创建二级分类汇总表...")
    
    # 按年月和二级分类汇总
    monthly_subcategory = df.pivot_table(
        index='年月',
        columns='二级分类',
        values='金额',
        aggfunc='sum'
    ).fillna(0)
    
    # 保存汇总表
    summary_file = '办公费用_二级分类月度汇总.csv'
    monthly_subcategory.to_csv(summary_file, encoding='utf-8-sig')
    print(f"💾 已保存汇总表: {summary_file}")
    
    # 创建一级分类和二级分类的交叉表
    cross_table = pd.crosstab(df['类别'], df['二级分类'], df['金额'], aggfunc='sum').fillna(0)
    cross_table_file = '办公费用_分类交叉表.csv'
    cross_table.to_csv(cross_table_file, encoding='utf-8-sig')
    print(f"💾 已保存交叉表: {cross_table_file}")
    
    # 显示样例数据
    print(f"\n📋 样例数据预览:")
    sample_columns = ['年月', '部门', '摘要', '金额', '类别', '二级分类']
    print(df[sample_columns].head(10))
    
    print(f"\n✅ 二级分类创建完成！")
    print(f"📊 处理结果:")
    print(f"- 原始记录数: {len(df)}")
    print(f"- 一级分类数: {len(df['类别'].unique())}")
    print(f"- 二级分类数: {len(df['二级分类'].unique())}")
    print(f"- 输出文件: {output_file}")
    print(f"- 汇总文件: {summary_file}")
    print(f"- 交叉表文件: {cross_table_file}")
    
    return df

if __name__ == "__main__":
    result_df = process_office_expenses()
