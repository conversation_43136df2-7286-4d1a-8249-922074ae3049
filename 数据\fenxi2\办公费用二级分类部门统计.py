#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
办公费用二级分类部门统计脚本
统计每个二级分类每月不同部门的合计费用
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def create_subcategory(category, summary):
    """为所有费用类别创建二级分类"""
    summary = str(summary)
    
    # 办公用品二级分类
    if category == '办公用品':
        if '茶叶' in summary:
            return '茶叶费'
        elif '水' in summary:
            return '水费'
        elif '日用品' in summary:
            return '日用品费用'
        elif '办公用品' in summary:
            return '办公用品费'
        elif '刻章' in summary or '印章' in summary:
            return '印章费'
        else:
            return '其他办公用品'
    
    # 邮寄费二级分类
    elif category == '邮寄费':
        if '顺丰' in summary:
            return '顺丰快递'
        elif '邮政' in summary:
            return '邮政快递'
        else:
            return '其他快递'
    
    # 印刷费二级分类
    elif category == '印刷费':
        if '宣传' in summary:
            return '宣传印刷'
        elif '照片' in summary or '相片' in summary:
            return '照片打印'
        else:
            return '其他印刷'
    
    # 其他费用二级分类
    elif category == '其他':
        if '档案' in summary:
            return '档案整理'
        elif '视频' in summary:
            return '视频制作'
        elif '党' in summary:
            return '党建相关'
        else:
            return '其他杂项'
    
    # 差旅费二级分类（如果有的话）
    elif category == '差旅费':
        if '交通' in summary:
            return '交通费'
        elif '住宿' in summary:
            return '住宿费'
        elif '餐饮' in summary or '伙食' in summary:
            return '餐饮费'
        else:
            return '其他差旅'
    
    # 计算机耗材二级分类
    elif category == '计算机耗材':
        if '硒鼓' in summary or '墨盒' in summary:
            return '打印耗材'
        elif '电脑' in summary or '计算机' in summary:
            return '计算机设备'
        else:
            return '其他耗材'
    
    # 默认返回原类别
    else:
        return f'其他{category}'

def main():
    print("🔧 开始办公费用二级分类部门统计...")
    
    # ==================== 读取数据 ====================
    print("📂 读取基础数据...")
    try:
        df = pd.read_csv('办公费用.csv', encoding='utf-8')
        print(f"✅ 数据读取成功: {df.shape}")
    except Exception as e:
        print(f"❌ 数据读取失败: {e}")
        return
    
    # ==================== 数据预处理 ====================
    print("🧹 数据预处理...")
    
    # 处理金额字段（去除逗号和引号）
    df['金额'] = df['金额'].astype(str).str.replace(',', '').str.replace('"', '').astype(float)
    
    # 创建年月字段
    df['年月'] = df['年'].astype(str) + '-' + df['月'].astype(str).str.zfill(2)
    
    # 创建二级分类
    print("🏷️ 创建二级分类...")
    df['二级分类'] = df.apply(lambda row: create_subcategory(row['类别'], row['摘要']), axis=1)
    
    print(f"清洗后数据形状: {df.shape}")
    print(f"发现部门: {df['部门'].unique()}")
    print(f"发现一级分类: {df['类别'].unique()}")
    print(f"创建二级分类: {len(df['二级分类'].unique())}个")
    
    # ==================== 按二级分类、年月、部门统计 ====================
    print("📊 按二级分类、年月、部门统计...")
    
    # 创建透视表：二级分类 x 年月 x 部门
    dept_subcategory_monthly = df.pivot_table(
        index=['二级分类', '年月'],
        columns='部门',
        values='金额',
        aggfunc='sum',
        fill_value=0
    )
    
    # 重置索引，使二级分类和年月成为普通列
    dept_subcategory_monthly = dept_subcategory_monthly.reset_index()
    
    # 添加总计列
    dept_columns = [col for col in dept_subcategory_monthly.columns if col not in ['二级分类', '年月']]
    dept_subcategory_monthly['总计'] = dept_subcategory_monthly[dept_columns].sum(axis=1)
    
    print(f"统计结果形状: {dept_subcategory_monthly.shape}")
    
    # ==================== 保存结果 ====================
    print("💾 保存统计结果...")
    
    # 保存主要统计文件
    output_file = '办公费用二级分类部门月度统计.csv'
    dept_subcategory_monthly.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"✅ 主要统计文件已保存: {output_file}")
    
    # ==================== 创建汇总统计 ====================
    print("📈 创建汇总统计...")
    
    # 1. 按二级分类汇总（不分部门）
    subcategory_summary = df.groupby(['二级分类', '年月'])['金额'].sum().reset_index()
    subcategory_summary_pivot = subcategory_summary.pivot(
        index='二级分类',
        columns='年月',
        values='金额'
    ).fillna(0)
    
    # 添加总计列和行
    subcategory_summary_pivot['总计'] = subcategory_summary_pivot.sum(axis=1)
    subcategory_summary_pivot.loc['总计'] = subcategory_summary_pivot.sum()
    
    subcategory_file = '办公费用二级分类月度汇总.csv'
    subcategory_summary_pivot.to_csv(subcategory_file, encoding='utf-8-sig')
    print(f"✅ 二级分类月度汇总已保存: {subcategory_file}")
    
    # 2. 按部门汇总（不分二级分类）
    dept_summary = df.groupby(['部门', '年月'])['金额'].sum().reset_index()
    dept_summary_pivot = dept_summary.pivot(
        index='部门',
        columns='年月',
        values='金额'
    ).fillna(0)
    
    # 添加总计列和行
    dept_summary_pivot['总计'] = dept_summary_pivot.sum(axis=1)
    dept_summary_pivot.loc['总计'] = dept_summary_pivot.sum()
    
    dept_file = '办公费用部门月度汇总.csv'
    dept_summary_pivot.to_csv(dept_file, encoding='utf-8-sig')
    print(f"✅ 部门月度汇总已保存: {dept_file}")
    
    # 3. 创建详细的三维统计表
    print("📋 创建详细统计表...")
    detailed_stats = df.groupby(['年月', '部门', '类别', '二级分类']).agg({
        '金额': ['sum', 'count', 'mean']
    }).round(2)
    
    detailed_stats.columns = ['总金额', '笔数', '平均金额']
    detailed_stats = detailed_stats.reset_index()
    
    detailed_file = '办公费用详细统计表.csv'
    detailed_stats.to_csv(detailed_file, index=False, encoding='utf-8-sig')
    print(f"✅ 详细统计表已保存: {detailed_file}")
    
    # ==================== 显示统计信息 ====================
    print("\n📊 统计信息汇总:")
    print("=" * 60)
    
    print(f"📅 时间范围: {df['年月'].min()} 到 {df['年月'].max()}")
    print(f"🏢 部门数量: {len(df['部门'].unique())}")
    print(f"🏷️ 一级分类数量: {len(df['类别'].unique())}")
    print(f"🏷️ 二级分类数量: {len(df['二级分类'].unique())}")
    print(f"📝 总记录数: {len(df)}")
    print(f"💰 总金额: {df['金额'].sum():,.2f}")
    
    print(f"\n🏢 各部门费用统计:")
    dept_stats = df.groupby('部门')['金额'].agg(['sum', 'count']).sort_values('sum', ascending=False)
    for dept, stats in dept_stats.iterrows():
        print(f"- {dept}: {stats['sum']:,.2f}元 ({stats['count']}笔)")
    
    print(f"\n🏷️ 各二级分类费用统计:")
    subcat_stats = df.groupby('二级分类')['金额'].agg(['sum', 'count']).sort_values('sum', ascending=False)
    for subcat, stats in subcat_stats.head(10).iterrows():
        print(f"- {subcat}: {stats['sum']:,.2f}元 ({stats['count']}笔)")
    
    print(f"\n📁 生成的文件:")
    print(f"1. {output_file} - 主要统计文件（二级分类×年月×部门）")
    print(f"2. {subcategory_file} - 二级分类月度汇总")
    print(f"3. {dept_file} - 部门月度汇总")
    print(f"4. {detailed_file} - 详细统计表")
    
    # 显示主要统计文件的前几行
    print(f"\n📋 主要统计文件预览:")
    print(dept_subcategory_monthly.head(10))
    
    print(f"\n✅ 办公费用二级分类部门统计完成！")

if __name__ == "__main__":
    main()
