#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
完整处理办公费用数据，添加二级分类
"""

import pandas as pd
import numpy as np

def create_subcategory(category, summary):
    """为所有费用类别创建二级分类"""
    summary = str(summary)
    
    # 办公用品二级分类
    if category == '办公用品':
        if '茶叶' in summary:
            return '茶叶费'
        elif '水' in summary:
            return '水费'
        elif '日用品' in summary:
            return '日用品费用'
        elif '办公用品' in summary:
            return '办公用品费'
        elif '刻章' in summary or '印章' in summary:
            return '印章费'
        else:
            return '其他办公用品'
    
    # 邮寄费二级分类
    elif category == '邮寄费':
        if '顺丰' in summary:
            return '顺丰快递'
        elif '邮政' in summary:
            return '邮政快递'
        else:
            return '其他快递'
    
    # 印刷费二级分类
    elif category == '印刷费':
        if '宣传' in summary:
            return '宣传印刷'
        elif '照片' in summary or '相片' in summary:
            return '照片打印'
        else:
            return '其他印刷'
    
    # 其他费用二级分类
    elif category == '其他':
        if '档案' in summary:
            return '档案整理'
        elif '视频' in summary:
            return '视频制作'
        elif '党' in summary:
            return '党建相关'
        else:
            return '其他杂项'
    
    # 差旅费二级分类（如果有的话）
    elif category == '差旅费':
        if '交通' in summary:
            return '交通费'
        elif '住宿' in summary:
            return '住宿费'
        elif '餐饮' in summary or '伙食' in summary:
            return '餐饮费'
        else:
            return '其他差旅'
    
    # 计算机耗材二级分类
    elif category == '计算机耗材':
        if '硒鼓' in summary or '墨盒' in summary:
            return '打印耗材'
        elif '电脑' in summary or '计算机' in summary:
            return '计算机设备'
        else:
            return '其他耗材'
    
    # 默认返回原类别
    else:
        return f'其他{category}'

def main():
    print("🔧 开始处理办公费用数据...")
    
    # 读取原始数据
    with open('办公费用.csv', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 处理数据
    processed_data = []
    header = ['年', '月', '日', '部门', '摘要', '金额', '类别', '费用类型', '二级分类', '年月']
    processed_data.append(header)
    
    for i, line in enumerate(lines[1:], 1):  # 跳过标题行
        parts = line.strip().split(',')
        if len(parts) >= 8:  # 确保有足够的列
            year = parts[0]
            month = parts[1]
            day = parts[2]
            dept = parts[3]
            summary = parts[4]
            # 跳过空列（第6列）
            amount = parts[6].replace('"', '').replace(',', '')
            category = parts[7]
            expense_type = parts[8] if len(parts) > 8 else '管理费用'
            
            # 创建二级分类
            subcategory = create_subcategory(category, summary)
            
            # 创建年月字段
            year_month = f"{year}-{month.zfill(2)}"
            
            # 添加到处理后的数据
            row = [year, month, day, dept, summary, amount, category, expense_type, subcategory, year_month]
            processed_data.append(row)
    
    # 保存到新的CSV文件
    with open('办公费用_含二级分类_完整版.csv', 'w', encoding='utf-8-sig', newline='') as f:
        import csv
        writer = csv.writer(f)
        writer.writerows(processed_data)
    
    print(f"✅ 处理完成！")
    print(f"📊 处理了 {len(processed_data)-1} 条记录")
    print(f"📁 输出文件: 办公费用_含二级分类_完整版.csv")
    
    # 统计信息
    df = pd.DataFrame(processed_data[1:], columns=processed_data[0])
    df['金额'] = pd.to_numeric(df['金额'], errors='coerce')
    
    print(f"\n📋 一级分类统计:")
    category_stats = df['类别'].value_counts()
    for category, count in category_stats.items():
        total_amount = df[df['类别'] == category]['金额'].sum()
        print(f"- {category}: {count}笔, 总金额: {total_amount:,.2f}")
    
    print(f"\n🏷️ 二级分类统计:")
    subcategory_stats = df['二级分类'].value_counts()
    print(f"总共创建了 {len(subcategory_stats)} 个二级分类")
    
    # 按一级分类显示二级分类详情
    categories = df['类别'].unique()
    for category in sorted(categories):
        print(f"\n{category}的二级分类:")
        subcategory_data = df[df['类别'] == category].groupby('二级分类')['金额'].agg(['count', 'sum'])
        subcategory_data = subcategory_data.sort_values('sum', ascending=False)
        
        for subcategory, stats in subcategory_data.iterrows():
            print(f"  - {subcategory}: {stats['count']}笔, 总金额: {stats['sum']:,.2f}")

if __name__ == "__main__":
    main()
