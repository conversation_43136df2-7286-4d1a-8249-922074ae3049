#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
生成办公费用二级分类部门统计（长格式）
每行包含：年、月、部门、二级分类、合计金额、类别
"""

import pandas as pd
import numpy as np

def create_subcategory(category, summary):
    """为所有费用类别创建二级分类"""
    summary = str(summary)
    
    if category == '办公用品':
        if '茶叶' in summary:
            return '茶叶费'
        elif '水' in summary:
            return '水费'
        elif '日用品' in summary:
            return '日用品费用'
        elif '办公用品' in summary:
            return '办公用品费'
        elif '刻章' in summary or '印章' in summary:
            return '印章费'
        else:
            return '其他办公用品'
    elif category == '邮寄费':
        if '顺丰' in summary:
            return '顺丰快递'
        elif '邮政' in summary:
            return '邮政快递'
        else:
            return '其他快递'
    elif category == '印刷费':
        if '宣传' in summary:
            return '宣传印刷'
        elif '照片' in summary or '相片' in summary:
            return '照片打印'
        else:
            return '其他印刷'
    elif category == '其他':
        if '档案' in summary:
            return '档案整理'
        elif '视频' in summary:
            return '视频制作'
        elif '党' in summary:
            return '党建相关'
        else:
            return '其他杂项'
    elif category == '计算机耗材':
        if '硒鼓' in summary or '墨盒' in summary:
            return '打印耗材'
        elif '电脑' in summary or '计算机' in summary:
            return '计算机设备'
        else:
            return '其他耗材'
    else:
        return f'其他{category}'

def main():
    print("🔧 生成办公费用二级分类部门统计（长格式）...")
    
    # 读取数据
    df = pd.read_csv('办公费用.csv', encoding='utf-8')
    print(f"数据形状: {df.shape}")
    
    # 数据预处理
    df['金额'] = df['金额'].astype(str).str.replace(',', '').str.replace('"', '').astype(float)
    
    # 创建二级分类
    df['二级分类'] = df.apply(lambda row: create_subcategory(row['类别'], row['摘要']), axis=1)
    
    # 按年、月、部门、二级分类、类别分组汇总
    result = df.groupby(['年', '月', '部门', '二级分类', '类别'])['金额'].sum().reset_index()
    result.columns = ['年', '月', '部门', '二级分类', '类别', '合计金额']
    
    # 重新排列列的顺序
    result = result[['年', '月', '部门', '二级分类', '合计金额', '类别']]
    
    # 按年、月、部门排序
    result = result.sort_values(['年', '月', '部门', '二级分类'])
    
    # 保存结果
    output_file = '办公费用二级分类部门统计_长格式_完整.csv'
    result.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"✅ 生成完成！")
    print(f"📊 结果形状: {result.shape}")
    print(f"📁 输出文件: {output_file}")
    
    # 显示统计信息
    print(f"\n📋 统计信息:")
    print(f"- 时间范围: {result['年'].min()}年{result['月'].min()}月 到 {result['年'].max()}年{result['月'].max()}月")
    print(f"- 部门数量: {result['部门'].nunique()}")
    print(f"- 二级分类数量: {result['二级分类'].nunique()}")
    print(f"- 一级分类数量: {result['类别'].nunique()}")
    print(f"- 总记录数: {len(result)}")
    print(f"- 总金额: {result['合计金额'].sum():,.2f}")
    
    # 显示前10行
    print(f"\n📋 前10行预览:")
    print(result.head(10))
    
    # 显示各部门统计
    print(f"\n🏢 各部门费用统计:")
    dept_stats = result.groupby('部门')['合计金额'].sum().sort_values(ascending=False)
    for dept, amount in dept_stats.items():
        print(f"- {dept}: {amount:,.2f}元")
    
    # 显示各二级分类统计
    print(f"\n🏷️ 各二级分类费用统计:")
    subcat_stats = result.groupby('二级分类')['合计金额'].sum().sort_values(ascending=False)
    for subcat, amount in subcat_stats.head(10).items():
        print(f"- {subcat}: {amount:,.2f}元")

if __name__ == "__main__":
    main()
