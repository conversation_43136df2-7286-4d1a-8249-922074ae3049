import pandas as pd

# 读取数据
df = pd.read_csv('办公费用.csv', encoding='utf-8')
print(f"数据形状: {df.shape}")

# 处理金额字段
df['金额'] = df['金额'].astype(str).str.replace(',', '').astype(float)

# 二级分类函数
def create_subcategory(row):
    category = row['类别']
    summary = str(row['摘要'])
    
    if category == '办公用品':
        if '茶叶' in summary:
            return '茶叶费'
        elif '水' in summary:
            return '水费'
        elif '日用品' in summary:
            return '日用品费用'
        elif '办公用品' in summary:
            return '办公用品费'
        elif '刻章' in summary or '印章' in summary:
            return '印章费'
        else:
            return '其他办公用品'
    elif category == '邮寄费':
        if '顺丰' in summary:
            return '顺丰快递'
        elif '邮政' in summary:
            return '邮政快递'
        else:
            return '其他快递'
    elif category == '印刷费':
        if '宣传' in summary:
            return '宣传印刷'
        elif '照片' in summary or '相片' in summary:
            return '照片打印'
        else:
            return '其他印刷'
    elif category == '其他':
        if '档案' in summary:
            return '档案整理'
        elif '视频' in summary:
            return '视频制作'
        elif '党' in summary:
            return '党建相关'
        else:
            return '其他杂项'
    elif category == '差旅费':
        if '交通' in summary:
            return '交通费'
        elif '住宿' in summary:
            return '住宿费'
        elif '餐饮' in summary or '伙食' in summary:
            return '餐饮费'
        else:
            return '其他差旅'
    elif category == '计算机耗材':
        if '硒鼓' in summary or '墨盒' in summary:
            return '打印耗材'
        elif '电脑' in summary or '计算机' in summary:
            return '计算机设备'
        else:
            return '其他耗材'
    else:
        return f'其他{category}'

# 应用分类
df['二级分类'] = df.apply(create_subcategory, axis=1)

# 创建年月字段
df['年月'] = df['年'].astype(str) + '-' + df['月'].astype(str).str.zfill(2)

# 保存结果
df.to_csv('办公费用_含二级分类.csv', index=False, encoding='utf-8-sig')

# 统计信息
print("一级分类统计:")
print(df['类别'].value_counts())

print("\n二级分类统计:")
print(df['二级分类'].value_counts())

print("\n按一级分类的二级分类详情:")
for category in df['类别'].unique():
    print(f"\n{category}:")
    subcats = df[df['类别'] == category]['二级分类'].value_counts()
    for subcat, count in subcats.items():
        amount = df[(df['类别'] == category) & (df['二级分类'] == subcat)]['金额'].sum()
        print(f"  {subcat}: {count}笔, {amount:,.2f}元")

print(f"\n处理完成！生成文件: 办公费用_含二级分类.csv")
print(f"总记录数: {len(df)}")
print(f"一级分类数: {len(df['类别'].unique())}")
print(f"二级分类数: {len(df['二级分类'].unique())}")
