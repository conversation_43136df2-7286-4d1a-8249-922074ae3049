import pandas as pd
import numpy as np

# 二级分类函数
def create_subcategory(category, summary):
    summary = str(summary)
    
    if category == '办公用品':
        if '茶叶' in summary:
            return '茶叶费'
        elif '水' in summary:
            return '水费'
        elif '日用品' in summary:
            return '日用品费用'
        elif '办公用品' in summary:
            return '办公用品费'
        elif '刻章' in summary or '印章' in summary:
            return '印章费'
        else:
            return '其他办公用品'
    elif category == '邮寄费':
        if '顺丰' in summary:
            return '顺丰快递'
        elif '邮政' in summary:
            return '邮政快递'
        else:
            return '其他快递'
    elif category == '印刷费':
        if '宣传' in summary:
            return '宣传印刷'
        elif '照片' in summary or '相片' in summary:
            return '照片打印'
        else:
            return '其他印刷'
    elif category == '其他':
        if '档案' in summary:
            return '档案整理'
        elif '视频' in summary:
            return '视频制作'
        elif '党' in summary:
            return '党建相关'
        else:
            return '其他杂项'
    elif category == '计算机耗材':
        if '硒鼓' in summary or '墨盒' in summary:
            return '打印耗材'
        elif '电脑' in summary or '计算机' in summary:
            return '计算机设备'
        else:
            return '其他耗材'
    else:
        return f'其他{category}'

print("开始处理数据...")

# 读取数据
df = pd.read_csv('办公费用.csv', encoding='utf-8')
print(f"数据形状: {df.shape}")

# 数据预处理
df['金额'] = df['金额'].astype(str).str.replace(',', '').str.replace('"', '').astype(float)
df['年月'] = df['年'].astype(str) + '-' + df['月'].astype(str).str.zfill(2)

# 创建二级分类
df['二级分类'] = df.apply(lambda row: create_subcategory(row['类别'], row['摘要']), axis=1)

print(f"部门: {df['部门'].unique()}")
print(f"二级分类数: {len(df['二级分类'].unique())}")

# 创建透视表：二级分类 x 年月 x 部门
pivot_table = df.pivot_table(
    index=['二级分类', '年月'],
    columns='部门',
    values='金额',
    aggfunc='sum',
    fill_value=0
)

# 重置索引
result = pivot_table.reset_index()

# 添加总计列
dept_columns = [col for col in result.columns if col not in ['二级分类', '年月']]
result['总计'] = result[dept_columns].sum(axis=1)

# 保存结果
result.to_csv('办公费用二级分类部门月度统计.csv', index=False, encoding='utf-8-sig')

print("统计完成！")
print(f"结果形状: {result.shape}")
print("\n前10行预览:")
print(result.head(10))

# 创建汇总统计
print("\n创建汇总统计...")

# 按二级分类汇总
subcategory_summary = df.groupby(['二级分类', '年月'])['金额'].sum().reset_index()
subcategory_pivot = subcategory_summary.pivot(
    index='二级分类',
    columns='年月',
    values='金额'
).fillna(0)

subcategory_pivot['总计'] = subcategory_pivot.sum(axis=1)
subcategory_pivot.to_csv('办公费用二级分类月度汇总.csv', encoding='utf-8-sig')

# 按部门汇总
dept_summary = df.groupby(['部门', '年月'])['金额'].sum().reset_index()
dept_pivot = dept_summary.pivot(
    index='部门',
    columns='年月',
    values='金额'
).fillna(0)

dept_pivot['总计'] = dept_pivot.sum(axis=1)
dept_pivot.to_csv('办公费用部门月度汇总.csv', encoding='utf-8-sig')

print("所有文件生成完成！")
print("生成的文件:")
print("1. 办公费用二级分类部门月度统计.csv")
print("2. 办公费用二级分类月度汇总.csv") 
print("3. 办公费用部门月度汇总.csv")
