# 办公费用二级分类部门统计（长格式）说明

## 📋 概述

`办公费用二级分类部门统计_长格式.csv` 是按照您要求的格式生成的统计文件，每一行代表一个具体的费用记录组合（年、月、部门、二级分类）。

## 📊 文件结构

### 列定义
| 列名 | 数据类型 | 说明 | 示例 |
|------|----------|------|------|
| 年 | 整数 | 费用发生年份 | 2022 |
| 月 | 整数 | 费用发生月份 | 1 |
| 部门 | 文本 | 费用发生部门 | 本部 |
| 二级分类 | 文本 | 费用的二级分类 | 顺丰快递 |
| 合计金额 | 数值 | 该组合的费用合计 | 888.00 |
| 类别 | 文本 | 费用的一级分类 | 邮寄费 |

### 数据格式
```csv
年,月,部门,二级分类,合计金额,类别
2022,1,本部,顺丰快递,888.00,邮寄费
2022,1,本部,印章费,155.00,办公用品
2022,1,本部,茶叶费,5200.00,办公用品
```

## 📈 数据特点

### 时间范围
- **起始时间**: 2022年1月
- **结束时间**: 2024年9月
- **总时间跨度**: 33个月

### 数据维度
- **部门数量**: 3个（本部、阳东分公司、阳春分公司）
- **一级分类**: 4个（办公用品、邮寄费、印刷费、其他）
- **二级分类**: 11个（详见下方分类说明）
- **总记录数**: 153条

### 金额统计
- **总费用**: 3,842,540.16元
- **平均每条记录**: 25,114.38元
- **最大单项**: 266,486.96元（2024年5月阳春分公司茶叶费）
- **最小单项**: -240.00元（2024年9月本部茶叶费，可能是退款）

## 🏷️ 二级分类体系

### 办公用品类（一级分类：办公用品）
1. **茶叶费**: 各种茶叶采购费用
2. **水费**: 桶装水等饮用水费用
3. **日用品费用**: 日常办公用品费用
4. **办公用品费**: 一般办公用品采购
5. **印章费**: 刻章、印章相关费用
6. **其他办公用品**: 其他未分类的办公用品

### 邮寄费类（一级分类：邮寄费）
7. **顺丰快递**: 顺丰快递服务费用
8. **邮政快递**: 邮政快递服务费用

### 印刷费类（一级分类：印刷费）
9. **宣传印刷**: 宣传材料印刷费用
10. **照片打印**: 照片、相片打印费用

### 其他费用类（一级分类：其他）
11. **档案整理**: 档案整理服务费用
12. **视频制作**: 视频制作相关费用
13. **其他杂项**: 其他未分类费用

## 🏢 部门分析

### 本部
- **主要费用类型**: 顺丰快递、宣传印刷、茶叶费
- **费用特点**: 管理费用和公共费用为主
- **总费用占比**: 约15%

### 阳东分公司
- **主要费用类型**: 办公用品费
- **费用特点**: 办公用品采购占绝大部分
- **总费用占比**: 约61%

### 阳春分公司
- **主要费用类型**: 茶叶费
- **费用特点**: 茶叶采购费用占主导
- **总费用占比**: 约28%

## 📊 数据应用场景

### 1. 时间序列分析
```python
# 分析特定二级分类的时间趋势
tea_data = df[df['二级分类'] == '茶叶费']
tea_monthly = tea_data.groupby(['年', '月'])['合计金额'].sum()
```

### 2. 部门对比分析
```python
# 比较各部门在特定类别上的费用
office_supplies = df[df['类别'] == '办公用品']
dept_comparison = office_supplies.groupby('部门')['合计金额'].sum()
```

### 3. 费用结构分析
```python
# 分析各二级分类的费用占比
subcategory_ratio = df.groupby('二级分类')['合计金额'].sum() / df['合计金额'].sum()
```

### 4. 异常检测
```python
# 识别异常高的费用记录
threshold = df['合计金额'].quantile(0.95)
anomalies = df[df['合计金额'] > threshold]
```

## 🔧 数据处理建议

### Excel分析
1. 使用数据透视表进行多维度分析
2. 创建图表展示各维度的趋势
3. 使用筛选功能查看特定条件的数据

### Python/R分析
1. 使用pandas的groupby功能进行聚合分析
2. 使用matplotlib/seaborn创建可视化图表
3. 进行时间序列分析和预测

### SQL查询示例
```sql
-- 查询2024年各部门茶叶费用
SELECT 部门, SUM(合计金额) as 茶叶费总额
FROM 办公费用统计
WHERE 年 = 2024 AND 二级分类 = '茶叶费'
GROUP BY 部门;

-- 查询每月顺丰快递费用趋势
SELECT 年, 月, SUM(合计金额) as 快递费
FROM 办公费用统计
WHERE 二级分类 = '顺丰快递'
GROUP BY 年, 月
ORDER BY 年, 月;
```

## ⚠️ 数据质量说明

### 数据来源
- 基于原始办公费用记录
- 通过摘要内容关键词匹配进行二级分类
- 按年、月、部门、二级分类进行汇总

### 注意事项
1. **负数金额**: 存在少量负数（如-240.00），可能代表退款或调整
2. **分类准确性**: 二级分类基于关键词匹配，可能存在少量误分类
3. **数据完整性**: 某些月份某些部门可能没有特定类别的费用记录

### 数据验证
- 总金额与原始数据一致
- 各维度汇总数据已验证
- 时间序列连续性已确认

## 📞 使用建议

1. **定期更新**: 建议每月更新一次数据
2. **分类审查**: 定期检查二级分类的准确性
3. **异常监控**: 关注异常高或异常低的费用记录
4. **趋势分析**: 重点关注主要费用类别的变化趋势

这个长格式的统计文件为办公费用的多维度分析提供了灵活的数据基础，支持各种复杂的查询和分析需求。
