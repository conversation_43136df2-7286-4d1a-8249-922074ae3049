# 指标数据生成工具

## 功能说明

这个目录包含了用于生成指标历史数据的工具和生成的SQL文件。

## 文件说明

### `generate_indicator_data.py`
- **功能**：自动生成指标数据的INSERT语句
- **数据来源**：从现有的指标初始化SQL文件中提取指标ID
- **时间范围**：2022-01 到 2025-06（共42个月）
- **数值范围**：100.00 到 10000.00（随机生成，保留2位小数）
- **输出文件**：`indicator_values_data.sql`

### `indicator_values_data.sql`
- **内容**：包含8652条INSERT语句
- **覆盖范围**：206个指标 × 42个月份
- **数据格式**：符合 `yjzb_indicator_values` 表结构

### `示例数据.sql`
- **内容**：展示生成数据的格式示例
- **用途**：帮助理解数据结构和格式

## 使用方法

### 1. 生成新的指标数据
```bash
python generate_indicator_data.py
```

### 2. 导入数据到数据库
```sql
-- 在数据库中执行生成的SQL文件
source indicator_values_data.sql;
-- 或者
\i indicator_values_data.sql
```

### 3. 验证数据
```sql
-- 查看总记录数
SELECT COUNT(*) FROM yjzb_indicator_values WHERE data_source = 'SCRIPT_GENERATED';

-- 查看某个指标的数据
SELECT * FROM yjzb_indicator_values 
WHERE indicator_id = 1952675507458174978 
ORDER BY period;

-- 查看数据统计
SELECT 
    COUNT(DISTINCT indicator_id) as 指标数量,
    COUNT(DISTINCT period) as 时间期间数量,
    COUNT(*) as 总记录数,
    MIN(value) as 最小值,
    MAX(value) as 最大值,
    AVG(value) as 平均值
FROM yjzb_indicator_values 
WHERE data_source = 'SCRIPT_GENERATED';
```

## 数据特点

- **指标来源**：从费用、利润表、资产负债三类指标中提取
- **时间完整性**：每个指标都有完整的42个月数据
- **数值随机性**：使用随机数生成，符合实际业务数据范围
- **ID唯一性**：使用时间戳+随机数生成唯一ID
- **格式标准**：完全符合现有数据库表结构

## 注意事项

1. **数据源标识**：生成的数据 `data_source` 字段为 `SCRIPT_GENERATED`，便于区分
2. **计算状态**：所有数据的 `calculation_status` 为 `COMPLETED`
3. **用户信息**：使用固定的用户ID和部门ID
4. **编码格式**：脚本输出使用UTF-8编码，避免中文乱码

## 扩展使用

如需修改生成参数，可以编辑 `generate_indicator_data.py` 文件中的以下配置：

```python
# 修改数值范围
def generate_random_value() -> str:
    value = random.uniform(100, 10000)  # 修改这里的范围
    return f"{value:.2f}"

# 修改时间范围
def generate_time_periods() -> List[str]:
    start_year, start_month = 2022, 1  # 修改开始时间
    end_year, end_month = 2025, 6      # 修改结束时间
```

## 数据库表结构

生成的数据符合以下表结构：
```sql
CREATE TABLE yjzb_indicator_values (
    id BIGSERIAL PRIMARY KEY,
    indicator_id BIGINT NOT NULL,
    period VARCHAR(20) NOT NULL,
    value NUMERIC(18,6),
    dimensions TEXT,
    data_source VARCHAR(100),
    calculation_status VARCHAR(20) DEFAULT 'PENDING',
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6) DEFAULT NOW(),
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);
```
