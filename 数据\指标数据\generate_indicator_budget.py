#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import random
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple


WORKSPACE_ROOT = Path(__file__).resolve().parents[2]

# 输入候选文件：优先从指标初始化文件提取 id+name；若缺失则回退到指标值文件提取 id
INDICATOR_INIT_FILES = [
    WORKSPACE_ROOT / '数据' / '费用数据' / '费用指标初始化.sql',
    WORKSPACE_ROOT / '数据' / '利润表数据' / '利润指标初始化.sql',
    WORKSPACE_ROOT / '数据' / '资产负债数据' / '资产负债指标初始化.sql',
]
INDICATOR_VALUES_FILE = (
    WORKSPACE_ROOT / '数据' / '指标数据' / '数据' / '指标数据' / 'indicator_values_data.sql'
)

OUTPUT_SQL = WORKSPACE_ROOT / '数据' / '指标数据' / 'indicator_budget_insert.sql'


def read_text_if_exists(path: Path) -> str:
    if path.exists():
        return path.read_text(encoding='utf-8', errors='ignore')
    return ''


def parse_indicators_from_init_sql(sql_text: str) -> Dict[int, str]:
    """从指标初始化 SQL 中解析 id 与 name。

    预期行格式（示例）：
    INSERT INTO "yjzb_indicator"("id", "name", ...) VALUES (195267..., '营业收入', ...);
    """
    indicator_pattern = re.compile(
        r'INSERT\s+INTO\s+"?yjzb_indicator"?.*?VALUES\s*\(\s*(\d+)\s*,\s*\'(.*?)\'\s*,',
        re.IGNORECASE | re.DOTALL,
    )
    id_to_name: Dict[int, str] = {}
    for match in indicator_pattern.finditer(sql_text):
        try:
            indicator_id = int(match.group(1))
            indicator_name = match.group(2)
            id_to_name[indicator_id] = indicator_name
        except Exception:
            continue
    return id_to_name


def parse_indicator_ids_from_values_sql(sql_text: str) -> List[int]:
    """从指标值 SQL 中解析 indicator_id 列，获取去重后的 id 列表。"""
    values_pattern = re.compile(
        r'INSERT\s+INTO\s+"?yjzb_indicator_values"?.*?VALUES\s*\(\s*\d+\s*,\s*(\d+)\s*,',
        re.IGNORECASE | re.DOTALL,
    )
    ids: List[int] = []
    for match in values_pattern.finditer(sql_text):
        try:
            ids.append(int(match.group(1)))
        except Exception:
            continue
    return sorted(list(set(ids)))


def generate_amount(min_value: float = 60000.0, max_value: float = 80000.0) -> str:
    value = random.uniform(min_value, max_value)
    return f"{value:.2f}"


def escape_sql_string(value: str) -> str:
    return value.replace("'", "''")


def build_insert_line(
    indicator_id: int,
    indicator_name: str,
    year: int,
    user_id: int,
    now_str: str,
) -> str:
    # 五个金额字段都按 60000-80000 范围生成，保留两位小数
    initial_budget = generate_amount()
    initial_reported = generate_amount()
    midyear_budget = generate_amount()
    midyear_reported = generate_amount()
    current_used = generate_amount()

    name_escaped = escape_sql_string(indicator_name)

    sql = (
        'INSERT INTO "yjzb_indicator_annual_budget"'
        ' ("indicator_id", "indicator_name", "year", '
        ' "initial_budget", "initial_reported", "midyear_budget", "midyear_reported", "current_used", '
        ' "create_user", "create_time", "update_user", "update_time", "status", "is_deleted") '
        f"VALUES ({indicator_id}, '{name_escaped}', {year}, "
        f"'{initial_budget}', '{initial_reported}', '{midyear_budget}', '{midyear_reported}', '{current_used}', "
        f"{user_id}, '{now_str}', {user_id}, '{now_str}', 1, 0);"
    )
    return sql


def main() -> None:
    random.seed(20250807)
    user_id = 1123598821738675201
    years = [2022, 2023, 2024, 2025]
    now_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    id_to_name: Dict[int, str] = {}

    # 先尝试从初始化 SQL 中解析 id+name
    for path in INDICATOR_INIT_FILES:
        text = read_text_if_exists(path)
        if text:
            parsed = parse_indicators_from_init_sql(text)
            id_to_name.update(parsed)

    # 若需要，从指标值 SQL 中补齐 id 集合
    ids_from_values: List[int] = []
    values_text = read_text_if_exists(INDICATOR_VALUES_FILE)
    if values_text:
        ids_from_values = parse_indicator_ids_from_values_sql(values_text)

    # 取并集，保证范围尽可能覆盖当前库中的指标
    all_indicator_ids: List[int] = sorted(set(list(id_to_name.keys()) + ids_from_values))

    if not all_indicator_ids:
        print('未在仓库中找到任何指标，已退出。请确认初始化或指标值 SQL 是否存在。')
        return

    # 输出文件头
    header_lines: List[str] = [
        '-- 指标年度预算生成 SQL',
        f"-- 生成时间：{now_str}",
        f"-- 指标数量：{len(all_indicator_ids)}",
        f"-- 年度范围：{years[0]} 到 {years[-1]}",
        f"-- 金额范围：60000.00 到 80000.00",
        '',
    ]

    output_lines: List[str] = []
    output_lines.extend(header_lines)

    for indicator_id in all_indicator_ids:
        indicator_name = id_to_name.get(indicator_id, f'指标{indicator_id}')
        for year in years:
            output_lines.append(
                build_insert_line(indicator_id, indicator_name, year, user_id, now_str)
            )

    OUTPUT_SQL.parent.mkdir(parents=True, exist_ok=True)
    OUTPUT_SQL.write_text("\n".join(output_lines) + "\n", encoding='utf-8')

    print(
        f"已生成 {len(all_indicator_ids) * len(years)} 条 INSERT 至: "
        f"{OUTPUT_SQL.relative_to(WORKSPACE_ROOT)}"
    )


if __name__ == '__main__':
    main()


