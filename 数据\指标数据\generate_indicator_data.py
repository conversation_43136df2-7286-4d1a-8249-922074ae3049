#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指标数据生成脚本
功能：为所有指标生成2022-01到2025-06期间的随机数据
作者：AI Assistant
创建时间：2025-01-27
"""

import random
import datetime
from typing import List, Tuple
import os

# 数据库配置（根据实际情况修改）
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'password',
    'database': 'bladex',
    'charset': 'utf8mb4'
}

# 固定的用户和部门信息（根据现有数据设置）
FIXED_USER_ID = 1123598821738675201
FIXED_DEPT_ID = 1123598813738675201

def generate_time_periods() -> List[str]:
    """生成2022-01到2025-06的时间期间列表"""
    periods = []
    start_year, start_month = 2022, 1
    end_year, end_month = 2025, 8
    
    current_year, current_month = start_year, start_month
    
    while current_year < end_year or (current_year == end_year and current_month <= end_month):
        periods.append(f"{current_year}-{current_month:02d}")
        current_month += 1
        if current_month > 12:
            current_month = 1
            current_year += 1
    
    return periods

def generate_random_value() -> str:
    """生成100到10000之间的随机数，保留2位小数"""
    value = random.uniform(100, 10000)
    return f"{value:.2f}"

def generate_id() -> int:
    """生成雪花ID（简化版本）"""
    # 使用时间戳和随机数生成简单的ID
    timestamp = int(datetime.datetime.now().timestamp() * 1)
    random_part = random.randint(100000000, 999999999)
    return timestamp * 1000000000 + random_part

def get_indicator_ids_from_sql_files() -> List[int]:
    """从现有的SQL文件中提取指标ID"""
    indicator_ids = []
    
    # 获取当前脚本的父目录（项目根目录）
    script_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    # 定义要搜索的SQL文件路径
    sql_files = [
        os.path.join(script_dir, "费用数据", "费用指标初始化_完整版.sql"),
        os.path.join(script_dir, "利润表数据", "利润指标初始化.sql"),
        os.path.join(script_dir, "资产负债数据", "资产负债指标初始化.sql")
    ]
    
    for file_path in sql_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 使用正则表达式提取ID
                import re
                # 匹配所有VALUES子句中的ID
                pattern = r'\(\s*(\d+),'
                matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
                
                for match in matches:
                    indicator_ids.append(int(match))
                    
                print(f"从 {file_path} 提取了 {len(matches)} 个指标ID")
                    
            except Exception as e:
                print(f"读取文件 {file_path} 时出错: {e}")
        else:
            print(f"文件不存在: {file_path}")
    
    return list(set(indicator_ids))  # 去重

def generate_insert_statements(indicator_ids: List[int], periods: List[str]) -> List[str]:
    """生成INSERT语句"""
    insert_statements = []
    current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
    
    for indicator_id in indicator_ids:
        for period in periods:
            record_id = generate_id()
            value = generate_random_value()
            
            insert_sql = f"""INSERT INTO "yjzb_indicator_values"("id", "indicator_id", "period", "value", "dimensions", "data_source", "calculation_status", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES ({record_id}, {indicator_id}, '{period}', '{value}', '', 'SCRIPT_GENERATED', 'COMPLETED', {FIXED_USER_ID}, {FIXED_DEPT_ID}, '{current_time}', {FIXED_USER_ID}, '{current_time}', 1, 0);"""
            
            insert_statements.append(insert_sql)
    
    return insert_statements

def main():
    """主函数"""
    print("开始生成指标数据...")
    
    # 确保输出目录存在
    output_dir = "数据/指标数据"
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取指标ID列表
    print("正在获取指标ID列表...")
    indicator_ids = get_indicator_ids_from_sql_files()
    
    if not indicator_ids:
        print("未找到任何指标ID，请检查SQL文件路径和格式")
        return
    
    print(f"找到 {len(indicator_ids)} 个指标ID")
    
    # 生成时间期间
    periods = generate_time_periods()
    print(f"生成时间期间：{periods[0]} 到 {periods[-1]}，共 {len(periods)} 个月")
    
    # 生成INSERT语句
    print("正在生成INSERT语句...")
    insert_statements = generate_insert_statements(indicator_ids, periods)
    
    # 输出到文件
    output_file = os.path.join(output_dir, "indicator_values_data.sql")
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("-- 指标数据生成SQL\n")
        f.write(f"-- 生成时间：{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"-- 指标数量：{len(indicator_ids)}\n")
        f.write(f"-- 时间范围：2022-01 到 2025-06\n")
        f.write(f"-- 总记录数：{len(insert_statements)}\n")
        f.write("-- 数值范围：100.00 到 10000.00\n")
        f.write("\n")
        
        for statement in insert_statements:
            f.write(statement + "\n")
    
    print(f"生成完成！")
    print(f"输出文件：{output_file}")
    print(f"总共生成 {len(insert_statements)} 条INSERT语句")
    print(f"涵盖 {len(indicator_ids)} 个指标，{len(periods)} 个时间期间")

if __name__ == "__main__":
    main()
