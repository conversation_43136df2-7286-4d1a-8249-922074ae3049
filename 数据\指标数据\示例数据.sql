-- 指标数据示例
-- 这是由脚本生成的示例数据，展示每个指标从2022-01到2025-06的随机数据

-- 示例：指标ID 1952675507458174978 的部分数据
INSERT INTO "yjzb_indicator_values"("id", "indicator_id", "period", "value", "dimensions", "data_source", "calculation_status", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (17544652875849416, 1952675507458174978, '2022-01', '5370.88', '', 'SCRIPT_GENERATED', 'COMPLETED', 1123598821738675201, 1123598813738675201, '2025-08-06 15:28:07.584', 1123598821738675201, '2025-08-06 15:28:07.584', 1, 0);
INSERT INTO "yjzb_indicator_values"("id", "indicator_id", "period", "value", "dimensions", "data_source", "calculation_status", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (17544652875844524, 1952675507458174978, '2022-02', '108.50', '', 'SCRIPT_GENERATED', 'COMPLETED', 1123598821738675201, 1123598813738675201, '2025-08-06 15:28:07.584', 1123598821738675201, '2025-08-06 15:28:07.584', 1, 0);
INSERT INTO "yjzb_indicator_values"("id", "indicator_id", "period", "value", "dimensions", "data_source", "calculation_status", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (17544652875844410, 1952675507458174978, '2022-03', '3005.36', '', 'SCRIPT_GENERATED', 'COMPLETED', 1123598821738675201, 1123598813738675201, '2025-08-06 15:28:07.584', 1123598821738675201, '2025-08-06 15:28:07.584', 1, 0);

-- 更多数据请查看 indicator_values_data.sql 文件
-- 总共包含 206 个指标 × 42 个月份 = 8652 条数据记录
