import csv
import re
from pathlib import Path


def trim_subject_name(subject: str) -> str:
    if subject is None:
        return ""
    return subject.strip()


def is_total_row(subject: str, formula: str) -> bool:
    # 约定：公式列为"0.00"的行为汇总行（合计/类别），不填占位符
    return (formula or "").strip() == "0.00"


def load_sql_names_by_category(sql_path: Path) -> dict[str, set[str]]:
    """从费用指标初始化_完整版.sql 中提取各类别的完整指标名称集合。

    例如："职工薪酬-短期薪酬-工资（销售费用）"、"保险费-车辆保险费（管理费用）" 等。
    既匹配 INSERT 语句，也匹配注释中的示例（财务费用块可能是注释）。
    """
    names_by_cat: dict[str, set[str]] = {"销售费用": set(), "管理费用": set(), "财务费用": set()}
    if not sql_path.exists():
        return names_by_cat

    text = sql_path.read_text(encoding="utf-8", errors="ignore")
    for cat in names_by_cat.keys():
        # 捕获所有带该类别后缀的被引号包裹的名称
        pattern = re.compile(r"'([^']+（" + re.escape(cat) + r"）)'")
        for m in pattern.finditer(text):
            names_by_cat[cat].add(m.group(1))
    return names_by_cat


def build_placeholder_from_joined(joined_name: str, category_name: str) -> str:
    if not joined_name or not category_name:
        return ""
    return f"{{{joined_name.strip()}（{category_name}）}}"


def detect_category(subject_trimmed: str, formula: str) -> str | None:
    # 顶层类别为：销售费用、管理费用、财务费用（且公式列为0.00）
    if is_total_row(subject_trimmed, formula):
        if subject_trimmed in ("销售费用", "管理费用", "财务费用"):
            return subject_trimmed
    return None


def main():
    base_dir = Path(__file__).parent
    src = base_dir / "广东烟草阳江市有限责任公司（本级）三项费用.csv"
    dst = base_dir / "费用数据导入模板.csv"

    if not src.exists():
        raise FileNotFoundError(f"未找到源文件: {src}")

    rows: list[list[str]] = []
    with src.open("r", encoding="utf-8") as f:
        reader = csv.reader(f)
        for row in reader:
            rows.append(row)

    if not rows:
        raise ValueError("源CSV为空")

    # 期望表头列：项 目, 行次, 公式列, 本月数, 本年累计, 上年同期累计
    header = rows[0]

    # 载入 SQL 名称字典，用于确定层级与校验
    sql_names = load_sql_names_by_category(base_dir / "费用指标初始化_完整版.sql")
    # 同时准备去掉类别后缀的基础名称集合，便于判断父子关系
    base_names: dict[str, set[str]] = {}
    parent_names_by_cat: dict[str, set[str]] = {}
    for cat, names in sql_names.items():
        bases = set()
        parents = set()
        for n in names:
            # 去掉末尾的（类别）
            bases.add(n[: n.rfind("（")]) if "（" in n else bases.add(n)
        # 识别父级（作为其他名称的前缀）
        for b in bases:
            if any(o.startswith(b + "-") for o in bases if o != b):
                parents.add(b)
        base_names[cat] = bases
        parent_names_by_cat[cat] = parents

    current_category = None  # 销售费用 / 管理费用 / 财务费用
    # 非“其中：”层级栈：元素为 (indent, full_path)
    non_zhong_stack: list[tuple[int, str]] = []
    # “其中：”分组栈：元素为 (indent, name)
    zhong_stack: list[tuple[int, str]] = []

    output_rows: list[list[str]] = []
    output_rows.append(header)

    for i, row in enumerate(rows[1:], start=1):
        # 兼容缺列的行
        while len(row) < 6:
            row.append("")

        subject_raw = row[0]
        line_no = row[1]
        formula = row[2]
        # row[3] 本月数，row[4] 本年累计，row[5] 上年同期累计

        # 名称与属性提取
        subject_raw = subject_raw or ""
        indent = len(subject_raw) - len(subject_raw.lstrip(" "))
        subject_stripped = subject_raw.lstrip(" ")
        is_zhong = subject_stripped.startswith("其中：")
        subject_core = subject_stripped.replace("其中：", "", 1).strip() if is_zhong else subject_stripped
        subject_trimmed = trim_subject_name(subject_stripped)

        # 更新类别指针
        new_cat = detect_category(subject_trimmed, formula)
        if new_cat:
            current_category = new_cat
            non_zhong_stack.clear()
            zhong_stack.clear()

        # 顶层总计行（如“三项费用合计”）也视为汇总
        is_total = is_total_row(subject_trimmed, formula)
        if is_total:
            # 清空本月数
            row[3] = ""
        else:
            # 生成候选路径，优先匹配 SQL 中存在的名称
            cat = current_category or ""
            cat_set_full = sql_names.get(cat, set())
            cat_set_base = base_names.get(cat, set())
            # 先根据缩进调整两个栈
            if is_zhong:
                # “其中：”栈根据缩进回退
                while zhong_stack and indent <= zhong_stack[-1][0]:
                    zhong_stack.pop()
                # 非“其中”栈不动
            else:
                # 非“其中”：两个栈都根据缩进回退（非其中出现可能意味着新的分支）
                while non_zhong_stack and indent <= non_zhong_stack[-1][0]:
                    non_zhong_stack.pop()
                while zhong_stack and indent <= zhong_stack[-1][0]:
                    zhong_stack.pop()

            # 计算当前基础路径（非“其中”部分）
            base_path = non_zhong_stack[-1][1] if non_zhong_stack else ""

            if is_zhong:
                # 判定该“其中”是否为分组头（在 SQL 中具有后续子项）
                zpath_existing = "-".join([name for _, name in zhong_stack])
                prefix_for_child = f"{base_path}-{zpath_existing}" if zpath_existing else base_path
                prefix_for_child = prefix_for_child.strip("-")

                test_prefix = (
                    f"{prefix_for_child}-{subject_core}-" if prefix_for_child else f"{subject_core}-"
                )
                is_group_header = any(b.startswith(test_prefix) for b in cat_set_base)

                if is_group_header:
                    # 作为分组头，推入栈
                    zhong_stack.append((indent, subject_core))
                    group_path = "-".join([name for _, name in zhong_stack])
                    joined_name = f"{base_path}-{group_path}" if base_path else group_path
                    row[3] = build_placeholder_from_joined(joined_name.strip("-"), cat)
                else:
                    # 叶子：不入栈，直接输出
                    joined_name = (
                        f"{prefix_for_child}-{subject_core}" if prefix_for_child else subject_core
                    )
                    row[3] = build_placeholder_from_joined(joined_name.strip("-"), cat)

            else:
                # 非“其中”：构造候选名称，并仅在该节点存在子项时将其作为父级入栈
                # 基础父路径：不含“其中”
                base_parent = base_path
                # 若存在活动的“其中”分组，作为更深一层父路径
                zpath = "-".join([name for _, name in zhong_stack]) if zhong_stack else ""
                parent_with_zhong = (
                    f"{base_parent}-{zpath}" if (base_parent and zpath) else (zpath or base_parent)
                )
                # 若当前科目或其与 base_parent 组合是一个父级，则清空“其中”栈，避免错误继承（如 从 短期薪酬 到 离职后福利）
                combined_parent = f"{base_parent}-{subject_core}" if base_parent else subject_core
                if subject_core in parent_names_by_cat.get(cat, set()) or combined_parent in parent_names_by_cat.get(cat, set()):
                    zhong_stack.clear()
                    parent_with_zhong = base_parent

                # 组合不同深度的候选，优先匹配 SQL
                candidates: list[str] = []
                # 1) 父级 + 其中路径 + 科目（最具体）
                if parent_with_zhong:
                    candidates.append(f"{parent_with_zhong}-{subject_core}")
                # 2) 父级 + 科目（不含其中路径）
                if base_parent:
                    candidates.append(f"{base_parent}-{subject_core}")
                # 3) 纯科目名
                candidates.append(subject_core)

                chosen = None
                for j in candidates:
                    if f"{j}（{cat}）" in cat_set_full:
                        chosen = j
                        break
                if chosen is None:
                    chosen = candidates[-1]

                row[3] = build_placeholder_from_joined(chosen, cat)

                # 仅当该非“其中”节点在 SQL 中作为父级（存在子项）时，更新父级栈
                # 判断以 chosen 为基准更稳妥，但 chosen 可能是纯科目或含父路径
                # 这里用纯路径 parent_for_stack：若 chosen 以 "-科目" 结尾，取其整体；否则取 subject_core
                parent_for_stack = chosen
                # 判定是否有子项
                has_children = any(b.startswith(parent_for_stack + "-") for b in cat_set_base)
                if not has_children and parent_for_stack != subject_core:
                    # 再尝试以纯科目名判断
                    has_children = any(b.startswith(subject_core + "-") for b in cat_set_base)
                    parent_for_stack = subject_core if has_children else parent_for_stack

                if has_children:
                    # 若该科目是类别下的独立顶层（如 SQL 中存在 "科目（类别）"），并且当前没有处于“其中”路径，则重置为新的起点
                    if f"{subject_core}（{cat}）" in cat_set_full and not zhong_stack:
                        non_zhong_stack.clear()
                        base_parent = ""
                    non_zhong_stack.append((indent, parent_for_stack))

        # 清空末两列，保持模板空值
        # 只保留6列
        output_rows.append([row[0], line_no, formula, row[3], row[4], row[5]])

    with dst.open("w", encoding="utf-8", newline="") as f:
        writer = csv.writer(f)
        writer.writerows(output_rows)

    print(f"已生成模板: {dst}")


if __name__ == "__main__":
    main()


