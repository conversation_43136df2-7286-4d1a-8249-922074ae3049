{"cells": [{"cell_type": "code", "execution_count": null, "id": "46eb9aeb-63a6-42b6-97cf-50e2584e61a9", "metadata": {}, "outputs": [], "source": ["#!/usr/bin/env python\n", "# -*- coding: utf-8 -*-\n", "\"\"\"\n", "水电费用新特征工程脚本\n", "按照具体需求设计的特征计算\n", "\"\"\"\n", "\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"🔧 开始新特征工程计算...\")\n", "\n", "# ==================== 辅助函数 ====================\n", "def calculate_trend_slope(values):\n", "    \"\"\"计算数值序列的线性趋势斜率\"\"\"\n", "    if len(values) < 2 or values.isna().all():\n", "        return 0\n", "    valid_values = values.dropna()\n", "    if len(valid_values) < 2:\n", "        return 0\n", "    x = np.arange(len(valid_values))\n", "    try:\n", "        slope, _ = np.polyfit(x, valid_values, 1)\n", "        return slope\n", "    except:\n", "        return 0\n", "\n", "def calculate_compound_growth_rate(values):\n", "    \"\"\"计算复合月增长率\"\"\"\n", "    if len(values) < 2:\n", "        return 0\n", "    valid_values = values.dropna()\n", "    if len(valid_values) < 2:\n", "        return 0\n", "    \n", "    first_val = valid_values.iloc[0]\n", "    last_val = valid_values.iloc[-1]\n", "    \n", "    if first_val <= 0:\n", "        return 0\n", "    \n", "    periods = len(valid_values) - 1\n", "    if periods <= 0:\n", "        return 0\n", "    \n", "    try:\n", "        compound_rate = (last_val / first_val) ** (1/periods) - 1\n", "        return compound_rate\n", "    except:\n", "        return 0\n", "\n", "def calculate_budget_deviation(actual_amount, budget_amount):\n", "    \"\"\"计算预算偏差\"\"\"\n", "    if budget_amount <= 0:\n", "        return 0\n", "    return (actual_amount - budget_amount) / budget_amount\n", "\n", "# ==================== 读取数据 ====================\n", "print(\"📂 读取基础数据...\")\n", "try:\n", "    df = pd.read_csv('办公费用.csv', encoding='utf-8')\n", "    print(f\"✅ 数据读取成功: {df.shape}\")\n", "except Exception as e:\n", "    print(f\"❌ 数据读取失败: {e}\")\n", "    exit()\n", "\n", "# ==================== 数据预处理 ====================\n", "print(\"🧹 数据预处理...\")\n", "# 处理金额字段\n", "df['金额'] = df['金额'].astype(str).str.replace(',', '').astype(float)\n", "# 处理类别统一\n", "if '类别' in df.columns:\n", "    df['类别'] = df['类别'].replace('邮寄', '邮寄费')\n", "else:\n", "    df['类别'] = '默认类别'\n", "\n", "# 生成标准化日期字段\n", "df['年月'] = df['年'].astype(str) + '-' + df['月'].astype(str).str.zfill(2)\n", "df['日期'] = pd.to_datetime(df['年月'] + '-01')\n", "\n", "print(f\"清洗后数据形状: {df.shape}\")\n", "\n", "# ==================== 按月汇总基础数据 ====================\n", "print(\"📊 按月汇总数据...\")\n", "# 按月汇总总金额\n", "monthly_summary = df.groupby('年月').agg({\n", "    '金额': ['sum', 'mean', 'count', 'std']\n", "}).round(2)\n", "monthly_summary.columns = ['月度总金额', '月度平均金额', '月度交易次数', '月度金额标准差']\n", "\n", "# 按月按类别汇总\n", "category_pivot = df.groupby(['年月', '类别'])['金额'].sum().unstack(fill_value=0)\n", "category_pivot.columns = [f'类别_{col}_金额' for col in category_pivot.columns]\n", "\n", "# 合并数据\n", "monthly_data = monthly_summary.join(category_pivot, how='left').fillna(0)\n", "monthly_data = monthly_data.reset_index()\n", "\n", "print(f\"月度汇总数据形状: {monthly_data.shape}\")\n", "print(\"类别特征:\", [col for col in monthly_data.columns if '类别_' in col])\n", "\n", "# ==================== 创建完整时间序列 ====================\n", "print(\"📅 创建完整时间序列...\")\n", "start_date = monthly_data['年月'].min()\n", "end_date = '2024-12'  # 扩展到2024年12月\n", "date_range = pd.date_range(start=pd.to_datetime(start_date + '-01'), \n", "                          end=pd.to_datetime(end_date + '-01'), \n", "                          freq='MS')\n", "\n", "# 创建基础时间框架\n", "base_df = pd.DataFrame({\n", "    '年月': date_range.strftime('%Y-%m'),\n", "    '年': date_range.year,\n", "    '月': date_range.month,\n", "    '季度': date_range.quarter,\n", "    '日期': date_range\n", "})\n", "\n", "# 合并月度数据\n", "feature_df = base_df.merge(monthly_data, on='年月', how='left')\n", "feature_df = feature_df.fillna(0)\n", "\n", "# 添加年度预算（基于历史数据计算）\n", "yearly_budget = monthly_data.groupby(monthly_data['年月'].str[:4])['月度总金额'].sum().to_dict()\n", "avg_budget = np.mean(list(yearly_budget.values())) if yearly_budget else 1000000\n", "feature_df['年度预算'] = feature_df['年'].map(yearly_budget).fillna(avg_budget)\n", "feature_df['月度预算'] = feature_df['年度预算'] / 12\n", "\n", "print(f\"完整特征数据形状: {feature_df.shape}\")\n", "\n", "# ==================== 按需求计算特征 ====================\n", "print(\"\\n🎯 按需求计算特征...\")\n", "feature_df = feature_df.sort_values('日期')\n", "\n", "# 获取类别特征列\n", "category_cols = [col for col in feature_df.columns if col.startswith('类别_') and col.endswith('_金额')]\n", "print(f\"发现类别特征: {len(category_cols)}个\")\n", "\n", "# 1. 上个月特征\n", "print(\"📊 1. 计算上个月特征...\")\n", "feature_df['上月总金额'] = feature_df['月度总金额'].shift(1)\n", "\n", "# 上个月各类别金额\n", "for col in category_cols:\n", "    new_col_name = col.replace('类别_', '上月_')\n", "    feature_df[new_col_name] = feature_df[col].shift(1)\n", "\n", "# 上个月累计预算偏差\n", "feature_df['上月预算偏差'] = feature_df.apply(\n", "    lambda row: calculate_budget_deviation(row['上月总金额'], row['月度预算']), axis=1\n", ")\n", "\n", "# 上个月复合月增长率\n", "feature_df['上月复合增长率'] = feature_df['月度总金额'].rolling(window=3).apply(\n", "    lambda x: calculate_compound_growth_rate(x), raw=False\n", ").shift(1)\n", "\n", "# 较上个月的增长率\n", "feature_df['较上月增长率'] = feature_df['月度总金额'].pct_change()\n", "\n", "# 2. 上2个月特征\n", "print(\"📊 2. 计算上2个月特征...\")\n", "feature_df['上2月总金额'] = feature_df['月度总金额'].shift(2)\n", "\n", "# 上2个月各类别金额\n", "for col in category_cols:\n", "    new_col_name = col.replace('类别_', '上2月_')\n", "    feature_df[new_col_name] = feature_df[col].shift(2)\n", "\n", "# 上2个月累计预算偏差\n", "feature_df['上2月预算偏差'] = feature_df.apply(\n", "    lambda row: calculate_budget_deviation(row['上2月总金额'], row['月度预算']), axis=1\n", ")\n", "\n", "# 上2个月复合月增长率\n", "feature_df['上2月复合增长率'] = feature_df['月度总金额'].rolling(window=3).apply(\n", "    lambda x: calculate_compound_growth_rate(x), raw=False\n", ").shift(2)\n", "\n", "# 3. 上3个月特征\n", "print(\"📊 3. 计算上3个月特征...\")\n", "feature_df['上3月总金额'] = feature_df['月度总金额'].shift(3)\n", "\n", "# 上3个月各类别金额\n", "for col in category_cols:\n", "    new_col_name = col.replace('类别_', '上3月_')\n", "    feature_df[new_col_name] = feature_df[col].shift(3)\n", "\n", "# 上3个月累计预算偏差\n", "feature_df['上3月预算偏差'] = feature_df.apply(\n", "    lambda row: calculate_budget_deviation(row['上3月总金额'], row['月度预算']), axis=1\n", ")\n", "\n", "# 上3个月复合月增长率\n", "feature_df['上3月复合增长率'] = feature_df['月度总金额'].rolling(window=3).apply(\n", "    lambda x: calculate_compound_growth_rate(x), raw=False\n", ").shift(3)\n", "\n", "# 4. 去年同期特征\n", "print(\"📊 4. 计算去年同期特征...\")\n", "feature_df['去年同期总金额'] = feature_df['月度总金额'].shift(12)\n", "\n", "# 去年同期各类别金额\n", "for col in category_cols:\n", "    new_col_name = col.replace('类别_', '去年同期_')\n", "    feature_df[new_col_name] = feature_df[col].shift(12)\n", "\n", "# 较去年同期增长率\n", "feature_df['较去年同期增长率'] = ((feature_df['月度总金额'] - feature_df['去年同期总金额']) / \n", "                              (feature_df['去年同期总金额'] + 1e-8))\n", "\n", "# 5. 斜率特征\n", "print(\"📊 5. 计算斜率特征...\")\n", "# 前3个月总金额的斜率\n", "feature_df['前3月斜率'] = feature_df['月度总金额'].rolling(window=3).apply(\n", "    lambda x: calculate_trend_slope(x), raw=False\n", ")\n", "\n", "# 前6个月总金额的斜率\n", "feature_df['前6月斜率'] = feature_df['月度总金额'].rolling(window=6).apply(\n", "    lambda x: calculate_trend_slope(x), raw=False\n", ")\n", "\n", "# 6. 时间特征\n", "print(\"📊 6. 计算时间特征...\")\n", "feature_df['是否年初'] = (feature_df['月'] <= 3).astype(int)\n", "feature_df['是否年中'] = ((feature_df['月'] >= 4) & (feature_df['月'] <= 9)).astype(int)\n", "feature_df['是否年末'] = (feature_df['月'] >= 10).astype(int)\n", "feature_df['是否上半年'] = (feature_df['月'] <= 6).astype(int)\n", "feature_df['是否第一季度'] = (feature_df['季度'] == 1).astype(int)\n", "feature_df['是否第二季度'] = (feature_df['季度'] == 2).astype(int)\n", "feature_df['是否第三季度'] = (feature_df['季度'] == 3).astype(int)\n", "feature_df['是否第四季度'] = (feature_df['季度'] == 4).astype(int)\n", "\n", "# 7. 平均值特征\n", "print(\"📊 7. 计算平均值特征...\")\n", "# 前3个月总金额平均值\n", "feature_df['前3月平均值'] = feature_df['月度总金额'].rolling(window=3).mean()\n", "\n", "# 去年同期三个月平均值\n", "feature_df['去年同期3月平均值'] = feature_df['前3月平均值'].shift(12)\n", "\n", "# ==================== 处理缺失值 ====================\n", "print(\"\\n🔧 处理缺失值...\")\n", "numeric_cols = feature_df.select_dtypes(include=[np.number]).columns\n", "feature_df[numeric_cols] = feature_df[numeric_cols].fillna(0)\n", "\n", "# ==================== 输出结果 ====================\n", "print(\"\\n💾 保存新特征工程结果...\")\n", "output_file = '新特征工程数据.csv'\n", "feature_df.to_csv(output_file, index=False, encoding='utf-8-sig')\n", "\n", "print(f\"✅ 新特征工程完成！\")\n", "print(f\"📊 最终数据形状: {feature_df.shape}\")\n", "print(f\"📁 输出文件: {output_file}\")\n", "\n", "# ==================== 特征统计 ====================\n", "print(\"\\n📋 新特征统计:\")\n", "print(\"=\" * 60)\n", "\n", "feature_categories = {\n", "    '基础特征': ['年月', '年', '月', '季度', '月度总金额', '月度平均金额', '月度交易次数'],\n", "    '上月特征': [col for col in feature_df.columns if '上月' in col],\n", "    '上2月特征': [col for col in feature_df.columns if '上2月' in col],\n", "    '上3月特征': [col for col in feature_df.columns if '上3月' in col],\n", "    '去年同期特征': [col for col in feature_df.columns if '去年同期' in col],\n", "    '斜率特征': [col for col in feature_df.columns if '斜率' in col],\n", "    '时间特征': [col for col in feature_df.columns if '是否' in col],\n", "    '平均值特征': [col for col in feature_df.columns if '平均值' in col],\n", "    '增长率特征': [col for col in feature_df.columns if '增长率' in col],\n", "    '预算特征': [col for col in feature_df.columns if '预算' in col]\n", "}\n", "\n", "total_features = 0\n", "for category, features in feature_categories.items():\n", "    if features:\n", "        print(f\"{category}: {len(features)}个\")\n", "        total_features += len(features)\n", "\n", "print(f\"\\n总特征数: {total_features}\")\n", "print(f\"数据时间范围: {feature_df['年月'].min()} 到 {feature_df['年月'].max()}\")\n", "\n", "print(\"\\n前5行数据预览:\")\n", "print(feature_df[['年月', '月度总金额', '上月总金额', '较上月增长率', '前3月斜率']].head())\n", "\n", "print(\"\\n✅ 新特征工程脚本执行完成！\")\n"]}, {"cell_type": "code", "execution_count": 2, "id": "9626fed5-3cc9-4cb8-b7e1-a07ff55b64cf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 按二级分类统计水电费用...\n", "\n", "✅ 二级分类特征工程完成！\n", "📊 最终数据形状: (73, 36)\n", "📁 输出文件: 水电费分类特征工程数据拆分.csv\n", "\n", "📋 二级分类特征统计:\n", "============================================================\n", "基础特征: 8个\n", "时间特征: 6个\n", "上月特征: 4个\n", "上2月特征: 4个\n", "上3月特征: 3个\n", "去年同期特征: 4个\n", "斜率特征: 1个\n", "平均值特征: 1个\n", "增长率特征: 3个\n", "预算特征: 5个\n", "\n", "总特征数: 39\n", "数据时间范围: 2021-12-01 到 2024-12-01\n", "二级分类数量: 2个\n", "\n", "二级分类列表:\n", "1. 水费\t2. 电费\t\n", "\n", "前5行数据预览:\n", "           年月 二级分类     月度总金额     上月总金额    较上月增长率      前3月斜率     预算完成率  \\\n", "0  2021-12-01   电费  30558.70      0.00  0.000000      0.000  1.000000   \n", "2  2022-01-01   电费  51866.10  30558.70  0.697261      0.000  0.057705   \n", "4  2022-02-01   电费  31445.69  51866.10 -0.393714      0.000  0.092691   \n", "6  2022-03-01   电费  55077.59  31445.69  0.751515   5313.626  0.153970   \n", "8  2022-04-01   电费  89674.49  55077.59  0.628148  13705.707  0.253740   \n", "\n", "       预算偏差  \n", "0  0.000000  \n", "2 -0.025628  \n", "4 -0.073975  \n", "6 -0.096030  \n", "8 -0.079593  \n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "✅ 二级分类特征工程脚本执行完成！\n"]}], "source": ["# ==================== 按二级分类统计水电费用 ====================\n", "print(\"\\n📊 按二级分类统计水电费用...\")\n", "\n", "# 读取带二级分类的数据\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.linear_model import LinearRegression\n", "import calendar\n", "\n", "# 读取带二级分类的数据\n", "df_with_subcategory = pd.read_csv('水电费明细.csv')\n", "\n", "# 确保日期列格式正确\n", "df_with_subcategory['年月'] = df_with_subcategory['年月'].astype(str)\n", "\n", "# 按年月和二级分类进行分组统计\n", "monthly_subcategory_stats = df_with_subcategory.groupby(['年月', '二级分类']).agg({\n", "    '金额': ['sum', 'mean', 'count']\n", "}).reset_index()\n", "\n", "# 调整列名\n", "monthly_subcategory_stats.columns = ['年月', '二级分类', '月度总金额', '月度平均金额', '月度交易次数']\n", "\n", "# 创建特征工程函数\n", "def create_subcategory_features(df):\n", "    # 复制数据框\n", "    feature_df = df.copy()\n", "    \n", "    # 创建时间特征\n", "    feature_df['年'] = feature_df['年月'].str[:4].astype(int)\n", "    feature_df['月'] = feature_df['年月'].str[5:7].astype(int)\n", "    feature_df['季度'] = ((feature_df['月'] - 1) // 3 + 1).astype(int)\n", "    \n", "    # 添加更多时间特征\n", "    feature_df['是否月初'] = (feature_df['月'] == 1).astype(int)\n", "    feature_df['是否月末'] = feature_df.apply(lambda x: x['月'] == 12, axis=1).astype(int)\n", "    feature_df['是否季初'] = (feature_df['月'] % 3 == 1).astype(int)\n", "    feature_df['是否季末'] = (feature_df['月'] % 3 == 0).astype(int)\n", "    feature_df['是否上半年'] = (feature_df['月'] <= 6).astype(int)\n", "    feature_df['月天数'] = feature_df.apply(lambda x: calendar.monthrange(x['年'], x['月'])[1], axis=1)\n", "    \n", "    # 创建上月特征\n", "    year_month_subcategory = feature_df[['年月', '二级分类', '月度总金额', '月度平均金额', '月度交易次数']]\n", "    \n", "    # 创建所有年月和二级分类的组合\n", "    all_year_months = sorted(feature_df['年月'].unique())\n", "    all_subcategories = sorted(feature_df['二级分类'].unique())\n", "    \n", "    # 创建上月、上2月、上3月特征\n", "    for i, ym in enumerate(all_year_months):\n", "        year, month = int(ym.split('-')[0]), int(ym.split('-')[1])\n", "        \n", "        # 上月\n", "        if i > 0:\n", "            prev_ym = all_year_months[i-1]\n", "            prev_data = year_month_subcategory[year_month_subcategory['年月'] == prev_ym]\n", "            \n", "            for _, row in prev_data.iterrows():\n", "                mask = (feature_df['年月'] == ym) & (feature_df['二级分类'] == row['二级分类'])\n", "                feature_df.loc[mask, '上月总金额'] = row['月度总金额']\n", "                feature_df.loc[mask, '上月平均金额'] = row['月度平均金额']\n", "                feature_df.loc[mask, '上月交易次数'] = row['月度交易次数']\n", "        \n", "        # 上2月\n", "        if i > 1:\n", "            prev2_ym = all_year_months[i-2]\n", "            prev2_data = year_month_subcategory[year_month_subcategory['年月'] == prev2_ym]\n", "            \n", "            for _, row in prev2_data.iterrows():\n", "                mask = (feature_df['年月'] == ym) & (feature_df['二级分类'] == row['二级分类'])\n", "                feature_df.loc[mask, '上2月总金额'] = row['月度总金额']\n", "                feature_df.loc[mask, '上2月平均金额'] = row['月度平均金额']\n", "                feature_df.loc[mask, '上2月交易次数'] = row['月度交易次数']\n", "        \n", "        # 上3月\n", "        if i > 2:\n", "            prev3_ym = all_year_months[i-3]\n", "            prev3_data = year_month_subcategory[year_month_subcategory['年月'] == prev3_ym]\n", "            \n", "            for _, row in prev3_data.iterrows():\n", "                mask = (feature_df['年月'] == ym) & (feature_df['二级分类'] == row['二级分类'])\n", "                feature_df.loc[mask, '上3月总金额'] = row['月度总金额']\n", "                feature_df.loc[mask, '上3月平均金额'] = row['月度平均金额']\n", "                feature_df.loc[mask, '上3月交易次数'] = row['月度交易次数']\n", "        \n", "        # 去年同期\n", "        last_year_ym = f\"{year-1}-{month:02d}\"\n", "        if last_year_ym in all_year_months:\n", "            last_year_data = year_month_subcategory[year_month_subcategory['年月'] == last_year_ym]\n", "            \n", "            for _, row in last_year_data.iterrows():\n", "                mask = (feature_df['年月'] == ym) & (feature_df['二级分类'] == row['二级分类'])\n", "                feature_df.loc[mask, '去年同期总金额'] = row['月度总金额']\n", "                feature_df.loc[mask, '去年同期平均金额'] = row['月度平均金额']\n", "                feature_df.loc[mask, '去年同期交易次数'] = row['月度交易次数']\n", "        else:\n", "            feature_df['去年同期总金额'] = 0\n", "            feature_df['去年同期平均金额'] = 0\n", "            feature_df['去年同期交易次数'] = 0\n", "    # 计算增长率特征\n", "    feature_df['较上月增长率'] = (feature_df['月度总金额'] - feature_df['上月总金额']) / feature_df['上月总金额'].replace(0, np.nan)\n", "    feature_df['较上2月增长率'] = (feature_df['月度总金额'] - feature_df['上2月总金额']) / feature_df['上2月总金额'].replace(0, np.nan)\n", "    feature_df['较去年同期增长率'] = (feature_df['月度总金额'] - feature_df['去年同期总金额']) / feature_df['去年同期总金额'].replace(0, np.nan)\n", "    \n", "    # 计算3个月平均值\n", "    feature_df['前3月平均值'] = feature_df[['月度总金额', '上月总金额', '上2月总金额']].mean(axis=1, skipna=True)\n", "    \n", "    # 计算斜率特征（趋势）\n", "    def calculate_slope(row):\n", "        y = [row['上3月总金额'], row['上2月总金额'], row['上月总金额'], row['月度总金额']]\n", "        x = np.arange(len(y)).reshape(-1, 1)\n", "        y = np.array(y).reshape(-1, 1)\n", "        if np.isnan(y).any():\n", "            return np.nan\n", "        model = LinearRegression()\n", "        model.fit(x, y)\n", "        return model.coef_[0][0]\n", "    \n", "    # 只对有足够历史数据的行计算斜率\n", "    mask = feature_df['上3月总金额'].notna()\n", "    feature_df.loc[mask, '前3月斜率'] = feature_df[mask].apply(calculate_slope, axis=1)\n", "    \n", "    # 计算预算特征\n", "    # 1. 计算每年每个二级分类的年度预算（当年累计金额）\n", "    yearly_budget = feature_df.groupby(['年', '二级分类'])['月度总金额'].sum().reset_index()\n", "    yearly_budget.rename(columns={'月度总金额': '年度预算'}, inplace=True)\n", "    \n", "    # 将年度预算合并回原数据\n", "    feature_df = pd.merge(feature_df, yearly_budget, on=['年', '二级分类'], how='left')\n", "    \n", "    # 2. 计算到当前月为止的累计金额\n", "    # 首先创建一个辅助列，用于排序\n", "    feature_df['年月排序'] = feature_df['年'].astype(str) + feature_df['月'].astype(str).str.zfill(2)\n", "    \n", "    # 对每个年份和二级分类，计算累计金额\n", "    cumulative_amounts = []\n", "    \n", "    for year in feature_df['年'].unique():\n", "        for category in feature_df['二级分类'].unique():\n", "            # 获取该年该分类的所有数据，并按月份排序\n", "            year_cat_data = feature_df[(feature_df['年'] == year) & (feature_df['二级分类'] == category)].sort_values('月')\n", "            \n", "            if not year_cat_data.empty:\n", "                # 计算累计金额\n", "                year_cat_data['当年累计金额'] = year_cat_data['月度总金额'].cumsum()\n", "                cumulative_amounts.append(year_cat_data)\n", "    \n", "    if cumulative_amounts:\n", "        feature_df = pd.concat(cumulative_amounts)\n", "    \n", "    # 3. 计算预算完成率\n", "    feature_df['预算完成率'] = feature_df['当年累计金额'] / feature_df['年度预算'].replace(0, np.nan)\n", "    \n", "    # 4. 计算预算偏差（实际进度与理论进度的差异）\n", "    feature_df['理论进度'] = feature_df['月'] / 12\n", "    feature_df['预算偏差'] = feature_df['预算完成率'] - feature_df['理论进度']\n", "    \n", "    # 删除辅助列\n", "    feature_df.drop('年月排序', axis=1, inplace=True)\n", "    \n", "    # 处理缺失值\n", "    numeric_cols = feature_df.select_dtypes(include=[np.number]).columns\n", "    feature_df[numeric_cols] = feature_df[numeric_cols].fillna(0)\n", "    \n", "    return feature_df\n", "\n", "# 创建特征\n", "subcategory_features = create_subcategory_features(monthly_subcategory_stats)\n", "\n", "# 保存结果\n", "output_file = '水电费分类特征工程数据拆分.csv'\n", "subcategory_features.to_csv(output_file, index=False, encoding='utf-8-sig')\n", "\n", "# 输出统计信息\n", "print(f\"\\n✅ 二级分类特征工程完成！\")\n", "print(f\"📊 最终数据形状: {subcategory_features.shape}\")\n", "print(f\"📁 输出文件: {output_file}\")\n", "\n", "# 特征统计\n", "print(\"\\n📋 二级分类特征统计:\")\n", "print(\"=\" * 60)\n", "\n", "feature_categories = {\n", "    '基础特征': ['年月', '二级分类', '年', '月', '季度', '月度总金额', '月度平均金额', '月度交易次数'],\n", "    '时间特征': [col for col in subcategory_features.columns if '是否' in col] + ['月天数'],\n", "    '上月特征': [col for col in subcategory_features.columns if '上月' in col],\n", "    '上2月特征': [col for col in subcategory_features.columns if '上2月' in col],\n", "    '上3月特征': [col for col in subcategory_features.columns if '上3月' in col],\n", "    '去年同期特征': [col for col in subcategory_features.columns if '去年同期' in col],\n", "    '斜率特征': [col for col in subcategory_features.columns if '斜率' in col],\n", "    '平均值特征': [col for col in subcategory_features.columns if '平均值' in col],\n", "    '增长率特征': [col for col in subcategory_features.columns if '增长率' in col],\n", "    '预算特征': ['年度预算', '当年累计金额', '预算完成率', '理论进度', '预算偏差']\n", "}\n", "\n", "total_features = 0\n", "for category, features in feature_categories.items():\n", "    if features:\n", "        print(f\"{category}: {len(features)}个\")\n", "        total_features += len(features)\n", "\n", "print(f\"\\n总特征数: {total_features}\")\n", "print(f\"数据时间范围: {subcategory_features['年月'].min()} 到 {subcategory_features['年月'].max()}\")\n", "print(f\"二级分类数量: {subcategory_features['二级分类'].nunique()}个\")\n", "\n", "# 显示二级分类列表\n", "print(\"\\n二级分类列表:\")\n", "for i, category in enumerate(sorted(subcategory_features['二级分类'].unique()), 1):\n", "    print(f\"{i}. {category}\", end=\"\\t\")\n", "    if i % 5 == 0:\n", "        print()\n", "if len(subcategory_features['二级分类'].unique()) % 5 != 0:\n", "    print()\n", "\n", "# 显示前5行数据预览\n", "print(\"\\n前5行数据预览:\")\n", "preview_cols = ['年月', '二级分类', '月度总金额', '上月总金额', '较上月增长率', '前3月斜率', '预算完成率', '预算偏差']\n", "available_cols = [col for col in preview_cols if col in subcategory_features.columns]\n", "print(subcategory_features[available_cols].head())\n", "\n", "# 绘制二级分类金额分布图\n", "plt.figure(figsize=(12, 8))\n", "top_categories = subcategory_features.groupby('二级分类')['月度总金额'].sum().nlargest(10)\n", "sns.barplot(x=top_categories.index, y=top_categories.values)\n", "plt.title('前10个二级分类总金额')\n", "plt.xticks(rotation=45, ha='right')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"\\n✅ 二级分类特征工程脚本执行完成！\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}