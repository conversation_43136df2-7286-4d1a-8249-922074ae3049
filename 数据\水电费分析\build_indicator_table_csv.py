import csv
from collections import defaultdict
from dataclasses import dataclass
from decimal import Decimal, ROUND_HALF_UP
from pathlib import Path
from typing import Dict, List, Tuple


@dataclass
class SourceRow:
    period: str  # YYYY-MM
    value: Decimal


def parse_decimal(text: str) -> Decimal:
    if text is None:
        return Decimal("0")
    s = str(text).strip()
    if s == "":
        return Decimal("0")
    s = s.replace(",", "").replace("\u00a0", "")
    return Decimal(s)


def read_monthly_series(path: Path) -> List[SourceRow]:
    rows: List[SourceRow] = []
    with path.open("r", encoding="utf-8-sig", newline="") as f:
        reader = csv.reader(f)
        header = next(reader, None)
        # Expect header: 年,月,年月,数值, ...
        for r in reader:
            if not r or all(c.strip() == "" for c in r):
                continue
            ym = (r[2] or "").strip()
            if ym == "":
                # Fallback 年-月
                year = (r[0] or "").strip()
                month = (r[1] or "").strip()
                if year and month:
                    ym = f"{year}-{str(int(month)).zfill(2)}"
                else:
                    continue
            ym = ym[:7]
            value = parse_decimal(r[3] if len(r) > 3 else "0")
            rows.append(SourceRow(period=ym, value=value))
    # sort by period
    rows.sort(key=lambda x: x.period)
    return rows


def year_of(period: str) -> str:
    return period.split("-")[0]


def format_decimal(d: Decimal) -> str:
    q = d.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)
    return f"{float(q):.2f}"


def build_feature_rows(series: List[SourceRow], indicator_id: str) -> List[List[str]]:
    # Pre-compute maps
    period_to_value: Dict[str, Decimal] = {r.period: r.value for r in series}

    # Compute annual totals for budget
    year_totals: Dict[str, Decimal] = defaultdict(Decimal)
    for r in series:
        year_totals[year_of(r.period)] += r.value

    # Compute rows with features
    periods = [r.period for r in series]

    def get_value(period: str) -> Decimal:
        return period_to_value.get(period, Decimal("0"))

    result: List[List[str]] = []

    # Build cumulative per-year
    cumulative_by_year: Dict[str, Decimal] = defaultdict(Decimal)

    for idx, p in enumerate(periods):
        yr = year_of(p)
        cur = get_value(p)
        cumulative_by_year[yr] += cur

        # lags
        # derive YYYY-MM strings
        y, m = p.split("-")
        y = int(y)
        m = int(m)

        def shift_months(y: int, m: int, delta: int) -> Tuple[int, int]:
            total = y * 12 + (m - 1) + delta
            new_y = total // 12
            new_m = total % 12 + 1
            return new_y, new_m

        def ym_str(y: int, m: int) -> str:
            return f"{y}-{str(m).zfill(2)}"

        lag1_p = ym_str(*shift_months(y, m, -1))
        lag2_p = ym_str(*shift_months(y, m, -2))
        lag3_p = ym_str(*shift_months(y, m, -3))
        last_year_p = ym_str(y - 1, m)

        lag1 = get_value(lag1_p) if lag1_p in period_to_value else None
        lag2 = get_value(lag2_p) if lag2_p in period_to_value else None
        lag3 = get_value(lag3_p) if lag3_p in period_to_value else None

        # ma3: include current month if we have at least three consecutive months
        ma3 = None
        if lag1 is not None and lag2 is not None:
            ma3 = (cur + lag1 + lag2) / Decimal(3)

        same_period_last_year = (
            get_value(last_year_p) if last_year_p in period_to_value else None
        )

        current_year_cumulative = cumulative_by_year[yr]
        annual_budget = year_totals[yr]

        def fmt(x: Decimal | None) -> str:
            return format_decimal(x) if x is not None else ""

        row_id = f"{indicator_id}-{p}"
        result.append(
            [
                row_id,
                indicator_id,
                p,
                format_decimal(cur),
                fmt(lag1),
                fmt(lag2),
                fmt(lag3),
                fmt(ma3),
                fmt(same_period_last_year),
                format_decimal(current_year_cumulative),
                format_decimal(annual_budget),
            ]
        )

    return result


def write_output(path: Path, rows: List[List[str]]):
    with path.open("w", encoding="utf-8-sig", newline="") as f:
        writer = csv.writer(f)
        writer.writerow(
            [
                "id",
                "indicator_id",
                "period",
                "current_value",
                "lag1_value",
                "lag2_value",
                "lag3_value",
                "ma3_value",
                "same_period_last_year_value",
                "current_year_cumulative_value",
                "annual_budget_value",
            ]
        )
        writer.writerows(rows)


def main():
    base = Path(__file__).parent
    inputs = [
        (base / "燃料费指标.csv", "fuel_cost"),
        (base / "水电费指标.csv", "water_electric_cost"),
    ]

    all_rows: List[List[str]] = []
    for src, indicator_id in inputs:
        if not src.exists():
            print(f"警告: 未找到输入文件 {src}")
            continue
        series = read_monthly_series(src)
        rows = build_feature_rows(series, indicator_id)
        all_rows.extend(rows)

    if not all_rows:
        raise SystemExit("没有可写入的数据，检查输入文件是否存在并非空")

    out_path = base / "费用指标_宽表.csv"
    write_output(out_path, all_rows)
    print(f"已生成: {out_path}")


if __name__ == "__main__":
    main()


