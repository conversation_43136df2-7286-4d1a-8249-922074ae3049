import csv
from decimal import Decimal, InvalidOperation, ROUND_HALF_UP
from collections import defaultdict
from pathlib import Path


def parse_decimal(value: str) -> Decimal:
    if value is None:
        return Decimal("0")
    text = str(value).strip()
    if text == "":
        return Decimal("0")
    # Remove common thousands separators if any
    text = text.replace(",", "").replace("\u00a0", "")
    try:
        return Decimal(text)
    except InvalidOperation:
        return Decimal("0")


def main():
    base_dir = Path(__file__).parent
    src_path = base_dir / "水电费明细.csv"
    out_path = base_dir / "水电费指标.csv"

    if not src_path.exists():
        raise FileNotFoundError(f"未找到源数据文件: {src_path}")

    monthly_sum: dict[str, Decimal] = defaultdict(Decimal)

    with src_path.open("r", encoding="utf-8-sig", newline="") as f:
        reader = csv.reader(f)
        try:
            header = next(reader)
        except StopIteration:
            header = []

        # Identify columns
        # Header expected: 年,月,日,部门,摘要,备注,金额,类别,费用类型,二级分类,年月,日期,
        # We will rely on 列名 to locate fields
        col_index = {name: idx for idx, name in enumerate(header)}
        amount_idx = col_index.get("金额", 6)
        yue_nian_idx = col_index.get("年月", 10)

        for row in reader:
            if not row or all(cell.strip() == "" for cell in row):
                continue

            # Extract 年月 (YYYY-MM from the 年月列 which looks like YYYY-MM-01)
            ym_raw = row[yue_nian_idx] if len(row) > yue_nian_idx else ""
            ym_raw = (ym_raw or "").strip()
            if ym_raw == "":
                # try fallback combine 年-月
                year = row[0].strip() if len(row) > 0 else ""
                month = row[1].strip() if len(row) > 1 else ""
                if year and month:
                    ym_key = f"{year}-{str(int(month)).zfill(2)}"
                else:
                    continue
            else:
                # keep only YYYY-MM
                ym_key = ym_raw[:7]

            amt = parse_decimal(row[amount_idx] if len(row) > amount_idx else "")
            monthly_sum[ym_key] += amt

    # Prepare rows sorted by 年月
    sorted_keys = sorted(monthly_sum.keys())

    # Write output with header matching 燃料费指标.csv structure
    with out_path.open("w", encoding="utf-8-sig", newline="") as f:
        writer = csv.writer(f)
        writer.writerow(["年", "月", "年月", "数值"] + [""] * 12)
        for ym in sorted_keys:
            year = ym.split("-")[0]
            month = ym.split("-")[1]
            total = monthly_sum[ym].quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)
            # format with thousands separator
            formatted = f"{float(total):,.2f}"
            writer.writerow([year, str(int(month)), ym, formatted] + [""] * 12)

    print(f"已生成: {out_path}")


if __name__ == "__main__":
    main()


