/*
 Navicat Premium Data Transfer

 Source Server         : mlops-test0210.rwlb.rds.aliyuncs.com
 Source Server Type    : MySQL
 Source Server Version : 80013
 Source Host           : mlops-test0210.rwlb.rds.aliyuncs.com:3306
 Source Schema         : local_mc

 Target Server Type    : MySQL
 Target Server Version : 80013
 File Encoding         : 65001

 Date: 31/07/2025 17:58:54
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for office2
-- ----------------------------
DROP TABLE IF EXISTS `office3`;
CREATE TABLE `office3`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `year_month` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '年月',
  `secondary_category` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '费用二级分类',
  `monthly_total_amount` decimal(15, 2) NULL COMMENT '月度总金额',
  `monthly_average_amount` decimal(15, 2) NULL COMMENT '月度平均金额',
  `monthly_transaction_count` int(11) NULL DEFAULT 0 COMMENT '月度交易次数',
  `year` int(11) NOT NULL COMMENT '年份',
  `month` tinyint(4) NOT NULL COMMENT '月份（1-12）',
  `quarter` tinyint(4) NOT NULL COMMENT '季度（1-4）',
  `is_month_start` tinyint(1) NULL DEFAULT 0 COMMENT '是否月初（0:否, 1:是）',
  `is_month_end` tinyint(1) NULL DEFAULT 0 COMMENT '是否月末（0:否, 1:是）',
  `is_quarter_start` tinyint(1) NULL DEFAULT 0 COMMENT '是否季初（0:否, 1:是）',
  `is_quarter_end` tinyint(1) NULL DEFAULT 0 COMMENT '是否季末（0:否, 1:是）',
  `is_first_half_year` tinyint(1) NULL DEFAULT 0 COMMENT '是否上半年（0:否, 1:是）',
  `days_in_month` tinyint(4) NOT NULL COMMENT '当月天数',
  `prev_month_total_amount` decimal(15, 2) NULL COMMENT '上月总金额',
  `prev_month_average_amount` decimal(15, 2) NULL COMMENT '上月平均金额',
  `prev_month_transaction_count` decimal(10, 2) NULL COMMENT '上月交易次数',
  `prev_2month_total_amount` decimal(15, 2) NULL COMMENT '上2月总金额',
  `prev_2month_average_amount` decimal(15, 2) NULL COMMENT '上2月平均金额',
  `prev_2month_transaction_count` decimal(10, 2) NULL COMMENT '上2月交易次数',
  `prev_3month_total_amount` decimal(15, 2) NULL COMMENT '上3月总金额',
  `prev_3month_average_amount` decimal(15, 2) NULL COMMENT '上3月平均金额',
  `prev_3month_transaction_count` decimal(10, 2) NULL COMMENT '上3月交易次数',
  `same_period_last_year_total_amount` decimal(15, 2) NULL COMMENT '去年同期总金额',
  `same_period_last_year_average_amount` decimal(15, 2) NULL COMMENT '去年同期平均金额',
  `same_period_last_year_transaction_count` decimal(10, 2) NULL COMMENT '去年同期交易次数',
  `growth_rate_vs_prev_month` decimal(10, 6) NULL COMMENT '较上月增长率',
  `growth_rate_vs_prev_2month` decimal(10, 6) NULL COMMENT '较上2月增长率',
  `growth_rate_vs_same_period_last_year` decimal(10, 6) NULL COMMENT '较去年同期增长率',
  `prev_3month_average_value` decimal(15, 2) NULL COMMENT '前3月平均值',
  `prev_3month_slope` decimal(15, 6) NULL COMMENT '前3月斜率',
  `annual_budget` decimal(15, 2) NULL COMMENT '年度预算',
  `current_year_cumulative_amount` decimal(15, 2) NULL COMMENT '当年累计金额',
  `budget_completion_rate` decimal(10, 6) NULL COMMENT '预算完成率',
  `theoretical_progress` decimal(10, 6) NULL COMMENT '理论进度',
  `budget_variance` decimal(10, 6) NULL COMMENT '预算偏差',
  `created_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2655 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '水电费用二级分类特征工程数据表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
