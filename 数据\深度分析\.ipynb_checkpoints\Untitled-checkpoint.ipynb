{"cells": [{"cell_type": "code", "execution_count": 4, "id": "46eb9aeb-63a6-42b6-97cf-50e2584e61a9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔧 开始办公费用特征工程...\n", "📂 读取办公费用基础数据...\n", "原始数据形状: (652, 9)\n", "原始数据列名: ['年', '月', '日', '部门', '摘要', 'Unnamed: 5', '金额', '类别', '费用类型']\n", "\n", "🧹 数据清洗...\n", "清洗后数据形状: (652, 11)\n", "\n", "📅 创建月度时间序列框架...\n", "时间框架: 2022-01 到 2025-12\n", "\n", "📊 计算历史统计特征...\n", "\n", "🎯 创建超宽特征...\n", "发现类别: ['邮寄费' '办公用品' '印刷费' '其他' '计算机耗材' '/']\n", "发现部门: ['本部' '阳春分公司' '阳东分公司' '阳西分公司']\n", "\n", "💰 计算年度预算特征...\n", "年度预算统计: {2022: 1370388.28, 2023: 1186225.45, 2024: 1842540.16}\n", "\n", "🔨 构建最终特征数据集...\n", "\n", "⏰ 添加时间特征...\n", "\n", "📈 添加优化的滞后特征...\n", "🎯 计算动态窗口特征...\n", "💰 计算累计费用特征...\n", "\n", "🔧 处理缺失值...\n", "\n", "📊 添加累积特征...\n", "\n", "🚀 添加扩展特征工程...\n", "🌸 计算季节性特征...\n", "📊 计算波动性指标...\n", "📈 计算增长率特征...\n", "🏷️ 计算类别占比特征...\n", "🔍 计算异常值检测特征...\n", "\n", "💾 保存特征工程结果...\n", "✅ 特征工程完成！\n", "📊 最终数据形状: (48, 66)\n", "📁 输出文件: 办公费用特征工程数据.csv\n", "\n", "📋 特征统计信息:\n", "============================================================\n", "时间范围: 2022-01 到 2025-12\n", "总特征数: 66\n", "类别特征数: 12\n", "部门特征数: 4\n", "动态窗口特征数: 7\n", "累计费用特征数: 2\n", "季节性特征数: 1\n", "增长率特征数: 4\n", "异常值特征数: 3\n", "\n", "🎯 预测配置:\n", "- 目标预测月份: 6月\n", "- 历史数据窗口: 3个月\n", "- 参考月份: [3, 4, 5]\n", "\n", "前5行数据预览:\n", "        年月     年  月  季度         日期      月度总金额    月度平均金额  月度交易次数   月度金额标准差  \\\n", "0  2022-01  2022  1   1 2022-01-01  147810.96   6718.68    22.0  10475.58   \n", "1  2022-02  2022  2   1 2022-02-01   93613.40   6240.89    15.0  11671.61   \n", "2  2022-03  2022  3   1 2022-03-01   79065.35   4392.52    18.0   6701.32   \n", "3  2022-04  2022  4   2 2022-04-01  260733.55  18623.82    14.0  42913.88   \n", "4  2022-05  2022  5   2 2022-05-01   96994.22   5388.57    18.0   7082.87   \n", "\n", "   月度类别数量  ...  类别_邮寄费_占比  类别_办公用品_占比  类别_印刷费_占比  类别_其他_占比  类别_计算机耗材_占比  \\\n", "0     4.0  ...   0.007280    0.493668   0.087822  0.411231          0.0   \n", "1     4.0  ...   0.008097    0.850427   0.129736  0.011740          0.0   \n", "2     4.0  ...   0.007778    0.870401   0.105062  0.016758          0.0   \n", "3     4.0  ...   0.001803    0.325695   0.053906  0.618597          0.0   \n", "4     4.0  ...   0.007763    0.809597   0.111058  0.071582          0.0   \n", "\n", "   类别_/_占比     类别集中度   Z_score  是否异常值  IQR异常值  \n", "0      0.0  0.420584  0.338298      0       0  \n", "1      0.0  0.740261 -0.377568      0       0  \n", "2      0.0  0.768977 -0.569725      0       0  \n", "3      0.0  0.491649  1.829830      0       1  \n", "4      0.0  0.672965 -0.332912      0       0  \n", "\n", "[5 rows x 66 columns]\n", "\n", "📊 特征分类列表:\n", "============================================================\n", "\n", "基础时间特征 (5个):\n", "   1. 年月\n", "   2. 年\n", "   3. 月\n", "   4. 季度\n", "   5. 日期\n", "\n", "基础统计特征 (4个):\n", "   1. 月度总金额\n", "   2. 月度平均金额\n", "   3. 月度交易次数\n", "   4. 月度类别数量\n", "\n", "类别金额特征 (6个):\n", "   1. 类别_邮寄费_金额\n", "   2. 类别_办公用品_金额\n", "   3. 类别_印刷费_金额\n", "   4. 类别_其他_金额\n", "   5. 类别_计算机耗材_金额\n", "   6. 类别_/_金额\n", "\n", "类别占比特征 (6个):\n", "   1. 类别_邮寄费_占比\n", "   2. 类别_办公用品_占比\n", "   3. 类别_印刷费_占比\n", "   4. 类别_其他_占比\n", "   5. 类别_计算机耗材_占比\n", "   6. 类别_/_占比\n", "\n", "部门特征 (4个):\n", "   1. 部门_本部_金额\n", "   2. 部门_阳春分公司_金额\n", "   3. 部门_阳东分公司_金额\n", "   4. 部门_阳西分公司_金额\n", "\n", "时间周期特征 (11个):\n", "   1. 是否年初\n", "   2. 是否年中\n", "   3. 是否年末\n", "   4. 是否上半年\n", "   5. 是否第一季度\n", "   6. 是否第二季度\n", "   7. 是否第三季度\n", "   8. 是否第四季度\n", "   9. 月份_sin\n", "  10. 月份_cos\n", "  11. 是否异常值\n", "\n", "滞后特征 (5个):\n", "   1. 上月总金额\n", "   2. 前2月总金额\n", "   3. 前3月总金额\n", "   4. 去年同期金额\n", "   5. 目标月前累计费用\n", "\n", "动态窗口特征 (7个):\n", "   1. 参考期平均金额\n", "   2. 参考期最大金额\n", "   3. 参考期最小金额\n", "   4. 参考期总金额\n", "   5. 参考期标准差\n", "   6. 参考期变化斜率\n", "   7. 参考期累计变化斜率\n", "\n", "累计费用特征 (2个):\n", "   1. 目标月前累计费用\n", "   2. 参考期累计变化斜率\n", "\n", "趋势特征 (7个):\n", "   1. 参考期变化斜率\n", "   2. 参考期累计变化斜率\n", "   3. 6月移动平均\n", "   4. 月度增长率\n", "   5. 环比增长率\n", "   6. 同比增长率\n", "   7. 季度环比增长率\n", "\n", "季节性特征 (1个):\n", "   1. 季节性指数\n", "\n", "波动性特征 (6个):\n", "   1. 月度金额标准差\n", "   2. 参考期标准差\n", "   3. 3月滚动标准差\n", "   4. 6月滚动标准差\n", "   5. 变异系数\n", "   6. 3月极差\n", "\n", "异常值特征 (3个):\n", "   1. Z_score\n", "   2. 是否异常值\n", "   3. IQR异常值\n", "\n", "其他特征 (3个):\n", "   1. 年度预算\n", "   2. 下月预测目标\n", "   3. 类别集中度\n", "\n", "📈 数据统计:\n", "- 历史数据月份数: 36\n", "- 预测月份数: 12\n", "- 平均月度金额: 122198.72\n", "- 最高月度金额: 376865.60\n", "\n", "🎯 6月预测特征示例:\n", "- 参考期平均金额: 145597.71\n", "- 参考期总金额: 436793.12\n", "- 参考期变化斜率: 8964.44\n", "- 目标月前累计费用: 678217.48\n", "- 参考期累计变化斜率: 178863.89\n", "- 季节性指数: 0.64\n", "\n", "✅ 优化后的特征工程完成！新增了 16 个高级特征\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from datetime import datetime, timedelta\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 尝试导入scipy，如果失败则使用替代方案\n", "try:\n", "    from scipy import stats\n", "    HAS_SCIPY = True\n", "except ImportError:\n", "    HAS_SCIPY = False\n", "    print(\"⚠️ scipy未安装，将使用简化的异常值检测方法\")\n", "\n", "# 配置参数\n", "PREDICTION_CONFIG = {\n", "    'target_month': 6,  # 预测目标月份\n", "    'history_window': 3,  # 历史数据窗口长度（月）\n", "    'reference_months': [3, 4, 5]  # 参考月份（3月、4月、5月）\n", "}\n", "\n", "print(\"🔧 开始办公费用特征工程...\")\n", "\n", "# 1. 读取基础数据\n", "print(\"📂 读取办公费用基础数据...\")\n", "try:\n", "    df = pd.read_csv('办公费用.csv', encoding='utf-8')\n", "except:\n", "    try:\n", "        df = pd.read_csv('办公费用.csv', encoding='gbk')\n", "    except:\n", "        print(\"❌ 无法读取办公费用.csv文件，请检查文件路径\")\n", "        exit()\n", "\n", "print(f\"原始数据形状: {df.shape}\")\n", "print(\"原始数据列名:\", df.columns.tolist())\n", "\n", "# 2. 数据清洗和预处理\n", "print(\"\\n🧹 数据清洗...\")\n", "# 处理金额字段\n", "df['金额'] = df['金额'].astype(str).str.replace(',', '').astype(float)\n", "# 删除全空列\n", "df = df.dropna(axis=1, how='all')\n", "# 处理类别统一\n", "if '类别' in df.columns:\n", "    df['类别'] = df['类别'].replace('邮寄', '邮寄费')\n", "else:\n", "    df['类别'] = '默认类别'\n", "\n", "# 生成标准化日期字段\n", "df['年月'] = df['年'].astype(str) + '-' + df['月'].astype(str).str.zfill(2)\n", "df['日期'] = pd.to_datetime(df['年月'] + '-01')\n", "\n", "print(f\"清洗后数据形状: {df.shape}\")\n", "\n", "# 3. 创建月度时间序列基础框架\n", "print(\"\\n📅 创建月度时间序列框架...\")\n", "start_date = df['日期'].min()\n", "end_date = df['日期'].max() + pd.DateOffset(months=12)  # 延伸12个月用于预测\n", "date_range = pd.date_range(start=start_date, end=end_date, freq='MS')\n", "\n", "# 创建基础时间框架\n", "base_df = pd.DataFrame({\n", "    '年月': date_range.strftime('%Y-%m'),\n", "    '年': date_range.year,\n", "    '月': date_range.month,\n", "    '季度': date_range.quarter,\n", "    '日期': date_range\n", "})\n", "\n", "print(f\"时间框架: {base_df['年月'].min()} 到 {base_df['年月'].max()}\")\n", "\n", "# 4. 计算历史统计特征\n", "print(\"\\n📊 计算历史统计特征...\")\n", "# 按月汇总历史数据\n", "monthly_summary = df.groupby('年月').agg({\n", "    '金额': ['sum', 'mean', 'count', 'std']\n", "}).round(2)\n", "\n", "monthly_summary.columns = ['月度总金额', '月度平均金额', '月度交易次数', '月度金额标准差']\n", "\n", "# 添加类别数量统计\n", "if '类别' in df.columns:\n", "    category_count = df.groupby('年月')['类别'].nunique()\n", "    monthly_summary['月度类别数量'] = category_count\n", "\n", "monthly_summary = monthly_summary.reset_index()\n", "monthly_summary = monthly_summary.fillna(0)\n", "\n", "# 5. 创建超宽特征\n", "print(\"\\n🎯 创建超宽特征...\")\n", "# 获取所有类别\n", "categories = df['类别'].unique() if '类别' in df.columns else ['默认类别']\n", "departments = df['部门'].unique() if '部门' in df.columns else ['默认部门']\n", "\n", "print(f\"发现类别: {categories}\")\n", "print(f\"发现部门: {departments}\")\n", "\n", "# 为每个月创建类别金额特征\n", "category_features = {}\n", "for category in categories:\n", "    cat_data = df[df['类别'] == category].groupby('年月')['金额'].sum()\n", "    category_features[f'类别_{category}_金额'] = cat_data\n", "\n", "# 为每个月创建部门金额特征\n", "dept_features = {}\n", "for dept in departments:\n", "    if '部门' in df.columns:\n", "        dept_data = df[df['部门'] == dept].groupby('年月')['金额'].sum()\n", "    else:\n", "        dept_data = df.groupby('年月')['金额'].sum()  # 默认处理\n", "    dept_features[f'部门_{dept}_金额'] = dept_data\n", "\n", "# 6. 计算年度预算特征\n", "print(\"\\n💰 计算年度预算特征...\")\n", "yearly_budget = df.groupby('年')['金额'].sum().to_dict()\n", "print(\"年度预算统计:\", yearly_budget)\n", "\n", "# 7. 创建最终特征数据集\n", "print(\"\\n🔨 构建最终特征数据集...\")\n", "feature_df = base_df.copy()\n", "\n", "# 合并月度统计特征\n", "feature_df = feature_df.merge(monthly_summary, on='年月', how='left')\n", "\n", "# 添加类别特征\n", "for feature_name, feature_data in category_features.items():\n", "    feature_df[feature_name] = feature_df['年月'].map(feature_data).fillna(0)\n", "\n", "# 添加部门特征\n", "for feature_name, feature_data in dept_features.items():\n", "    feature_df[feature_name] = feature_df['年月'].map(feature_data).fillna(0)\n", "\n", "# 添加年度预算特征\n", "feature_df['年度预算'] = feature_df['年'].map(yearly_budget)\n", "# 对于未来年份，使用历史平均值\n", "avg_budget = np.mean(list(yearly_budget.values()))\n", "feature_df['年度预算'] = feature_df['年度预算'].fillna(avg_budget)\n", "\n", "# 8. 添加时间特征\n", "print(\"\\n⏰ 添加时间特征...\")\n", "feature_df['是否年初'] = (feature_df['月'] <= 3).astype(int)\n", "feature_df['是否年中'] = ((feature_df['月'] >= 4) & (feature_df['月'] <= 9)).astype(int)\n", "feature_df['是否年末'] = (feature_df['月'] >= 10).astype(int)\n", "feature_df['是否上半年'] = (feature_df['月'] <= 6).astype(int)\n", "feature_df['是否第一季度'] = (feature_df['季度'] == 1).astype(int)\n", "feature_df['是否第二季度'] = (feature_df['季度'] == 2).astype(int)\n", "feature_df['是否第三季度'] = (feature_df['季度'] == 3).astype(int)\n", "feature_df['是否第四季度'] = (feature_df['季度'] == 4).astype(int)\n", "\n", "# 添加月份的周期性特征\n", "feature_df['月份_sin'] = np.sin(2 * np.pi * feature_df['月'] / 12)\n", "feature_df['月份_cos'] = np.cos(2 * np.pi * feature_df['月'] / 12)\n", "\n", "# ==================== 辅助函数定义 ====================\n", "def calculate_trend_slope(values):\n", "    \"\"\"计算数值序列的线性趋势斜率\"\"\"\n", "    if len(values) < 2 or values.isna().all():\n", "        return 0\n", "    valid_values = values.dropna()\n", "    if len(valid_values) < 2:\n", "        return 0\n", "    x = np.arange(len(valid_values))\n", "    try:\n", "        slope, _ = np.polyfit(x, valid_values, 1)\n", "        return slope\n", "    except:\n", "        return 0\n", "\n", "def calculate_compound_growth_rate(values):\n", "    \"\"\"计算复合月增长率\"\"\"\n", "    if len(values) < 2:\n", "        return 0\n", "    valid_values = values.dropna()\n", "    if len(valid_values) < 2:\n", "        return 0\n", "\n", "    first_val = valid_values.iloc[0]\n", "    last_val = valid_values.iloc[-1]\n", "\n", "    if first_val <= 0:\n", "        return 0\n", "\n", "    periods = len(valid_values) - 1\n", "    if periods <= 0:\n", "        return 0\n", "\n", "    try:\n", "        compound_rate = (last_val / first_val) ** (1/periods) - 1\n", "        return compound_rate\n", "    except:\n", "        return 0\n", "\n", "def calculate_budget_deviation(actual_amount, budget_amount):\n", "    \"\"\"计算预算偏差\"\"\"\n", "    if budget_amount <= 0:\n", "        return 0\n", "    return (actual_amount - budget_amount) / budget_amount\n", "\n", "# 9. 按需求重新设计特征工程\n", "print(\"\\n📈 按需求计算特征...\")\n", "feature_df = feature_df.sort_values('日期')\n", "\n", "# 首先确保有类别特征列\n", "category_cols = [col for col in feature_df.columns if col.startswith('类别_') and col.endswith('_金额')]\n", "print(f\"发现类别特征: {category_cols}\")\n", "\n", "# 9.1 上个月特征\n", "print(\"📊 计算上个月特征...\")\n", "feature_df['上月总金额'] = feature_df['月度总金额'].shift(1)\n", "\n", "# 上个月各类别金额\n", "for col in category_cols:\n", "    new_col_name = col.replace('类别_', '上月类别_')\n", "    feature_df[new_col_name] = feature_df[col].shift(1)\n", "\n", "# 上个月累计预算偏差（需要先计算月度预算）\n", "feature_df['月度预算'] = feature_df['年度预算'] / 12\n", "feature_df['上月预算偏差'] = feature_df.apply(\n", "    lambda row: calculate_budget_deviation(row['上月总金额'], row['月度预算']), axis=1\n", ")\n", "\n", "# 上个月复合月增长率（基于前3个月数据计算）\n", "feature_df['上月复合增长率'] = feature_df['月度总金额'].rolling(window=3).apply(\n", "    lambda x: calculate_compound_growth_rate(x), raw=False\n", ").shift(1)\n", "\n", "# 较上个月的增长率\n", "feature_df['较上月增长率'] = feature_df['月度总金额'].pct_change()\n", "\n", "# 9.2 上2个月特征\n", "print(\"� 计算上2个月特征...\")\n", "feature_df['上2月总金额'] = feature_df['月度总金额'].shift(2)\n", "\n", "# 上2个月各类别金额\n", "for col in category_cols:\n", "    new_col_name = col.replace('类别_', '上2月类别_')\n", "    feature_df[new_col_name] = feature_df[col].shift(2)\n", "\n", "# 上2个月累计预算偏差\n", "feature_df['上2月预算偏差'] = feature_df.apply(\n", "    lambda row: calculate_budget_deviation(row['上2月总金额'], row['月度预算']), axis=1\n", ")\n", "\n", "# 上2个月复合月增长率\n", "feature_df['上2月复合增长率'] = feature_df['月度总金额'].rolling(window=3).apply(\n", "    lambda x: calculate_compound_growth_rate(x), raw=False\n", ").shift(2)\n", "\n", "# 9.3 上3个月特征\n", "print(\"📊 计算上3个月特征...\")\n", "feature_df['上3月总金额'] = feature_df['月度总金额'].shift(3)\n", "\n", "# 上3个月各类别金额\n", "for col in category_cols:\n", "    new_col_name = col.replace('类别_', '上3月类别_')\n", "    feature_df[new_col_name] = feature_df[col].shift(3)\n", "\n", "# 上3个月累计预算偏差\n", "feature_df['上3月预算偏差'] = feature_df.apply(\n", "    lambda row: calculate_budget_deviation(row['上3月总金额'], row['月度预算']), axis=1\n", ")\n", "\n", "# 上3个月复合月增长率\n", "feature_df['上3月复合增长率'] = feature_df['月度总金额'].rolling(window=3).apply(\n", "    lambda x: calculate_compound_growth_rate(x), raw=False\n", ").shift(3)\n", "\n", "# 9.4 去年同期特征\n", "print(\"📊 计算去年同期特征...\")\n", "feature_df['去年同期总金额'] = feature_df['月度总金额'].shift(12)\n", "\n", "# 去年同期各类别金额\n", "for col in category_cols:\n", "    new_col_name = col.replace('类别_', '去年同期类别_')\n", "    feature_df[new_col_name] = feature_df[col].shift(12)\n", "\n", "# 较去年同期增长率\n", "feature_df['较去年同期增长率'] = ((feature_df['月度总金额'] - feature_df['去年同期总金额']) /\n", "                              (feature_df['去年同期总金额'] + 1e-8))\n", "\n", "# 9.5 斜率特征\n", "print(\"📊 计算斜率特征...\")\n", "# 前3个月总金额的斜率\n", "feature_df['前3月斜率'] = feature_df['月度总金额'].rolling(window=3).apply(\n", "    lambda x: calculate_trend_slope(x), raw=False\n", ")\n", "\n", "# 前6个月总金额的斜率\n", "feature_df['前6月斜率'] = feature_df['月度总金额'].rolling(window=6).apply(\n", "    lambda x: calculate_trend_slope(x), raw=False\n", ")\n", "\n", "# 9.6 时间特征\n", "print(\"📊 计算时间特征...\")\n", "feature_df['是否年初'] = (feature_df['月'] <= 3).astype(int)\n", "feature_df['是否年中'] = ((feature_df['月'] >= 4) & (feature_df['月'] <= 9)).astype(int)\n", "feature_df['是否年末'] = (feature_df['月'] >= 10).astype(int)\n", "feature_df['是否上半年'] = (feature_df['月'] <= 6).astype(int)\n", "feature_df['是否第一季度'] = (feature_df['季度'] == 1).astype(int)\n", "feature_df['是否第二季度'] = (feature_df['季度'] == 2).astype(int)\n", "feature_df['是否第三季度'] = (feature_df['季度'] == 3).astype(int)\n", "feature_df['是否第四季度'] = (feature_df['季度'] == 4).astype(int)\n", "\n", "# 9.7 平均值特征\n", "print(\"📊 计算平均值特征...\")\n", "# 前3个月总金额平均值\n", "feature_df['前3月平均值'] = feature_df['月度总金额'].rolling(window=3).mean()\n", "\n", "# 去年同期三个月平均值\n", "feature_df['去年同期3月平均值'] = feature_df['前3月平均值'].shift(12)\n", "\n", "# 10. 填充缺失值\n", "print(\"\\n🔧 处理缺失值...\")\n", "# 数值型特征用0填充\n", "numeric_cols = feature_df.select_dtypes(include=[np.number]).columns\n", "feature_df[numeric_cols] = feature_df[numeric_cols].fillna(0)\n", "\n", "# 11. 添加目标变量（下月金额，用于预测）\n", "feature_df['下月预测目标'] = feature_df['月度总金额'].shift(-1)\n", "\n", "# 12. 添加累积特征\n", "print(\"\\n📊 添加累积特征...\")\n", "feature_df['年内累积金额'] = feature_df.groupby('年')['月度总金额'].cumsum()\n", "feature_df['年内累积占比'] = feature_df['年内累积金额'] / feature_df['年度预算']\n", "\n", "# 13. 添加扩展的高级特征工程\n", "print(\"\\n🚀 添加扩展特征工程...\")\n", "\n", "# 13.1 季节性特征\n", "print(\"🌸 计算季节性特征...\")\n", "# 基于历史数据计算每月的季节性指数\n", "monthly_seasonal = feature_df[feature_df['月度总金额'] > 0].groupby('月')['月度总金额'].mean()\n", "overall_mean = feature_df[feature_df['月度总金额'] > 0]['月度总金额'].mean()\n", "seasonal_index = (monthly_seasonal / overall_mean).to_dict()\n", "feature_df['季节性指数'] = feature_df['月'].map(seasonal_index).fillna(1.0)\n", "\n", "# 13.2 波动性指标\n", "print(\"📊 计算波动性指标...\")\n", "# 计算滚动标准差和变异系数\n", "feature_df['3月滚动标准差'] = feature_df['月度总金额'].rolling(window=3).std()\n", "feature_df['6月滚动标准差'] = feature_df['月度总金额'].rolling(window=6).std()\n", "feature_df['变异系数'] = feature_df['3月滚动标准差'] / (feature_df['参考期平均金额'] + 1e-8)\n", "\n", "# 计算极差（最大值-最小值）\n", "feature_df['3月极差'] = (feature_df['月度总金额'].rolling(window=3).max() -\n", "                      feature_df['月度总金额'].rolling(window=3).min())\n", "\n", "# 13.3 同比/环比增长率特征\n", "print(\"📈 计算增长率特征...\")\n", "# 同比增长率（与去年同月比较）\n", "feature_df['同比增长率'] = ((feature_df['月度总金额'] - feature_df['去年同期金额']) /\n", "                        (feature_df['去年同期金额'] + 1e-8))\n", "\n", "# 季度环比增长率\n", "feature_df['季度环比增长率'] = ((feature_df['月度总金额'] - feature_df['前3月总金额']) /\n", "                           (feature_df['前3月总金额'] + 1e-8))\n", "\n", "# 13.4 类别占比特征\n", "print(\"🏷️ 计算类别占比特征...\")\n", "# 计算各类别在总费用中的占比\n", "category_cols = [col for col in feature_df.columns if col.startswith('类别_') and col.endswith('_金额')]\n", "total_category_amount = feature_df[category_cols].sum(axis=1)\n", "\n", "for col in category_cols:\n", "    ratio_col = col.replace('_金额', '_占比')\n", "    feature_df[ratio_col] = feature_df[col] / (total_category_amount + 1e-8)\n", "\n", "# 计算类别集中度（赫芬达尔指数）\n", "category_ratios = [col for col in feature_df.columns if col.startswith('类别_') and col.endswith('_占比')]\n", "feature_df['类别集中度'] = sum(feature_df[col] ** 2 for col in category_ratios)\n", "\n", "# 13.5 异常值检测特征\n", "print(\"🔍 计算异常值检测特征...\")\n", "# 基于Z-score的异常值检测\n", "historical_data = feature_df[feature_df['月度总金额'] > 0]['月度总金额']\n", "if len(historical_data) > 0:\n", "    mean_val = historical_data.mean()\n", "    std_val = historical_data.std()\n", "    feature_df['Z_score'] = (feature_df['月度总金额'] - mean_val) / (std_val + 1e-8)\n", "    feature_df['是否异常值'] = (np.abs(feature_df['Z_score']) > 2).astype(int)\n", "else:\n", "    feature_df['Z_score'] = 0\n", "    feature_df['是否异常值'] = 0\n", "\n", "# 基于IQR的异常值检测\n", "if len(historical_data) > 0:\n", "    Q1 = historical_data.quantile(0.25)\n", "    Q3 = historical_data.quantile(0.75)\n", "    IQR = Q3 - Q1\n", "    lower_bound = Q1 - 1.5 * IQR\n", "    upper_bound = Q3 + 1.5 * IQR\n", "    feature_df['IQR异常值'] = ((feature_df['月度总金额'] < lower_bound) |\n", "                             (feature_df['月度总金额'] > upper_bound)).astype(int)\n", "else:\n", "    feature_df['IQR异常值'] = 0\n", "\n", "# 13. 输出结果\n", "print(\"\\n💾 保存特征工程结果...\")\n", "output_file = '办公费用特征工程数据.csv'\n", "feature_df.to_csv(output_file, index=False, encoding='utf-8-sig')\n", "\n", "print(f\"✅ 特征工程完成！\")\n", "print(f\"📊 最终数据形状: {feature_df.shape}\")\n", "print(f\"📁 输出文件: {output_file}\")\n", "\n", "# 15. 显示特征统计\n", "print(\"\\n📋 特征统计信息:\")\n", "print(\"=\" * 60)\n", "print(f\"时间范围: {feature_df['年月'].min()} 到 {feature_df['年月'].max()}\")\n", "print(f\"总特征数: {len(feature_df.columns)}\")\n", "print(f\"类别特征数: {len([col for col in feature_df.columns if '类别_' in col])}\")\n", "print(f\"部门特征数: {len([col for col in feature_df.columns if '部门_' in col])}\")\n", "\n", "# 新增特征统计\n", "print(f\"动态窗口特征数: {len([col for col in feature_df.columns if '参考期' in col])}\")\n", "print(f\"累计费用特征数: {len([col for col in feature_df.columns if '累计' in col])}\")\n", "print(f\"季节性特征数: {len([col for col in feature_df.columns if '季节' in col or '波动' in col])}\")\n", "print(f\"增长率特征数: {len([col for col in feature_df.columns if '增长率' in col])}\")\n", "print(f\"异常值特征数: {len([col for col in feature_df.columns if '异常' in col or 'Z_score' in col])}\")\n", "\n", "print(f\"\\n🎯 预测配置:\")\n", "print(f\"- 目标预测月份: {PREDICTION_CONFIG['target_month']}月\")\n", "print(f\"- 历史数据窗口: {PREDICTION_CONFIG['history_window']}个月\")\n", "print(f\"- 参考月份: {PREDICTION_CONFIG['reference_months']}\")\n", "\n", "print(\"\\n前5行数据预览:\")\n", "print(feature_df.head())\n", "\n", "print(\"\\n📊 特征分类列表:\")\n", "print(\"=\" * 60)\n", "\n", "# 按类别显示特征\n", "feature_categories = {\n", "    '基础时间特征': [col for col in feature_df.columns if col in ['年月', '年', '月', '季度', '日期']],\n", "    '基础统计特征': [col for col in feature_df.columns if any(x in col for x in ['月度总金额', '月度平均金额', '月度交易次数', '月度标准差', '月度类别数量'])],\n", "    '类别金额特征': [col for col in feature_df.columns if col.startswith('类别_') and col.endswith('_金额')],\n", "    '类别占比特征': [col for col in feature_df.columns if col.startswith('类别_') and col.endswith('_占比')],\n", "    '部门特征': [col for col in feature_df.columns if col.startswith('部门_')],\n", "    '时间周期特征': [col for col in feature_df.columns if any(x in col for x in ['是否', '月份_sin', '月份_cos'])],\n", "    '滞后特征': [col for col in feature_df.columns if any(x in col for x in ['上月', '前', '去年'])],\n", "    '动态窗口特征': [col for col in feature_df.columns if '参考期' in col],\n", "    '累计费用特征': [col for col in feature_df.columns if '累计' in col],\n", "    '趋势特征': [col for col in feature_df.columns if any(x in col for x in ['移动平均', '增长率', '斜率'])],\n", "    '季节性特征': [col for col in feature_df.columns if '季节' in col],\n", "    '波动性特征': [col for col in feature_df.columns if any(x in col for x in ['标准差', '变异系数', '极差'])],\n", "    '异常值特征': [col for col in feature_df.columns if any(x in col for x in ['异常', 'Z_score'])],\n", "    '其他特征': [col for col in feature_df.columns if any(x in col for x in ['年度预算', '集中度', '下月预测目标'])]\n", "}\n", "\n", "for category, features in feature_categories.items():\n", "    if features:\n", "        print(f\"\\n{category} ({len(features)}个):\")\n", "        for i, feature in enumerate(features, 1):\n", "            print(f\"  {i:2d}. {feature}\")\n", "\n", "print(f\"\\n📈 数据统计:\")\n", "print(f\"- 历史数据月份数: {len(feature_df[feature_df['月度总金额'] > 0])}\")\n", "print(f\"- 预测月份数: {len(feature_df[feature_df['月度总金额'] == 0])}\")\n", "print(f\"- 平均月度金额: {feature_df[feature_df['月度总金额'] > 0]['月度总金额'].mean():.2f}\")\n", "print(f\"- 最高月度金额: {feature_df['月度总金额'].max():.2f}\")\n", "\n", "# 显示预测6月的特征示例\n", "june_data = feature_df[feature_df['月'] == PREDICTION_CONFIG['target_month']]\n", "if len(june_data) > 0:\n", "    print(f\"\\n🎯 {PREDICTION_CONFIG['target_month']}月预测特征示例:\")\n", "    sample_june = june_data.iloc[0]\n", "    key_features = ['参考期平均金额', '参考期总金额', '参考期变化斜率',\n", "                   '目标月前累计费用', '参考期累计变化斜率', '季节性指数']\n", "    for feature in key_features:\n", "        if feature in sample_june:\n", "            print(f\"- {feature}: {sample_june[feature]:.2f}\")\n", "\n", "print(f\"\\n✅ 优化后的特征工程完成！新增了 {len(feature_df.columns) - 50} 个高级特征\")"]}, {"cell_type": "code", "execution_count": 2, "id": "181d8537-a207-4608-a72c-4a86af53e7f4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 开始完整特征工程流水线...\n", "============================================================\n", "🔧 开始办公费用特征工程\n", "============================================================\n", "📂 正在加载数据...\n", "✅ 成功使用 utf-8 编码读取数据\n", "📊 原始数据形状: (652, 9)\n", "📋 数据列名: ['年', '月', '日', '部门', '摘要', 'Unnamed: 5', '金额', '类别', '费用类型']\n", "\n", "📋 原始数据预览:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>年</th>\n", "      <th>月</th>\n", "      <th>日</th>\n", "      <th>部门</th>\n", "      <th>摘要</th>\n", "      <th>Unnamed: 5</th>\n", "      <th>金额</th>\n", "      <th>类别</th>\n", "      <th>费用类型</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2022</td>\n", "      <td>1</td>\n", "      <td>11</td>\n", "      <td>本部</td>\n", "      <td>报销2021年12月份顺丰快递费</td>\n", "      <td>报销2021年12月份顺丰快递费</td>\n", "      <td>888.00</td>\n", "      <td>邮寄费</td>\n", "      <td>管理费用</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2022</td>\n", "      <td>1</td>\n", "      <td>13</td>\n", "      <td>阳春分公司</td>\n", "      <td>报销阳春分公司刻章费用</td>\n", "      <td>报销阳春分公司刻章费用</td>\n", "      <td>660.00</td>\n", "      <td>办公用品</td>\n", "      <td>管理费用</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2022</td>\n", "      <td>1</td>\n", "      <td>13</td>\n", "      <td>阳春分公司</td>\n", "      <td>报销阳春分公司采购茶叶一批费用</td>\n", "      <td>报销阳春分公司采购茶叶一批费用</td>\n", "      <td>1,900.00</td>\n", "      <td>办公用品</td>\n", "      <td>管理费用</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2022</td>\n", "      <td>1</td>\n", "      <td>17</td>\n", "      <td>本部</td>\n", "      <td>报销12月份宣传用品印刷等采购费用</td>\n", "      <td>报销12月份宣传用品印刷等采购费用</td>\n", "      <td>8,603.00</td>\n", "      <td>印刷费</td>\n", "      <td>管理费用</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2022</td>\n", "      <td>1</td>\n", "      <td>17</td>\n", "      <td>本部</td>\n", "      <td>报销12月份茶叶采购费用</td>\n", "      <td>报销12月份茶叶采购费用</td>\n", "      <td>5,200.00</td>\n", "      <td>办公用品</td>\n", "      <td>管理费用</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      年  月   日     部门                 摘要         Unnamed: 5        金额    类别  \\\n", "0  2022  1  11     本部   报销2021年12月份顺丰快递费   报销2021年12月份顺丰快递费    888.00   邮寄费   \n", "1  2022  1  13  阳春分公司        报销阳春分公司刻章费用        报销阳春分公司刻章费用    660.00  办公用品   \n", "2  2022  1  13  阳春分公司    报销阳春分公司采购茶叶一批费用    报销阳春分公司采购茶叶一批费用  1,900.00  办公用品   \n", "3  2022  1  17     本部  报销12月份宣传用品印刷等采购费用  报销12月份宣传用品印刷等采购费用  8,603.00   印刷费   \n", "4  2022  1  17     本部       报销12月份茶叶采购费用       报销12月份茶叶采购费用  5,200.00  办公用品   \n", "\n", "   费用类型  \n", "0  管理费用  \n", "1  管理费用  \n", "2  管理费用  \n", "3  管理费用  \n", "4  管理费用  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "========================================\n", "🧹 数据清洗和预处理\n", "========================================\n", "💰 处理金额字段...\n", "📝 处理类别字段...\n", "🏢 处理部门字段...\n", "📅 处理时间字段...\n", "✅ 数据清洗完成，清洗后形状: (652, 12)\n", "\n", "========================================\n", "📅 创建时间序列框架\n", "========================================\n", "⏰ 时间范围: 2022-01 到 2025-12\n", "📊 生成时间框架: 48 个月\n", "\n", "========================================\n", "📊 创建基础统计特征\n", "========================================\n", "✅ 基础特征创建完成\n", "\n", "========================================\n", "📈 创建历史特征\n", "========================================\n", "✅ 历史特征创建完成\n", "\n", "========================================\n", "📝 创建类别特征\n", "========================================\n", "✅ 类别特征创建完成\n", "\n", "========================================\n", "🏢 创建部门特征\n", "========================================\n", "✅ 部门特征创建完成\n", "\n", "========================================\n", "💰 创建预算特征\n", "========================================\n", "✅ 预算特征创建完成\n", "\n", "========================================\n", "⏰ 创建时间特征\n", "========================================\n", "✅ 时间特征创建完成\n", "\n", "========================================\n", "🎯 创建目标变量\n", "========================================\n", "✅ 目标变量创建完成\n", "\n", "========================================\n", "🔧 最终化特征处理\n", "========================================\n", "✅ 最终化处理完成，特征数: 39\n", "\n", "========================================\n", "💾 保存结果\n", "========================================\n", "✅ 结果已保存到: 办公费用特征工程数据.csv\n", "\n", "========================================\n", "📊 生成报告\n", "========================================\n", "特征工程完成！\n", "- 数据行数: 48\n", "- 特征数量: 39\n", "- 时间范围: 2022-01 到 2025-12\n", "\n", "🎉 特征工程流水线执行完成！\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}