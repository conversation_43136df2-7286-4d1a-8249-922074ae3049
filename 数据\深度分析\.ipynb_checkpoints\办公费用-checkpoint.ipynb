{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 办公费用分析\n", "本Notebook将带你一步步完成办公费用的机器学习分析，包括：\n", "1. 预测未来某个月的办公费用\n", "2. 统计每月固定和不固定费用\n", "3. 统计每年都会有的办公费用\n", "4. 结论输出与可视化\n", "\n", "> 请确保办公费用.csv文件与本notebook在同一目录下。"]}, {"cell_type": "code", "metadata": {}, "source": ["# 1. 导入常用库\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import mean_absolute_error\n", "\n", "# 显示中文和负号\n", "plt.rcParams['font.sans-serif'] = ['SimHei']\n", "plt.rcParams['axes.unicode_minus'] = False"]}, {"cell_type": "code", "metadata": {}, "source": ["# 2. 读取数据\n", "df = pd.read_csv('办公费用.csv', encoding='utf-8')\n", "df.head()"]}, {"cell_type": "code", "metadata": {}, "source": ["# 3. 数据清洗\n", "# 处理金额字段，去掉千分位逗号\n", "df['金额'] = df['金额'].astype(str).str.replace(',', '').astype(float)\n", "# 删除全空列\n", "df = df.dropna(axis=1, how='all')\n", "# 处理类别中的不一致（如‘邮寄’与‘邮寄费’）\n", "df['类别'] = df['类别'].replace('邮寄', '邮寄费')\n", "# 生成年月和日期字段\n", "df['年月'] = df['年'].astype(str) + '-' + df['月'].astype(str).str.zfill(2)\n", "df['日期'] = pd.to_datetime(df['年'].astype(str) + '-' + df['月'].astype(str).str.zfill(2) + '-' + df['日'].astype(str).str.zfill(2), errors='coerce')\n", "df.head()"]}, {"cell_type": "code", "metadata": {}, "source": ["# 4. 特征工程\n", "# 只用‘部门’‘类别’‘费用类型’‘年月’做特征\n", "features = ['部门', '类别', '费用类型', '年月']\n", "df_ml = df[features + ['金额']].copy()\n", "df_ml = pd.get_dummies(df_ml, columns=['部门', '类别', '费用类型', '年月'])\n", "df_ml.head()"]}, {"cell_type": "code", "metadata": {}, "source": ["# 5. 机器学习建模预测未来某月办公费用\n", "X = df_ml.drop('金额', axis=1)\n", "y = df_ml['金额']\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "model = RandomForestRegressor(random_state=42)\n", "model.fit(X_train, y_train)\n", "y_pred = model.predict(X_test)\n", "print('模型平均绝对误差：', mean_absolute_error(y_test, y_pred))"]}, {"cell_type": "code", "metadata": {}, "source": ["# 6. 预测未来某个月办公费用的函数\n", "def predict_future(year, month, 部门, 类别, 费用类型):\n", "    future = pd.DataFrame([0]*X.shape[1], index=X.columns).T\n", "    for col in future.columns:\n", "        if col == f'部门_{部门}':\n", "            future[col] = 1\n", "        if col == f'类别_{类别}':\n", "            future[col] = 1\n", "        if col == f'费用类型_{费用类型}':\n", "            future[col] = 1\n", "        if col == f'年月_{year}-{str(month).zfill(2)}':\n", "            future[col] = 1\n", "    return model.predict(future)[0]\n", "\n", "# 示例：预测2024年12月本部-办公用品-管理费用的办公费用\n", "print('预测2024年12月本部-办公用品-管理费用的办公费用：')\n", "print(predict_future(2024, 12, '本部', '办公用品', '管理费用'))"]}, {"cell_type": "code", "metadata": {}, "source": ["# 7. 统计每月固定费用和不固定费用\n", "类别_每月统计 = df.groupby(['类别', '年月']).size().unstack(fill_value=0)\n", "固定费用类别 = 类别_每月统计[(类别_每月统计 > 0).all(axis=1)].index.tolist()\n", "print('每月固定出现的费用类别：', 固定费用类别)\n", "不固定费用类别 = 类别_每月统计[(类别_每月统计 > 0).any(axis=1) & ~(类别_每月统计 > 0).all(axis=1)].index.tolist()\n", "print('不固定出现的费用类别：', 不固定费用类别)"]}, {"cell_type": "code", "metadata": {}, "source": ["# 8. 统计每年都会有的办公费用\n", "类别_每年统计 = df.groupby(['类别', '年']).size().unstack(fill_value=0)\n", "每年都有的类别 = 类别_每年统计[(类别_每年统计 > 0).all(axis=1)].index.tolist()\n", "print('每年都会有的办公费用类别：', 每年都有的类别)"]}, {"cell_type": "code", "metadata": {}, "source": ["# 9. 可视化：每月办公费用总额趋势\n", "plt.figure(figsize=(12,6))\n", "df.groupby('年月')['金额'].sum().plot(marker='o')\n", "plt.title('每月办公费用总额趋势')\n", "plt.ylabel('金额')\n", "plt.xticks(rotation=45)\n", "plt.grid()\n", "plt.show()"]}, {"cell_type": "code", "metadata": {}, "source": ["# 10. 可视化：各类别办公费用总额\n", "plt.figure(figsize=(10,5))\n", "df.groupby('类别')['金额'].sum().sort_values().plot(kind='barh')\n", "plt.title('各类别办公费用总额')\n", "plt.xlabel('金额')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 结论\n", "1. 通过机器学习模型，可以预测未来某个月的办公费用。\n", "2. 固定费用类别为：见上文输出\n", "3. 不固定费用类别为：见上文输出\n", "4. 每年都会有的办公费用类别为：见上文输出\n", "5. 你可以根据predict_future函数，输入不同的部门、类别、费用类型和年月，预测未来的费用。\n", "\n", "如需更详细分析或可视化，欢迎随时补充需求！"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.8"}}, "nbformat": 4, "nbformat_minor": 2}