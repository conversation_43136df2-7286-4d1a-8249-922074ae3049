# 办公费用特征工程项目

## 项目简介

本项目基于办公费用历史数据，生成多维度特征用于费用预测分析。通过深入挖掘历史费用模式、周期性规律和业务特征，为精准的费用预测和预算管控提供数据支撑。

## 数据来源

- **主数据文件**: `办公费用1.csv`
- **数据时间范围**: 2022年1月 - 2024年12月
- **数据记录数**: 649条费用记录
- **主要字段**: 年、月、日、部门、摘要、金额、类别、费用类型

## 特征工程脚本

### 主要脚本文件

1. **`特征工程数据1.py`** - 完整的特征工程脚本（推荐使用）
2. **`特征工程测试.py`** - 简化版测试脚本
3. **`简单测试.py`** - 环境测试脚本

### 生成的特征类别

#### 1. 历史费用特征 (约25个特征)
- **滞后特征**: 费用_lag1, 费用_lag2, 费用_lag3
- **移动平均**: 费用_ma3, 费用_ma6, 费用_ma12
- **时间对比**:
  - 上月费用、上季度费用、去年同期费用
  - 各类别上月/上季度/去年同期费用（办公用品、印刷费、邮寄费等）
- **增长率**: 
  - 费用同比增长率、费用环比增长率
  - 月环比增减额、年同比增减额
- **统计量**: 
  - 费用最大值/最小值/中位数/标准差（6个月、12个月窗口）
  - 费用分位数（25%、75%分位数）

#### 2. 费用类型特征 (约15个特征)
- **费用类别金额**: 当月各类别费用（办公用品、印刷费、邮寄费、其他、计算机耗材）
- **费用类别占比**: 各类别费用占当月总费用的百分比
- **智能标注**:
  - 费用是否周期性（基于变异系数分析）
  - 费用是否临时性（基于异常检测）
- **费用类型**: 当月管理费用、当月销售费用

#### 3. 预算特征 (约6个特征)
- **预算数**: 基于去年同期+10%估算
- **执行分析**:
  - 费用执行率（实际/预算 * 100%）
  - 执行进度差值（实际-预算）
  - 年度累积执行率
- **累积分析**:
  - 累积总费用
  - 累积总费用同比增长率

#### 4. 时间类特征 (约6个特征)
- **基础时间**: 年、月、季度
- **季节特征**: 春、夏、秋、冬
- **工作日特征**: 当月工作日数（考虑中国节假日）

#### 5. 部门特征 (约8个特征)
- **部门费用**: 本部、阳春、阳东、阳西各部门月度费用
- **部门占比**: 各部门费用占当月总费用的百分比

#### 6. 目标变量 (3个特征)
- **下月费用**: 用于回归预测的主要目标
- **下月是否超预算**: 用于分类预测的目标（0/1）
- **下月费用增长率**: 用于增长率预测的目标

## 使用方法

### 环境要求
```bash
pip install pandas numpy holidays
```

### 运行脚本
```bash
# 进入项目目录
cd 数据/深度分析

# 运行完整特征工程
python 特征工程数据1.py

# 或运行简化测试版本
python 特征工程测试.py
```

### 输出文件
- **主输出**: `办公费用特征工程数据1.csv` - 包含所有特征的月度数据
- **数据维度**: 约36行 × 约60列特征

## 特征工程核心亮点

### 1. 业务导向的特征设计
- **费用周期性检测**: 基于变异系数自动识别周期性费用模式
- **异常费用标注**: 使用Z-score检测临时性/异常费用
- **预算执行监控**: 多维度预算执行率分析

### 2. 时间序列特征
- **多层次滞后**: 1-3个月的滞后特征捕捉短期趋势
- **多窗口移动平均**: 3/6/12个月平滑化处理
- **同比环比分析**: 全面的增长率特征

### 3. 类别细分特征
- **分类别历史追踪**: 为每个费用类别生成独立的历史特征
- **占比分析**: 揭示费用结构变化趋势
- **部门对比**: 多部门费用对比分析

### 4. 智能预算特征
- **动态预算估算**: 基于历史数据自动生成预算基线
- **执行率监控**: 实时预算执行情况跟踪
- **偏差分析**: 预算与实际的差异量化

## 应用场景

### 1. 费用预测模型
- 利用历史特征预测下月费用金额
- 预测费用是否会超预算
- 预测费用增长率趋势

### 2. 异常检测
- 识别异常高额费用
- 发现费用模式异常变化
- 预警潜在的预算超支风险

### 3. 预算管控
- 优化预算分配策略
- 提高预算执行精度
- 支持动态预算调整

### 4. 业务分析
- 费用结构分析
- 部门费用对比
- 季节性费用模式识别

## 技术特色

### 1. 鲁棒性设计
- 自动处理缺失值和异常值
- 灵活的时间窗口参数
- 错误处理和异常捕获

### 2. 可扩展性
- 模块化的特征生成函数
- 易于添加新的特征类别
- 支持不同数据源接入

### 3. 业务友好
- 中文特征命名
- 详细的执行日志
- 直观的结果展示

## 注意事项

1. **数据质量**: 确保输入数据的金额字段格式正确
2. **时间连续性**: 最好有连续的月度数据以获得更好的特征效果
3. **预算数据**: 当前预算是基于历史数据估算，实际使用时建议使用真实预算数据
4. **节假日计算**: 工作日特征基于中国节假日，可根据实际情况调整

## 后续优化方向

1. **更多外部特征**: 加入经济指标、行业数据等外部特征
2. **深度特征**: 使用深度学习方法生成更复杂的特征
3. **实时特征**: 支持流式数据的实时特征计算
4. **个性化特征**: 针对不同部门生成定制化特征

---

**作者**: AI助手  
**更新时间**: 2024年12月  
**版本**: v1.0 