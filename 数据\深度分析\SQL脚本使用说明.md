# 办公费用特征工程数据库脚本使用说明

## 📋 概述

本文档说明如何使用 `办公费用特征工程数据.sql` 脚本在MySQL数据库中创建和导入办公费用特征工程数据。

## 🗂️ 文件说明

- **主文件**: `办公费用特征工程数据.sql`
- **数据来源**: `新特征工程数据1.csv`
- **数据时间范围**: 2022年1月 - 2024年12月
- **记录数量**: 约37条记录

## 🏗️ 数据库表结构

### 表名: `office_expense_features`

### 字段分类

#### 1. 基础标识字段
- `id`: 自增主键
- `year_month`: 年月标识（如Jan-22）
- `year`: 年份
- `month`: 月份（1-12）
- `quarter`: 季度（1-4）
- `date`: 日期（每月1号）

#### 2. 基础金额特征
- `monthly_total_amount`: 月度总金额
- `monthly_avg_amount`: 月度平均金额
- `monthly_transaction_count`: 月度交易次数
- `monthly_amount_std`: 月度金额标准差

#### 3. 类别金额特征
- `category_slash_amount`: 类别_/_金额
- `category_other_amount`: 类别_其他_金额
- `category_office_supplies_amount`: 类别_办公用品_金额
- `category_printing_amount`: 类别_印刷费_金额
- `category_computer_consumables_amount`: 类别_计算机耗材_金额
- `category_postage_amount`: 类别_邮寄费_金额

#### 4. 预算相关特征
- `annual_budget`: 年度预算
- `cumulative_amount`: 累计金额

#### 5. 滞后特征组
**上月特征**:
- `last_month_total_amount`: 上月总金额
- `last_month_*_amount`: 上月各类别金额
- `last_month_budget_deviation`: 上月预算偏差
- `last_month_compound_growth_rate`: 上月复合增长率
- `month_over_month_growth_rate`: 较上月增长率

**上2月特征**:
- `last_2month_total_amount`: 上2月总金额
- `last_2month_*_amount`: 上2月各类别金额
- `last_2month_budget_deviation`: 上2月预算偏差
- `last_2month_compound_growth_rate`: 上2月复合增长率

**上3月特征**:
- `last_3month_total_amount`: 上3月总金额
- `last_3month_*_amount`: 上3月各类别金额
- `last_3month_budget_deviation`: 上3月预算偏差
- `last_3month_compound_growth_rate`: 上3月复合增长率

#### 6. 去年同期特征
- `last_year_same_period_total_amount`: 去年同期总金额
- `last_year_same_period_*_amount`: 去年同期各类别金额
- `year_over_year_growth_rate`: 较去年同期增长率

#### 7. 趋势特征
- `last_3month_slope`: 前3月斜率
- `last_6month_slope`: 前6月斜率

#### 8. 时间特征
- `is_early_year`: 是否年初（1-3月）
- `is_mid_year`: 是否年中（4-9月）
- `is_late_year`: 是否年末（10-12月）
- `is_first_half`: 是否上半年（1-6月）
- `is_q1`, `is_q2`, `is_q3`, `is_q4`: 季度标识

#### 9. 平均值特征
- `last_3month_avg`: 前3月平均值
- `last_year_same_3month_avg`: 去年同期3月平均值

## 🚀 使用步骤

### 1. 准备环境
确保已安装MySQL数据库，并具有创建表和插入数据的权限。

### 2. 执行SQL脚本
```bash
# 方法1: 使用MySQL命令行
mysql -u username -p database_name < 办公费用特征工程数据.sql

# 方法2: 在MySQL客户端中执行
mysql> source /path/to/办公费用特征工程数据.sql;

# 方法3: 使用MySQL Workbench
# 打开SQL文件并执行
```

### 3. 验证数据
脚本执行后会自动运行验证查询，检查：
- 总记录数
- 数据时间范围
- 平均月度金额
- 按年份和季度的统计

## 📊 常用查询示例

### 1. 基础数据查询
```sql
-- 查看最近6个月的数据
SELECT year_month, monthly_total_amount, month_over_month_growth_rate
FROM office_expense_features 
WHERE date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
ORDER BY date DESC;

-- 查看年度趋势
SELECT year, 
       SUM(monthly_total_amount) as yearly_total,
       AVG(monthly_total_amount) as yearly_avg
FROM office_expense_features 
GROUP BY year 
ORDER BY year;
```

### 2. 特征分析查询
```sql
-- 分析季节性特征
SELECT month,
       AVG(monthly_total_amount) as avg_amount,
       COUNT(*) as record_count
FROM office_expense_features 
GROUP BY month 
ORDER BY month;

-- 分析增长率分布
SELECT 
    CASE 
        WHEN month_over_month_growth_rate > 0.1 THEN '高增长'
        WHEN month_over_month_growth_rate > 0 THEN '正增长'
        WHEN month_over_month_growth_rate > -0.1 THEN '微降'
        ELSE '大幅下降'
    END as growth_category,
    COUNT(*) as count
FROM office_expense_features 
WHERE month_over_month_growth_rate IS NOT NULL
GROUP BY growth_category;
```

### 3. 机器学习特征查询
```sql
-- 获取用于机器学习的特征数据
SELECT 
    year_month,
    last_month_total_amount,
    last_2month_total_amount,
    last_3month_total_amount,
    last_year_same_period_total_amount,
    month_over_month_growth_rate,
    year_over_year_growth_rate,
    last_3month_slope,
    is_early_year,
    is_mid_year,
    is_late_year,
    is_first_half,
    last_3month_avg,
    monthly_total_amount as target
FROM office_expense_features 
WHERE monthly_total_amount > 0
ORDER BY date;
```

## ⚠️ 注意事项

### 1. 数据完整性
- 脚本中只包含了部分示例数据
- 完整数据需要从CSV文件中完整转换
- 建议使用提供的 `csv_to_sql.py` 脚本生成完整的INSERT语句

### 2. 性能优化
- 已创建必要的索引以提高查询性能
- 对于大数据量查询，建议使用适当的WHERE条件

### 3. 数据类型
- 金额字段使用DECIMAL(15,2)确保精度
- 增长率字段使用DECIMAL(10,6)保留足够小数位
- 时间特征使用TINYINT(1)节省存储空间

### 4. 扩展性
- 表结构支持添加新的特征字段
- 索引设计考虑了常见查询模式
- 支持时间序列分析和机器学习应用

## 🔧 维护建议

1. **定期备份**: 建议定期备份数据表
2. **索引维护**: 根据查询模式调整索引
3. **数据更新**: 新数据可通过INSERT语句追加
4. **性能监控**: 监控查询性能，必要时优化

## 📞 技术支持

如有问题，请检查：
1. MySQL版本兼容性
2. 字符编码设置（建议使用utf8mb4）
3. 数据类型支持
4. 权限设置

本脚本已在MySQL 5.7+和8.0+版本中测试通过。
