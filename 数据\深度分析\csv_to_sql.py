#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
CSV转SQL脚本
将新特征工程数据1.csv转换为SQL INSERT语句
"""

import pandas as pd
import numpy as np
from datetime import datetime

def convert_csv_to_sql():
    """转换CSV文件为SQL INSERT语句"""
    
    print("🔧 开始转换CSV为SQL...")
    
    # 读取CSV文件
    df = pd.read_csv('新特征工程数据1.csv', encoding='utf-8')
    print(f"读取数据: {df.shape}")
    
    # 处理缺失值和特殊值
    df = df.fillna(0)
    
    # 生成INSERT语句
    insert_statements = []
    
    for index, row in df.iterrows():
        # 处理日期格式
        date_str = row['日期']
        if pd.isna(date_str) or date_str == 0:
            continue
            
        try:
            # 转换日期格式
            if '/' in str(date_str):
                date_obj = datetime.strptime(str(date_str), '%Y/%m/%d')
                formatted_date = date_obj.strftime('%Y-%m-%d')
            else:
                formatted_date = '2022-01-01'  # 默认值
        except:
            formatted_date = '2022-01-01'  # 默认值
        
        # 处理数值，确保格式正确
        def format_value(val):
            if pd.isna(val) or val == '':
                return 0
            try:
                # 如果是数值，保留6位小数
                num_val = float(val)
                if abs(num_val) < 0.000001:  # 非常小的数值设为0
                    return 0
                return round(num_val, 6)
            except:
                return 0
        
        # 构建INSERT语句的值部分
        values = [
            f"'{row['年月']}'",  # year_month
            int(row['年']),  # year
            int(row['月']),  # month
            int(row['季度']),  # quarter
            f"'{formatted_date}'",  # date
            format_value(row['月度总金额']),  # monthly_total_amount
            format_value(row['月度平均金额']),  # monthly_avg_amount
            int(format_value(row['月度交易次数'])),  # monthly_transaction_count
            format_value(row['月度金额标准差']),  # monthly_amount_std
            format_value(row['类别_/_金额']),  # category_slash_amount
            format_value(row['类别_其他_金额']),  # category_other_amount
            format_value(row['类别_办公用品_金额']),  # category_office_supplies_amount
            format_value(row['类别_印刷费_金额']),  # category_printing_amount
            format_value(row['类别_计算机耗材_金额']),  # category_computer_consumables_amount
            format_value(row['类别_邮寄费_金额']),  # category_postage_amount
            format_value(row['年度预算']),  # annual_budget
            format_value(row['累计金额']),  # cumulative_amount
            format_value(row['上月总金额']),  # last_month_total_amount
            format_value(row['上月_/_金额']),  # last_month_slash_amount
            format_value(row['上月_其他_金额']),  # last_month_other_amount
            format_value(row['上月_办公用品_金额']),  # last_month_office_supplies_amount
            format_value(row['上月_印刷费_金额']),  # last_month_printing_amount
            format_value(row['上月_计算机耗材_金额']),  # last_month_computer_consumables_amount
            format_value(row['上月_邮寄费_金额']),  # last_month_postage_amount
            format_value(row['上月预算偏差']),  # last_month_budget_deviation
            format_value(row['上月复合增长率']),  # last_month_compound_growth_rate
            format_value(row['较上月增长率']),  # month_over_month_growth_rate
            format_value(row['上2月总金额']),  # last_2month_total_amount
            format_value(row['上2月_/_金额']),  # last_2month_slash_amount
            format_value(row['上2月_其他_金额']),  # last_2month_other_amount
            format_value(row['上2月_办公用品_金额']),  # last_2month_office_supplies_amount
            format_value(row['上2月_印刷费_金额']),  # last_2month_printing_amount
            format_value(row['上2月_计算机耗材_金额']),  # last_2month_computer_consumables_amount
            format_value(row['上2月_邮寄费_金额']),  # last_2month_postage_amount
            format_value(row['上2月预算偏差']),  # last_2month_budget_deviation
            format_value(row['上2月复合增长率']),  # last_2month_compound_growth_rate
            format_value(row['上3月总金额']),  # last_3month_total_amount
            format_value(row['上3月_/_金额']),  # last_3month_slash_amount
            format_value(row['上3月_其他_金额']),  # last_3month_other_amount
            format_value(row['上3月_办公用品_金额']),  # last_3month_office_supplies_amount
            format_value(row['上3月_印刷费_金额']),  # last_3month_printing_amount
            format_value(row['上3月_计算机耗材_金额']),  # last_3month_computer_consumables_amount
            format_value(row['上3月_邮寄费_金额']),  # last_3month_postage_amount
            format_value(row['上3月预算偏差']),  # last_3month_budget_deviation
            format_value(row['上3月复合增长率']),  # last_3month_compound_growth_rate
            format_value(row['去年同期总金额']),  # last_year_same_period_total_amount
            format_value(row['去年同期_/_金额']),  # last_year_same_period_slash_amount
            format_value(row['去年同期_其他_金额']),  # last_year_same_period_other_amount
            format_value(row['去年同期_办公用品_金额']),  # last_year_same_period_office_supplies_amount
            format_value(row['去年同期_印刷费_金额']),  # last_year_same_period_printing_amount
            format_value(row['去年同期_计算机耗材_金额']),  # last_year_same_period_computer_consumables_amount
            format_value(row['去年同期_邮寄费_金额']),  # last_year_same_period_postage_amount
            format_value(row['较去年同期增长率']),  # year_over_year_growth_rate
            format_value(row['前3月斜率']),  # last_3month_slope
            format_value(row['前6月斜率']),  # last_6month_slope
            int(format_value(row['是否年初'])),  # is_early_year
            int(format_value(row['是否年中'])),  # is_mid_year
            int(format_value(row['是否年末'])),  # is_late_year
            int(format_value(row['是否上半年'])),  # is_first_half
            int(format_value(row['是否第一季度'])),  # is_q1
            int(format_value(row['是否第二季度'])),  # is_q2
            int(format_value(row['是否第三季度'])),  # is_q3
            int(format_value(row['是否第四季度'])),  # is_q4
            format_value(row['前3月平均值']),  # last_3month_avg
            format_value(row['去年同期3月平均值'])  # last_year_same_3month_avg
        ]
        
        # 构建完整的INSERT语句
        values_str = '(' + ', '.join(str(v) for v in values) + ')'
        insert_statements.append(values_str)
    
    # 生成完整的SQL
    sql_content = ',\n'.join(insert_statements) + ';'
    
    # 保存到文件
    with open('insert_statements.sql', 'w', encoding='utf-8') as f:
        f.write(sql_content)
    
    print(f"✅ 转换完成！生成了 {len(insert_statements)} 条INSERT语句")
    print("📁 输出文件: insert_statements.sql")
    
    return sql_content

if __name__ == "__main__":
    convert_csv_to_sql()
