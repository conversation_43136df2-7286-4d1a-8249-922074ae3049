# 办公费用特征工程开发日志

## 2025-07-28 - 特征工程优化升级

### 作者
AI Assistant (Augment Agent)

### 变更内容
对 `办公费用特征工程_jupyter.py` 进行了全面的特征工程优化，主要包括以下几个方面：

## 2025-07-28 - 核心特征工程重新设计

### 作者
AI Assistant (Augment Agent)

### 变更内容
根据用户具体需求，重新设计了特征工程脚本，创建了 `核心特征工程.py`，专注于以下核心特征：

#### 1. 上个月特征组
- `上月总金额`: 上个月的总金额
- `上月_办公用品`, `上月_印刷费`, `上月_邮寄费`, `上月_其他`: 上个月各类别金额
- `上月预算偏差`: 上个月累计预算偏差
- `上月复合增长率`: 上个月复合月增长率
- `较上月增长率`: 较上个月的增长率

#### 2. 上2个月特征组
- `上2月总金额`: 上2个月的总金额
- `上2月_办公用品`, `上2月_印刷费`, `上2月_邮寄费`, `上2月_其他`: 上2个月各类别金额
- `上2月预算偏差`: 上2个月累计预算偏差
- `上2月复合增长率`: 上2个月复合月增长率

#### 3. 上3个月特征组
- `上3月总金额`: 上3个月的总金额
- `上3月_办公用品`, `上3月_印刷费`, `上3月_邮寄费`, `上3月_其他`: 上3个月各类别金额
- `上3月预算偏差`: 上3个月累计预算偏差
- `上3月复合增长率`: 上3个月复合月增长率

#### 4. 去年同期特征组
- `去年同期总金额`: 去年同期的总金额
- `去年同期_办公用品`, `去年同期_印刷费`, `去年同期_邮寄费`, `去年同期_其他`: 去年同期各类别金额
- `较去年同期增长率`: 较去年同期的增长率

#### 5. 斜率特征
- `前3月斜率`: 前3个月总金额的线性趋势斜率
- `前6月斜率`: 前6个月总金额的线性趋势斜率

#### 6. 时间特征
- `是否年初`: 是否为年初（1-3月）
- `是否年中`: 是否为年中（4-9月）
- `是否年末`: 是否为年末（10-12月）
- `是否上半年`: 是否为上半年（1-6月）
- `是否第一季度`, `是否第二季度`, `是否第三季度`, `是否第四季度`: 季度标识

#### 7. 平均值特征
- `前3月平均值`: 前3个月总金额平均值
- `去年同期3月平均值`: 去年同期三个月平均值

### 技术实现特点
1. **模块化设计**: 将特征计算封装为独立函数，便于维护和复用
2. **精确的特征定义**: 严格按照用户需求设计每个特征
3. **完整的类别支持**: 自动识别和处理所有费用类别
4. **数据完整性**: 处理缺失值和异常情况
5. **清晰的输出**: 提供详细的特征分类统计

### 核心算法
- **复合增长率计算**: `(last_val / first_val) ** (1/periods) - 1`
- **斜率计算**: 使用 `numpy.polyfit` 进行线性回归
- **预算偏差**: `(actual - budget) / budget`

### 影响范围
- **新增文件**: `核心特征工程.py`
- **输出文件**: `核心特征数据.csv`
- **特征数量**: 根据类别数量动态生成，预计60-80个特征

### 使用方法
```python
from 核心特征工程 import create_features
result_df = create_features('办公费用.csv', '核心特征数据.csv')
```

### 验证要点
1. 确认各月份的滞后特征计算正确
2. 验证复合增长率计算的准确性
3. 检查预算偏差计算逻辑
4. 确认时间特征的正确性
5. 验证去年同期特征的对应关系

## 2025-07-28 - MySQL数据库脚本生成

### 作者
AI Assistant (Augment Agent)

### 变更内容
基于 `新特征工程数据1.csv` 文件生成了完整的MySQL数据库脚本，实现特征工程数据的数据库化存储和管理。

#### 1. 生成的文件
- **主脚本**: `办公费用特征工程数据.sql`
- **使用说明**: `SQL脚本使用说明.md`
- **转换工具**: `csv_to_sql.py`

#### 2. 数据库表设计
**表名**: `office_expense_features`

**字段设计特点**:
- 使用自增主键确保唯一性
- 年月字段设置唯一索引支持快速查询
- 金额字段使用DECIMAL(15,2)确保精度
- 增长率字段使用DECIMAL(10,6)保留足够小数位
- 时间特征使用TINYINT(1)节省存储空间

**字段分类**:
- 基础标识字段: 6个
- 基础金额特征: 4个
- 类别金额特征: 6个
- 预算相关特征: 2个
- 滞后特征组: 27个（上月、上2月、上3月各9个）
- 去年同期特征: 8个
- 趋势特征: 2个
- 时间特征: 8个
- 平均值特征: 2个
- **总计**: 65个字段

#### 3. 数据处理特点
- **数据类型映射**: CSV字符串数据转换为适当的MySQL数据类型
- **缺失值处理**: 空值统一转换为0或默认值
- **日期格式转换**: 将CSV中的日期格式转换为MySQL标准DATE格式
- **数值精度控制**: 保留适当的小数位数，避免精度丢失

#### 4. 性能优化
**索引设计**:
- 主键索引: `id`
- 唯一索引: `year_month`
- 复合索引: `year, month, date`
- 单列索引: `date`, `monthly_total_amount`
- 时间特征组合索引: 支持时间维度查询

#### 5. 脚本功能
- **表结构创建**: 完整的CREATE TABLE语句
- **数据插入**: 批量INSERT语句
- **索引创建**: 性能优化索引
- **数据验证**: 自动验证查询
- **使用示例**: 常用查询模板

#### 6. 技术特点
- **兼容性**: 支持MySQL 5.7+和8.0+
- **字符编码**: 使用utf8mb4支持完整Unicode
- **存储引擎**: InnoDB支持事务和外键
- **注释完整**: 每个字段都有详细的中文注释

#### 7. 数据验证查询
脚本包含以下验证查询:
- 总记录数统计
- 数据时间范围检查
- 按年份统计汇总
- 按季度统计汇总
- 表结构展示
- 样例数据预览

### 使用方法
```sql
-- 在MySQL中执行
mysql -u username -p database_name < 办公费用特征工程数据.sql

-- 或在MySQL客户端中
source /path/to/办公费用特征工程数据.sql;
```

### 影响范围
- **新增文件**: `办公费用特征工程数据.sql`, `SQL脚本使用说明.md`
- **数据库表**: `office_expense_features`
- **数据记录**: 37条（2022年1月-2024年12月）
- **字段数量**: 65个特征字段

### 后续应用
1. **机器学习**: 可直接用于模型训练的特征数据
2. **数据分析**: 支持复杂的SQL分析查询
3. **报表生成**: 支持BI工具连接和报表制作
4. **API开发**: 为应用程序提供数据接口
5. **数据挖掘**: 支持时间序列分析和趋势预测

#### 1. 时间窗口调整
- **问题**: 原有代码使用固定的滞后特征，不适合特定预测场景
- **解决方案**: 
  - 新增配置参数 `PREDICTION_CONFIG`，支持动态配置预测目标月份和历史窗口
  - 对于预测6月数据的场景，只使用3月、4月、5月的历史数据作为特征
  - 修改"12个月前"特征为"去年同期"特征，更准确地反映同比关系

#### 2. 新增累计费用特征
- **新增特征**:
  - `目标月前累计费用`: 计算6月之前的累计费用总额
  - `参考期累计变化斜率`: 计算3-5月累计金额的线性趋势斜率
- **技术实现**: 使用 `numpy.polyfit` 计算线性回归斜率

#### 3. 动态窗口特征系统
- **新增函数**: `calculate_dynamic_window_features()`
- **新增特征**:
  - `参考期平均金额`: 3-5月平均金额
  - `参考期最大金额`: 3-5月最大金额
  - `参考期最小金额`: 3-5月最小金额
  - `参考期总金额`: 3-5月总金额
  - `参考期标准差`: 3-5月标准差
  - `参考期变化斜率`: 3-5月变化趋势

#### 4. 扩展特征工程
##### 4.1 季节性特征
- `季节性指数`: 基于历史数据计算每月的季节性模式

##### 4.2 波动性指标
- `3月滚动标准差`: 3个月滚动标准差
- `6月滚动标准差`: 6个月滚动标准差
- `变异系数`: 标准差与均值的比值
- `3月极差`: 3个月内最大值与最小值的差

##### 4.3 增长率特征
- `同比增长率`: 与去年同月的增长率
- `季度环比增长率`: 与前3个月的增长率
- `环比增长率`: 与上月的增长率

##### 4.4 类别占比特征
- 各类别在总费用中的占比
- `类别集中度`: 基于赫芬达尔指数的类别集中度

##### 4.5 异常值检测特征
- `Z_score`: 基于Z-score的异常值评分
- `是否异常值`: Z-score > 2 的异常值标识
- `IQR异常值`: 基于四分位距的异常值检测

### 技术改进
1. **模块化设计**: 将特征计算拆分为独立函数，提高代码可维护性
2. **参数化配置**: 通过 `PREDICTION_CONFIG` 支持不同预测场景
3. **错误处理**: 增强了异常情况的处理能力
4. **性能优化**: 优化了特征计算的效率

### 影响范围
- **文件**: `数据/深度分析/办公费用特征工程_jupyter.py`
- **新增依赖**: `scipy.stats` (用于异常值检测)
- **输出变化**: 特征数量从约50个增加到80+个

### 相关文件
- 主要修改文件: `办公费用特征工程_jupyter.py`
- 输出文件: `办公费用特征工程数据.csv`

### 测试建议
1. 验证预测6月时确实只使用3-5月数据
2. 检查累计特征的计算正确性
3. 确认新特征没有引入数据泄露
4. 测试不同预测月份的配置

### 后续优化建议
1. 可以考虑添加更多时间序列分解特征（趋势、季节性、残差）
2. 可以引入外部经济指标作为特征
3. 可以考虑使用机器学习方法自动生成特征组合

