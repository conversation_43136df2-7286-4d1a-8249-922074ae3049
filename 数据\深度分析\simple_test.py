import pandas as pd
import numpy as np

print("开始测试...")

# 配置参数
PREDICTION_CONFIG = {
    'target_month': 6,
    'reference_months': [3, 4, 5]
}

print("配置参数:", PREDICTION_CONFIG)

# 读取数据
df = pd.read_csv('办公费用.csv', encoding='utf-8')
print(f"数据形状: {df.shape}")

# 预处理
df['金额'] = df['金额'].astype(str).str.replace(',', '').astype(float)
df['年月'] = df['年'].astype(str) + '-' + df['月'].astype(str).str.zfill(2)

# 按月汇总
monthly = df.groupby(['年', '月'])['金额'].sum().reset_index()
print("月度汇总完成")

# 测试6月数据
june_data = monthly[monthly['月'] == 6]
print(f"6月数据: {len(june_data)} 行")

for _, row in june_data.iterrows():
    year = row['年']
    amount = row['金额']
    print(f"{year}年6月: {amount:,.2f}")
    
    # 获取3-5月数据
    ref_data = monthly[
        (monthly['年'] == year) & 
        (monthly['月'].isin([3, 4, 5]))
    ]
    
    if len(ref_data) > 0:
        ref_sum = ref_data['金额'].sum()
        ref_mean = ref_data['金额'].mean()
        print(f"  3-5月总额: {ref_sum:,.2f}")
        print(f"  3-5月平均: {ref_mean:,.2f}")
    
    # 6月前累计
    before_june = monthly[
        (monthly['年'] == year) & 
        (monthly['月'] < 6)
    ]
    cumulative = before_june['金额'].sum()
    print(f"  6月前累计: {cumulative:,.2f}")

print("测试完成!")
