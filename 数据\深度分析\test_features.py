#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试优化后的特征工程脚本
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 配置参数
PREDICTION_CONFIG = {
    'target_month': 6,  # 预测目标月份
    'history_window': 3,  # 历史数据窗口长度（月）
    'reference_months': [3, 4, 5]  # 参考月份（3月、4月、5月）
}

print("🔧 开始测试办公费用特征工程...")

# 1. 读取基础数据
print("📂 读取办公费用基础数据...")
try:
    df = pd.read_csv('办公费用.csv', encoding='utf-8')
    print(f"✅ 成功读取数据，形状: {df.shape}")
    print("数据列名:", df.columns.tolist())
    print("\n前5行数据:")
    print(df.head())
except Exception as e:
    print(f"❌ 读取数据失败: {e}")
    exit()

# 2. 数据预处理
print("\n🧹 数据清洗...")
# 处理金额字段
df['金额'] = df['金额'].astype(str).str.replace(',', '').astype(float)
# 生成标准化日期字段
df['年月'] = df['年'].astype(str) + '-' + df['月'].astype(str).str.zfill(2)
df['日期'] = pd.to_datetime(df['年月'] + '-01')

print(f"清洗后数据形状: {df.shape}")

# 3. 按月汇总数据
print("\n📊 按月汇总数据...")
monthly_summary = df.groupby('年月').agg({
    '金额': ['sum', 'mean', 'count', 'std']
}).round(2)

monthly_summary.columns = ['月度总金额', '月度平均金额', '月度交易次数', '月度金额标准差']
monthly_summary = monthly_summary.reset_index()
monthly_summary = monthly_summary.fillna(0)

print("月度汇总数据:")
print(monthly_summary.head(10))

# 4. 测试动态窗口特征计算
print("\n🎯 测试动态窗口特征...")

def calculate_trend_slope(values):
    """计算数值序列的线性趋势斜率"""
    if len(values) < 2 or values.isna().all():
        return 0
    valid_values = values.dropna()
    if len(valid_values) < 2:
        return 0
    x = np.arange(len(valid_values))
    try:
        slope, _ = np.polyfit(x, valid_values, 1)
        return slope
    except:
        return 0

# 创建基础时间框架
start_date = monthly_summary['年月'].min()
end_date = '2024-12'  # 扩展到2024年12月
date_range = pd.date_range(start=pd.to_datetime(start_date + '-01'), 
                          end=pd.to_datetime(end_date + '-01'), 
                          freq='MS')

base_df = pd.DataFrame({
    '年月': date_range.strftime('%Y-%m'),
    '年': date_range.year,
    '月': date_range.month,
    '日期': date_range
})

# 合并月度数据
feature_df = base_df.merge(monthly_summary, on='年月', how='left')
feature_df = feature_df.fillna(0)

print(f"特征数据框形状: {feature_df.shape}")

# 5. 测试6月预测特征
print("\n🎯 测试6月预测特征计算...")
target_month = PREDICTION_CONFIG['target_month']
reference_months = PREDICTION_CONFIG['reference_months']

june_rows = feature_df[feature_df['月'] == target_month]
print(f"找到 {len(june_rows)} 个6月数据行")

for idx, row in june_rows.iterrows():
    current_year = row['年']
    print(f"\n处理 {current_year}年6月:")
    
    # 获取当年参考月份的数据
    ref_data = feature_df[(feature_df['年'] == current_year) & 
                         (feature_df['月'].isin(reference_months))]
    
    if len(ref_data) > 0:
        print(f"  参考期数据 ({len(ref_data)}个月):")
        for _, ref_row in ref_data.iterrows():
            print(f"    {ref_row['年月']}: {ref_row['月度总金额']:.2f}")
        
        # 计算参考期特征
        ref_mean = ref_data['月度总金额'].mean()
        ref_max = ref_data['月度总金额'].max()
        ref_min = ref_data['月度总金额'].min()
        ref_sum = ref_data['月度总金额'].sum()
        ref_std = ref_data['月度总金额'].std() if len(ref_data) > 1 else 0
        ref_slope = calculate_trend_slope(ref_data['月度总金额'])
        
        print(f"  参考期平均金额: {ref_mean:.2f}")
        print(f"  参考期总金额: {ref_sum:.2f}")
        print(f"  参考期变化斜率: {ref_slope:.2f}")
        
        # 计算累计费用
        before_target = feature_df[(feature_df['年'] == current_year) & 
                                  (feature_df['月'] < target_month)]
        cumulative_amount = before_target['月度总金额'].sum()
        print(f"  6月前累计费用: {cumulative_amount:.2f}")
        
        # 计算累计变化斜率
        if len(ref_data) >= 2:
            ref_data_sorted = ref_data.sort_values('月')
            cumulative_series = ref_data_sorted['月度总金额'].cumsum()
            cumulative_slope = calculate_trend_slope(cumulative_series)
            print(f"  参考期累计变化斜率: {cumulative_slope:.2f}")

print("\n✅ 特征工程测试完成！")
print("\n主要改进验证:")
print("1. ✅ 动态时间窗口：6月预测使用3-5月数据")
print("2. ✅ 累计费用特征：计算6月前累计和3-5月趋势")
print("3. ✅ 参考期特征：基于指定月份计算统计特征")
print("4. ✅ 趋势斜率计算：使用线性回归计算变化趋势")
