#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
验证优化后的特征工程功能
"""

import pandas as pd
import numpy as np

print("🔧 验证特征工程优化...")

# 读取数据
try:
    df = pd.read_csv('办公费用.csv', encoding='utf-8')
    print(f"✅ 数据读取成功: {df.shape}")
except Exception as e:
    print(f"❌ 数据读取失败: {e}")
    exit()

# 数据预处理
df['金额'] = df['金额'].astype(str).str.replace(',', '').astype(float)
df['年月'] = df['年'].astype(str) + '-' + df['月'].astype(str).str.zfill(2)

# 按月汇总
monthly_data = df.groupby(['年', '月'])['金额'].sum().reset_index()
monthly_data['年月'] = monthly_data['年'].astype(str) + '-' + monthly_data['月'].astype(str).str.zfill(2)

print("\n📊 月度数据汇总:")
print(monthly_data.head(10))

# 验证6月预测特征
print("\n🎯 验证6月预测特征:")
target_month = 6
reference_months = [3, 4, 5]

june_data = monthly_data[monthly_data['月'] == target_month]
print(f"找到 {len(june_data)} 个6月数据")

for _, row in june_data.iterrows():
    year = row['年']
    print(f"\n{year}年6月预测特征:")
    
    # 获取3-5月数据
    ref_data = monthly_data[
        (monthly_data['年'] == year) & 
        (monthly_data['月'].isin(reference_months))
    ]
    
    if len(ref_data) > 0:
        print(f"  参考期数据 (3-5月):")
        for _, ref_row in ref_data.iterrows():
            print(f"    {ref_row['年月']}: {ref_row['金额']:,.2f}")
        
        # 计算特征
        ref_mean = ref_data['金额'].mean()
        ref_sum = ref_data['金额'].sum()
        ref_max = ref_data['金额'].max()
        ref_min = ref_data['金额'].min()
        
        print(f"  ✅ 参考期平均金额: {ref_mean:,.2f}")
        print(f"  ✅ 参考期总金额: {ref_sum:,.2f}")
        print(f"  ✅ 参考期最大金额: {ref_max:,.2f}")
        print(f"  ✅ 参考期最小金额: {ref_min:,.2f}")
        
        # 计算6月前累计费用
        before_june = monthly_data[
            (monthly_data['年'] == year) & 
            (monthly_data['月'] < target_month)
        ]
        cumulative = before_june['金额'].sum()
        print(f"  ✅ 6月前累计费用: {cumulative:,.2f}")
        
        # 计算趋势斜率
        if len(ref_data) >= 2:
            ref_sorted = ref_data.sort_values('月')
            x = np.arange(len(ref_sorted))
            y = ref_sorted['金额'].values
            slope = np.polyfit(x, y, 1)[0]
            print(f"  ✅ 参考期变化斜率: {slope:,.2f}")
    else:
        print(f"  ⚠️ {year}年缺少3-5月数据")

# 验证去年同期特征
print("\n📅 验证去年同期特征:")
for _, row in june_data.iterrows():
    year = row['年']
    last_year_june = monthly_data[
        (monthly_data['年'] == year - 1) & 
        (monthly_data['月'] == target_month)
    ]
    
    if len(last_year_june) > 0:
        last_year_amount = last_year_june.iloc[0]['金额']
        current_amount = row['金额']
        growth_rate = (current_amount - last_year_amount) / last_year_amount * 100
        print(f"  {year}年6月: {current_amount:,.2f}")
        print(f"  {year-1}年6月: {last_year_amount:,.2f}")
        print(f"  ✅ 同比增长率: {growth_rate:.2f}%")
    else:
        print(f"  ⚠️ {year-1}年6月数据缺失")

print("\n✅ 特征工程验证完成！")
print("\n🎯 主要优化点验证:")
print("1. ✅ 时间窗口调整: 6月预测使用3-5月历史数据")
print("2. ✅ 累计费用特征: 计算6月前累计总额")
print("3. ✅ 参考期特征: 基于3-5月计算统计特征")
print("4. ✅ 去年同期特征: 使用去年6月数据作为参考")
print("5. ✅ 趋势斜率: 计算3-5月线性变化趋势")
