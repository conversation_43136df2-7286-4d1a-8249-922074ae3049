import pandas as pd
from prophet import Prophet
import matplotlib.pyplot as plt

# 1. 读取和清洗数据
df = pd.read_csv('数据/深度分析/办公费用.csv')
df['金额'] = df['金额'].astype(str).str.replace(',', '').astype(float)
df['年月'] = df['年'].astype(str) + '-' + df['月'].astype(str).str.zfill(2)
df['年月'] = pd.to_datetime(df['年月'], format='%Y-%m')

# 2. 按部门和年月统计每月费用
df_group = df.groupby(['部门', '年月'])['金额'].sum().reset_index()

# 3. 对每个部门分别建模和预测
departments = df_group['部门'].unique()
future_months = 6  # 预测未来6个月

for dept in departments:
    df_dept = df_group[df_group['部门'] == dept][['年月', '金额']].rename(columns={'年月': 'ds', '金额': 'y'})
    if len(df_dept) < 12:  # 数据太少不建模
        continue

    # 建立模型
    model = Prophet(yearly_seasonality=True, monthly_seasonality=True)
    model.fit(df_dept)

    # 生成未来日期
    future = model.make_future_dataframe(periods=future_months, freq='MS')
    forecast = model.predict(future)

    # 画图
    plt.figure(figsize=(10, 5))
    plt.plot(df_dept['ds'], df_dept['y'], label='历史数据')
    plt.plot(forecast['ds'], forecast['yhat'], label='预测')
    plt.title(f'{dept} 办公费用预测')
    plt.xlabel('时间')
    plt.ylabel('费用')
    plt.legend()
    plt.show()

    # 打印未来预测
    print(f"\n{dept} 未来{future_months}个月预测：")
    print(forecast[['ds', 'yhat']].tail(future_months))