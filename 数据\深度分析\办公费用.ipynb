{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 办公费用分析\n", "本Notebook将带你一步步完成办公费用的机器学习分析，包括：\n", "1. 预测未来某个月的办公费用\n", "2. 统计每月固定和不固定费用\n", "3. 统计每年都会有的办公费用\n", "4. 结论输出与可视化\n", "\n", "> 请确保办公费用.csv文件与本notebook在同一目录下。"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# 1. 导入常用库\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import mean_absolute_error\n", "\n", "# 显示中文和负号\n", "plt.rcParams['font.sans-serif'] = ['SimHei']\n", "plt.rcParams['axes.unicode_minus'] = False"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>年</th>\n", "      <th>月</th>\n", "      <th>日</th>\n", "      <th>部门</th>\n", "      <th>摘要</th>\n", "      <th>Unnamed: 5</th>\n", "      <th>金额</th>\n", "      <th>类别</th>\n", "      <th>费用类型</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2022</td>\n", "      <td>1</td>\n", "      <td>11</td>\n", "      <td>本部</td>\n", "      <td>报销2021年12月份顺丰快递费</td>\n", "      <td>报销2021年12月份顺丰快递费</td>\n", "      <td>888.00</td>\n", "      <td>邮寄费</td>\n", "      <td>管理费用</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2022</td>\n", "      <td>1</td>\n", "      <td>13</td>\n", "      <td>阳春分公司</td>\n", "      <td>报销阳春分公司刻章费用</td>\n", "      <td>报销阳春分公司刻章费用</td>\n", "      <td>660.00</td>\n", "      <td>办公用品</td>\n", "      <td>管理费用</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2022</td>\n", "      <td>1</td>\n", "      <td>13</td>\n", "      <td>阳春分公司</td>\n", "      <td>报销阳春分公司采购茶叶一批费用</td>\n", "      <td>报销阳春分公司采购茶叶一批费用</td>\n", "      <td>1,900.00</td>\n", "      <td>办公用品</td>\n", "      <td>管理费用</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2022</td>\n", "      <td>1</td>\n", "      <td>17</td>\n", "      <td>本部</td>\n", "      <td>报销12月份宣传用品印刷等采购费用</td>\n", "      <td>报销12月份宣传用品印刷等采购费用</td>\n", "      <td>8,603.00</td>\n", "      <td>印刷费</td>\n", "      <td>管理费用</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2022</td>\n", "      <td>1</td>\n", "      <td>17</td>\n", "      <td>本部</td>\n", "      <td>报销12月份茶叶采购费用</td>\n", "      <td>报销12月份茶叶采购费用</td>\n", "      <td>5,200.00</td>\n", "      <td>办公用品</td>\n", "      <td>管理费用</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      年  月   日     部门                 摘要         Unnamed: 5        金额    类别  \\\n", "0  2022  1  11     本部   报销2021年12月份顺丰快递费   报销2021年12月份顺丰快递费    888.00   邮寄费   \n", "1  2022  1  13  阳春分公司        报销阳春分公司刻章费用        报销阳春分公司刻章费用    660.00  办公用品   \n", "2  2022  1  13  阳春分公司    报销阳春分公司采购茶叶一批费用    报销阳春分公司采购茶叶一批费用  1,900.00  办公用品   \n", "3  2022  1  17     本部  报销12月份宣传用品印刷等采购费用  报销12月份宣传用品印刷等采购费用  8,603.00   印刷费   \n", "4  2022  1  17     本部       报销12月份茶叶采购费用       报销12月份茶叶采购费用  5,200.00  办公用品   \n", "\n", "   费用类型  \n", "0  管理费用  \n", "1  管理费用  \n", "2  管理费用  \n", "3  管理费用  \n", "4  管理费用  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# 2. 读取数据\n", "df = pd.read_csv('办公费用.csv', encoding='utf-8')\n", "df.head()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>年</th>\n", "      <th>月</th>\n", "      <th>日</th>\n", "      <th>部门</th>\n", "      <th>摘要</th>\n", "      <th>Unnamed: 5</th>\n", "      <th>金额</th>\n", "      <th>类别</th>\n", "      <th>费用类型</th>\n", "      <th>年月</th>\n", "      <th>日期</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2022</td>\n", "      <td>1</td>\n", "      <td>11</td>\n", "      <td>本部</td>\n", "      <td>报销2021年12月份顺丰快递费</td>\n", "      <td>报销2021年12月份顺丰快递费</td>\n", "      <td>888.0</td>\n", "      <td>邮寄费</td>\n", "      <td>管理费用</td>\n", "      <td>2022-01</td>\n", "      <td>2022-01-11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2022</td>\n", "      <td>1</td>\n", "      <td>13</td>\n", "      <td>阳春分公司</td>\n", "      <td>报销阳春分公司刻章费用</td>\n", "      <td>报销阳春分公司刻章费用</td>\n", "      <td>660.0</td>\n", "      <td>办公用品</td>\n", "      <td>管理费用</td>\n", "      <td>2022-01</td>\n", "      <td>2022-01-13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2022</td>\n", "      <td>1</td>\n", "      <td>13</td>\n", "      <td>阳春分公司</td>\n", "      <td>报销阳春分公司采购茶叶一批费用</td>\n", "      <td>报销阳春分公司采购茶叶一批费用</td>\n", "      <td>1900.0</td>\n", "      <td>办公用品</td>\n", "      <td>管理费用</td>\n", "      <td>2022-01</td>\n", "      <td>2022-01-13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2022</td>\n", "      <td>1</td>\n", "      <td>17</td>\n", "      <td>本部</td>\n", "      <td>报销12月份宣传用品印刷等采购费用</td>\n", "      <td>报销12月份宣传用品印刷等采购费用</td>\n", "      <td>8603.0</td>\n", "      <td>印刷费</td>\n", "      <td>管理费用</td>\n", "      <td>2022-01</td>\n", "      <td>2022-01-17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2022</td>\n", "      <td>1</td>\n", "      <td>17</td>\n", "      <td>本部</td>\n", "      <td>报销12月份茶叶采购费用</td>\n", "      <td>报销12月份茶叶采购费用</td>\n", "      <td>5200.0</td>\n", "      <td>办公用品</td>\n", "      <td>管理费用</td>\n", "      <td>2022-01</td>\n", "      <td>2022-01-17</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      年  月   日     部门                 摘要         Unnamed: 5      金额    类别  \\\n", "0  2022  1  11     本部   报销2021年12月份顺丰快递费   报销2021年12月份顺丰快递费   888.0   邮寄费   \n", "1  2022  1  13  阳春分公司        报销阳春分公司刻章费用        报销阳春分公司刻章费用   660.0  办公用品   \n", "2  2022  1  13  阳春分公司    报销阳春分公司采购茶叶一批费用    报销阳春分公司采购茶叶一批费用  1900.0  办公用品   \n", "3  2022  1  17     本部  报销12月份宣传用品印刷等采购费用  报销12月份宣传用品印刷等采购费用  8603.0   印刷费   \n", "4  2022  1  17     本部       报销12月份茶叶采购费用       报销12月份茶叶采购费用  5200.0  办公用品   \n", "\n", "   费用类型       年月         日期  \n", "0  管理费用  2022-01 2022-01-11  \n", "1  管理费用  2022-01 2022-01-13  \n", "2  管理费用  2022-01 2022-01-13  \n", "3  管理费用  2022-01 2022-01-17  \n", "4  管理费用  2022-01 2022-01-17  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# 3. 数据清洗\n", "# 处理金额字段，去掉千分位逗号\n", "df['金额'] = df['金额'].astype(str).str.replace(',', '').astype(float)\n", "# 删除全空列\n", "df = df.dropna(axis=1, how='all')\n", "# 处理类别中的不一致（如‘邮寄’与‘邮寄费’）\n", "df['类别'] = df['类别'].replace('邮寄', '邮寄费')\n", "# 生成年月和日期字段\n", "df['年月'] = df['年'].astype(str) + '-' + df['月'].astype(str).str.zfill(2)\n", "df['日期'] = pd.to_datetime(df['年'].astype(str) + '-' + df['月'].astype(str).str.zfill(2) + '-' + df['日'].astype(str).str.zfill(2), errors='coerce')\n", "df.head()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>金额</th>\n", "      <th>部门_本部</th>\n", "      <th>部门_阳东分公司</th>\n", "      <th>部门_阳春分公司</th>\n", "      <th>部门_阳西分公司</th>\n", "      <th>类别_/</th>\n", "      <th>类别_其他</th>\n", "      <th>类别_办公用品</th>\n", "      <th>类别_印刷费</th>\n", "      <th>类别_计算机耗材</th>\n", "      <th>...</th>\n", "      <th>年月_2024-03</th>\n", "      <th>年月_2024-04</th>\n", "      <th>年月_2024-05</th>\n", "      <th>年月_2024-06</th>\n", "      <th>年月_2024-07</th>\n", "      <th>年月_2024-08</th>\n", "      <th>年月_2024-09</th>\n", "      <th>年月_2024-10</th>\n", "      <th>年月_2024-11</th>\n", "      <th>年月_2024-12</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>888.0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>660.0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1900.0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>8603.0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5200.0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 49 columns</p>\n", "</div>"], "text/plain": ["       金额  部门_本部  部门_阳东分公司  部门_阳春分公司  部门_阳西分公司   类别_/  类别_其他  类别_办公用品  类别_印刷费  \\\n", "0   888.0   True     False     False     False  False  False    False   False   \n", "1   660.0  False     False      True     False  False  False     True   False   \n", "2  1900.0  False     False      True     False  False  False     True   False   \n", "3  8603.0   True     False     False     False  False  False    False    True   \n", "4  5200.0   True     False     False     False  False  False     True   False   \n", "\n", "   类别_计算机耗材  ...  年月_2024-03  年月_2024-04  年月_2024-05  年月_2024-06  年月_2024-07  \\\n", "0     False  ...       False       False       False       False       False   \n", "1     False  ...       False       False       False       False       False   \n", "2     False  ...       False       False       False       False       False   \n", "3     False  ...       False       False       False       False       False   \n", "4     False  ...       False       False       False       False       False   \n", "\n", "   年月_2024-08  年月_2024-09  年月_2024-10  年月_2024-11  年月_2024-12  \n", "0       False       False       False       False       False  \n", "1       False       False       False       False       False  \n", "2       False       False       False       False       False  \n", "3       False       False       False       False       False  \n", "4       False       False       False       False       False  \n", "\n", "[5 rows x 49 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# 4. 特征工程\n", "# 只用‘部门’‘类别’‘费用类型’‘年月’做特征\n", "features = ['部门', '类别', '费用类型', '年月']\n", "df_ml = df[features + ['金额']].copy()\n", "df_ml = pd.get_dummies(df_ml, columns=['部门', '类别', '费用类型', '年月'])\n", "df_ml.head()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'df_ml' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[2], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# 5. 机器学习建模预测未来某月办公费用\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m X \u001b[38;5;241m=\u001b[39m \u001b[43mdf_ml\u001b[49m\u001b[38;5;241m.\u001b[39mdrop(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m金额\u001b[39m\u001b[38;5;124m'\u001b[39m, axis\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m1\u001b[39m)\n\u001b[0;32m      3\u001b[0m y \u001b[38;5;241m=\u001b[39m df_ml[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m金额\u001b[39m\u001b[38;5;124m'\u001b[39m]\n\u001b[0;32m      4\u001b[0m X_train, X_test, y_train, y_test \u001b[38;5;241m=\u001b[39m train_test_split(X, y, test_size\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0.2\u001b[39m, random_state\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m42\u001b[39m)\n", "\u001b[1;31mNameError\u001b[0m: name 'df_ml' is not defined"]}], "source": ["# 5. 机器学习建模预测未来某月办公费用\n", "X = df_ml.drop('金额', axis=1)\n", "y = df_ml['金额']\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "model = RandomForestRegressor(random_state=42)\n", "model.fit(X_train, y_train)\n", "y_pred = model.predict(X_test)\n", "print('模型平均绝对误差：', mean_absolute_error(y_test, y_pred))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["预测2024年12月本部-办公用品-管理费用的办公费用：\n"]}, {"ename": "NameError", "evalue": "name 'model' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[8], line 17\u001b[0m\n\u001b[0;32m     15\u001b[0m \u001b[38;5;66;03m# 示例：预测2024年12月本部-办公用品-管理费用的办公费用\u001b[39;00m\n\u001b[0;32m     16\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m预测2024年12月本部-办公用品-管理费用的办公费用：\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m---> 17\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[43mpredict_future\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m2024\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m12\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43m本部\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43m办公用品\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43m管理费用\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m)\n", "Cell \u001b[1;32mIn[8], line 13\u001b[0m, in \u001b[0;36mpredict_future\u001b[1;34m(year, month, 部门, 类别, 费用类型)\u001b[0m\n\u001b[0;32m     11\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m col \u001b[38;5;241m==\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m年月_\u001b[39m\u001b[38;5;132;01m{\u001b[39;00myear\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m-\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mstr\u001b[39m(month)\u001b[38;5;241m.\u001b[39mzfill(\u001b[38;5;241m2\u001b[39m)\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m:\n\u001b[0;32m     12\u001b[0m         future[col] \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[1;32m---> 13\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mmodel\u001b[49m\u001b[38;5;241m.\u001b[39mpredict(future)[\u001b[38;5;241m0\u001b[39m]\n", "\u001b[1;31mNameError\u001b[0m: name 'model' is not defined"]}], "source": ["# 6. 预测未来某个月办公费用的函数\n", "def predict_future(year, month, 部门, 类别, 费用类型):\n", "    future = pd.DataFrame([0]*X.shape[1], index=X.columns).T\n", "    for col in future.columns:\n", "        if col == f'部门_{部门}':\n", "            future[col] = 1\n", "        if col == f'类别_{类别}':\n", "            future[col] = 1\n", "        if col == f'费用类型_{费用类型}':\n", "            future[col] = 1\n", "        if col == f'年月_{year}-{str(month).zfill(2)}':\n", "            future[col] = 1\n", "    return model.predict(future)[0]\n", "\n", "# 示例：预测2024年12月本部-办公用品-管理费用的办公费用\n", "print('预测2024年12月本部-办公用品-管理费用的办公费用：')\n", "print(predict_future(2024, 12, '本部', '办公用品', '管理费用'))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["每月固定出现的费用类别： ['办公用品', '印刷费', '邮寄费']\n", "不固定出现的费用类别： ['/', '其他', '计算机耗材']\n"]}], "source": ["# 7. 统计每月固定费用和不固定费用\n", "类别_每月统计 = df.groupby(['类别', '年月']).size().unstack(fill_value=0)\n", "固定费用类别 = 类别_每月统计[(类别_每月统计 > 0).all(axis=1)].index.tolist()\n", "print('每月固定出现的费用类别：', 固定费用类别)\n", "不固定费用类别 = 类别_每月统计[(类别_每月统计 > 0).any(axis=1) & ~(类别_每月统计 > 0).all(axis=1)].index.tolist()\n", "print('不固定出现的费用类别：', 不固定费用类别)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["每年都会有的办公费用类别： ['其他', '办公用品', '印刷费', '邮寄费']\n"]}], "source": ["# 8. 统计每年都会有的办公费用\n", "类别_每年统计 = df.groupby(['类别', '年']).size().unstack(fill_value=0)\n", "每年都有的类别 = 类别_每年统计[(类别_每年统计 > 0).all(axis=1)].index.tolist()\n", "print('每年都会有的办公费用类别：', 每年都有的类别)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 9. 可视化：每月办公费用总额趋势\n", "plt.figure(figsize=(12,6))\n", "df.groupby('年月')['金额'].sum().plot(marker='o')\n", "plt.title('每月办公费用总额趋势')\n", "plt.ylabel('金额')\n", "plt.xticks(rotation=45)\n", "plt.grid()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA3wAAAHTCAYAAAB8wRowAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAATo1JREFUeJzt3Xd4FNX+x/HPJptsSCChhhoCCNKiFEFQ4QICgoIgSBOQcgURBVTAgkqzUJQuCipVUBCRjpUqIFwBKUaalCCEIsFUkmx2s/P7g4f5uSRAgkJg8n49zzyPe+ac2e+se9d87sycYzMMwxAAAAAAwHJ8croAAAAAAMCNQeADAAAAAIsi8AEAAACARRH4AAAAAMCiCHwAAAAAYFEEPgAAAACwKAIfAAAAAFgUgQ8AAAAALIrABwC4pblcrpwuAQCA2xaBDwBwS3v++efVqlWrbI9LTU1VWlqa+Xrjxo1atGiR+TouLk5r1qzJdOzBgwe1efPmDNuePXsy9HW5XHrssce0c+fOq9bzySefqFWrVjp27Fi2z0WSzp07Z/7ziRMn1LVrV509ezbL4w3D0OzZs3X8+HFJ0vbt27V27dprjktOTlZcXFyWtvj4+GyfV3Jycpb6XbhwQcOGDdPmzZuz/R4AkJsR+AAAtyyXy6WvvvpKzZs3z/bYnj17ql27dmbo+/bbb/XRRx+Z+wcOHKh27dplGpreeecdNWjQQE2aNDG3hg0b6rnnnsvQ18/PT8uXL79m2ImNjdXKlSuVN2/ebJ/L+fPnVbZsWX3xxReSpIIFC2r16tWaM2fOVcelpqbK5XLJMAzZbDY9//zzZmhduXKlZs2adc33HjBggAoUKJClrWjRolc8zpdffqkWLVrI7XZ7tb/yyitq0qRJhvbLBQUFae7cufryyy/Ntp9++kklSpRQuXLlVL58eXMrU6aMhg0bds1zA4DcgMAHALhlffzxx/rzzz/13HPPyWazZdjCw8OVlJSU6di33npLv/zyix5//HGlpaXJ19dXfn5+kqQPP/xQn3/+uVauXJlpSPHz89OTTz6p1NRUc3vjjTfk7+8vSTp58qTq1aunI0eOSJJ8fX3l43P1/6T6+vrK399fRYoUyfbnMGvWLDkcDj3yyCOSLoaf3r17a+rUqV5XMS/Xrl07+fv7y8fHRzabTYmJiWrdurVsNpveeustff75516fZ82aNTP9LB5//HEZhmFujz/+uLp37+7VNnv2bDkcjivWcs899+jnn3/Wyy+/7NW+atUq1a1bV3a7PdNxBQsWNOv7448/NGXKFPP1vn37tGDBAi1cuFANGjRQgQIFtHDhQn322Wd68skns/LRAoDlEfgAALekmJgYvfnmm6pVq5bq1aun2NhYc1u3bp38/Pw0bdq0DFfMDh8+rIMHD8rX11eTJk1SSkqKTp48qfj4eKWkpCgqKkobNmzQ66+/rrCwMP3+++/at2+f1zFsNlumNV0KdTabTVu2bFF6evoV609JSVFKSopcLpd59crhcCgmJsZri4uLu+rn8Ndff2ns2LEaNGiQ8uXLZ7YPHjxY8fHxmjFjxhXHLlmyRC6XSx6PRx6PR4UKFdL69etlGIYGDRrkFdpeeeUVBQQEZDjGlYJYZnx9fa+4r1y5cpo1a5YOHDggwzAkSZs3b9a5c+c0cODAK45zOBz65ptvtGbNGh06dMist2rVqipYsKAaNGige++9V0lJSXr44YdVq1YtPfDAA6pQoUKW6wYAK8v6rzgAADeJYRjq2bOnIiIitGzZMlWsWFErVqxQt27d9Ndff6l3797q3r27ecXr79q1a6dff/1VQUFBZkCrVKmSXC6X7Ha7qlevLkn6/vvvNX78eKWlpcnPz8/rlkyPx6O4uDhFRkaabX/++acZVC4Fm6td1XvppZf0wQcfZGi//Apf48aNr/gsoSS99tpryps3r1588UWv9tDQUL3yyisaPny42rVrp9DQ0Axj58yZoz59+pj1pqenq0mTJuY5StL8+fPN/jVq1MhwjGtducxK3z59+ujjjz++Yr9ChQpJkgYNGqRx48Z57XviiSfkcDjUvn17de/eXePHj5d08d9fYGCgxowZI0nasmWL/P39NWbMGAUFBal///5ZrhsArIwrfACAW87w4cO1efNmzZw5U/ny5dPkyZPVr18//fjjj3rooYdUunTpTMOUJG3btk1ut1sJCQmKi4vTqlWrFBAQoOrVq6tx48basWOHKlWqpFWrVikuLk7JyckZnr9zOp1avny57rrrLnObNm2anE5nts7h2LFjOnXqlM6dO6d+/fqpUqVKOnfunLnVqVNHpUqVuuIx1q5dq48//lgTJkxQnjx5MuwfNGiQSpQooSeeeCLTWztjY2MVEREht9utmJgYSdLOnTvldrs1cOBAdevWTW6329y2b9+e4Rg2m01fffWV162fX331lebOnevV1rNnzyueh8PhyHAL6OVb48aNzVtm/27s2LEaNWqUzp8/r5kzZyp//vzKnz+/fvjhB+XNm1dDhgzR4cOH1bVrV5UsWVJ79+7V2LFjr1gLAOQ2BD4AwC2nX79++uqrr1SmTBlJUvv27dW6dWs1aNBAqampWrlyZabhQJICAgJks9nk8Xg0ZcoUNWvWTKNGjdIjjzwij8ejYsWKqWbNmmrQoIGeeeYZJSYmZjjGvHnz5HK5NHnyZLVo0UIul0sul0sbN27M8jn8+eefCg4OVvHixVW4cGElJiaqZMmSKly4sLklJSWpSJEi2rlzp3nF7ZITJ06oS5cuat++vdq2bStJ+uyzzzRt2jQz3AUEBOiLL77Q//73P3Xs2DFD6Pvjjz8UGRkpm82mAgUKSJKqV68um82m8ePHZwhtNpstwwQ5l57Zy8ozfFdy6dnJa7m8X2pqqp544glFR0drzpw5On78uIYNG6YKFSqoS5custvt8vX11QsvvKAmTZqoWbNmevrpp6/6LCEA5DYEPgDALSc0NFQPPvigpIvP5D399NNatGiRHnnkEf3+++/q3Lmzvvnmm0xndkxISND8+fNVrVo1jR8/XkuWLFG/fv3kdDrldDqVN29effjhh1qzZo2WL1+uiIgI/fjjj17H8PHxkd1uV0JCgo4dOya73W6Gi6zq1q2bGjVqZL4+cuSIypUr59Xn0jOJtWvX1sKFC832M2fOqFmzZgoICPCaWXTKlCmaPn26Vx2VKlXS559/rtWrV+u+++5TdHS0ue+DDz6QYRjaunWr8ubNq549e5oB7fJn+AzDUP/+/b2eE5SUIYhej7S0NCUmJurw4cNX3C497/h3TqdToaGhWrFihRYtWqQyZcpoyJAh+uijjzI8W/jRRx+ZofNKz2ACQG5E4AMA3HLOnz+v2bNn66GHHlKVKlWUkJCgX375RatXr1ZkZKQKFSqkNm3aqGDBgmrSpIkGDhyo7777TpK0YMECDRgwQB06dNC+ffvUrFkzSVKZMmUUGBho3pbZqFEj7dy5U5UrV1b+/PnN9544caIaN26s5s2b69NPP9WpU6fUvHlzc5s8efI161+5cqV++eUXczISj8ejPXv2qGrVql79YmNjVb16dbVo0UIjRowww9XmzZsVExOjr7/+2qxt69at+vnnnzV58uQMwbNVq1b68ssv1bRpU5UoUcJsT0lJ0ejRo9WoUSOFhYVleD7u7+bOnatVq1apePHiXu1ut1sul8trvT2Xy6W0tDSvtqutp5eWlqYlS5aoQoUKV9x++umnDFcoQ0JC9OGHH6pUqVIqV66c/Pz8FB4erocffljTp0/3CqMul0vFihW7Yg0AkFvZjEtPoAMAkMNiYmLUvHlz7dq1S2XKlFH9+vX13//+V6VLl87Q99LC6Xv27NG3336rVatWqU6dOoqMjJTL5TJvYbxkxowZmjlzprZu3erVfim8lC5dWvny5dPKlSu1a9cu+fv7a9GiRUpPT9cTTzwh6eKsl3Xr1tVrr72m4sWL6/fff1f58uVlt9u1Zs0aNWzYUJJUq1Ytpaamau/evfLx8dGWLVtUr1497dy501z6IC0tTQ6HQ/PmzVNYWJgaNmyo5cuXm4vMx8bGep1DixYt5OPjo5UrV5ptBw8eVMWKFTN8Nunp6erXr5+++OILJSUlZbhylpn69eurYsWKGjlypFdo7Nmz5zXX+7ukUKFC5rOClzidTvMWy7i4OOXNm1c7duxQTEyMWrZs6VVzQkKC1zknJydr5syZGj9+vO666y7NmDFDBQsW1Pvvv6+33npL69evV61atbR7924999xzatu2rapVq6ZevXrp8OHDWaoZACzPAADgFrJo0SJj586dhmEYho+PjyHpilvx4sUNwzCM9PR0wzAMw+VyGb6+vkZQUJARHBxshISEmNulY+XNm9erPTAw0LDb7ca3336boZYHHnjAGDJkiPm6W7duxsCBA42oqChDkrFv3z7DMAzD19fXWL9+vWEYhnHgwAEjICDAWLJkiTmuQ4cORnh4uOHxeMy2M2fOGJKMFStWGIZhGBUqVDCaNGmS6WeyefNmw9fX1/j111/NtkOHDhm+vr7G119/nemYadOmGR988IGxbt06Q5KRmJhoGIZhOJ1O45NPPjFOnDhh9i1UqJAxe/bsTI/TsmVL4+mnn/Zqe/zxx43u3bt7tc2ePdsoVKhQhvFvvvmm0aFDB8MwLv77/Pbbb42dO3cawcHBxtq1a81+Y8eONSpVqmScPn3abPvjjz+MMmXKGKVKlfL6dxYSEmJ+3pKMPXv2GIULFza2bNlirF+/3ihTpkym5wIAuRG3dAIAbint27c3r4L5+/tr5cqVmc7q+PeFvi9N82+32+V2u5WUlKT4+HjzdsPt27fL19dXFStWVOvWrb1uRbxw4YJcLpd56+cl0dHR2rFjh+rWrWu2paSkKDAwUCkpKZKU6aydFStW1KFDh9SmTRtJ0vbt27V48WK98sorXs+WxcbGSpJ5y+bQoUPVq1evDMfzeDx64YUX1L17d0VERJjtFSpUUK9evfTss89mejvlM888o2effVZBQUFe7WvWrFHv3r3122+/ebVn9jykJB04cECVK1fOdF9W/PDDD6pWrZqki7N1BgQEqGbNmho2bJhGjBhhLnXx4osvKiwsTI0bN9b58+clSWFhYTpy5IgeeOABjRgxwvx3Fh8fr5CQEDmdToWHh2vDhg0KDg7Wfffdp4CAAN1xxx3XXS8AWA2BDwBwy7rW7I5ZmUTlr7/+UseOHdW9e3dt3LhRW7Zs0dNPP33VZ84Mw9DgwYNVuHBhr7X+UlJSlCdPHlWqVEmGYZhr+l0uLCxMknTy5Ek9/vjjqlGjhp5++mmvPmfOnJEk8xbGJ598Uh07dsxwrLfeekuHDx/WO++8I8MwFBMToz179mjVqlUqV66coqKi9Oabb17xXC6FU0lKSkrS66+/rtq1a6tu3br673//q7Vr16pTp04qW7ZshrEnT57U4cOHVb9+/Sse/2rOnDmjn376Sa1bt5Z0McBfCucvvviivv76a9lsNv3yyy9atmyZFi1aJJvNpmeffdY8xpXW9rPb7XI4HNq7d6+mT5+ucePGafTo0Ro0aJAWL158XfUCgBUR+AAAt7Q///xTUVFRGbaYmBjz6lBmLly4oJkzZ6p69eoKDw/XlClTVLRoUW3atEm//PKL7r77bs2aNcsrEEkXlwLo3bu3Fi1apGnTpslutystLU3R0dE6ePCgQkJCvPqfOHFC6enpGcLnihUrVKNGDRmGoeXLl3stfh4TE6O5c+cqMDBQFSpUuOI5DB8+XCNGjJAk3XvvvXI4HCpSpIhq1Kihvn376ptvvlGjRo00ceJEHTp0KNNj1K9fX2fOnNHSpUtVo0YNRUVFacaMGTp16pT+/PNPNW3aVD/88IP27duXYYmKuXPnqnTp0uYV10s8Hk+G2TsTEhIyBPSZM2eqYsWK5mQ1/v7+OnTokDwej3x8fJQ3b155PB5NnTpVzz//vIKDg7Vs2TJNmDAhw/u9+OKL5tIR0sVnL48dO6YmTZqoTZs2atOmjTp16qTU1FT95z//8ZqtFABytRy6lRQAgGtyOBxXfYavZMmSXv1dLpcxevRoo3Xr1ka+fPmMwoULG+PHjzef8bskJSXFGD58uBEYGGgEBwcbrVu3Ns6ePWt4PB7jgQceMHx9fb2eaWvVqpUhyShUqJBx+PBhwzAMIy0tzahYsaJht9sNf39/89mzxMRE48EHHzQkGTVq1DD++OMP8zhxcXFGoUKFDEmGj4+PMW7cuKue/7x584wyZcoYXbp0McaOHWssWbLE+PXXX43k5GSvcwkLCzM6d+7sNfaPP/4w7rrrLqNo0aKGr6+v4XA4jG7duhnHjx/36rd//36ja9euho+Pj9GoUSOvWosUKWIMGzYsQ10tWrQwOnbsaH4O5cuXN3x9fY22bdt69fvrr7+M//3vf16fo81my/DvsVChQsaCBQuu+Dm0bNnSmDhxovlakjFmzBgjb968xnvvvefVNz4+3mjYsKGxefPmKx4PAHITZukEANyyjh8/rtDQUOXJkyfLY/r376/o6Gi1bdtWjz/++FXHnjlzRrNmzdKff/6pSZMmSZK2bNmiCxcu6KGHHjL7/fLLLzp69KiaNWvmtU7dtGnT5HQ61bJlS5UvX95s/+677/T9999r1KhRGRYBX7hwoVJTU9W0aVOVLFnyqudiGEaW1pTbsmWLKlasqMKFC3u1v/POO/L19dVdd92lBg0aKG/evFc8RmRkpAICAszz+PPPP/Xuu+/q5ZdfVmhoqFffvXv3ytfX17xy9/HHH6tgwYJq2bKlAgICrlnvv+XMmTMsxQAA10DgAwAAAACL4hk+AAAAALAoAh8AAAAAWBSBDwAAAAAsisAHAAAAABZlz+kCkHUej0enTp1Svnz5sjRrGwAAAABrMgxDiYmJKlGihHx8rnwdj8B3Gzl16pTCwsJyugwAAAAAt4gTJ06oVKlSV9xP4LuNXFr76cSJEwoODs7hagAAAADklISEBIWFhXmtD5sZAt9t5NJtnMHBwQQ+AAAAANd81ItJWwAAAADAogh8AAAAAGBRBD4AAAAAsCgCHwAAAABYFIEPAAAAACyKwAcAAAAAFkXgAwAAAACLIvABAAAAgEUR+AAAAADAogh8AAAAAGBRBD4AAAAAsCgCHwAAAABYFIEPAAAAACzKntMFIPsihn8nH0dgTpcBAAAA5BpRY1rkdAnXhSt8AAAAAGBRBD4AAAAAsCgCHwAAAABYFIEPAAAAACyKwAcAAAAAFkXgAwAAAACLIvABAAAAgEUR+AAAAADAogh8AAAAAGBRt33gS05O/kdjd+/e/e8VAwAAAAC3kNs+8L377ruqXLlylvtPmjRJH374oSTpp59+UpMmTeR0Oq/Y3263q3Tp0ipTpozKlCkjf39/FStWzHxdsGBBtW/fPsO43377TT4+PsqfP3+GzW63a926ddk/WQAAAADIhts+8Pn7+6t06dJZ6ut2uzVhwgSFh4dLkpo0aaISJUpo3rx5VxzjcDj0yy+/KCoqSlFRUbrzzjs1f/588/Vrr70mh8ORYVxwcLBKly6tuLi4DFu9evUUEBBwfScMAAAAAFl02wc+m82mvHnzZqnvpEmTVLJkSbVo0cJse/vtt/X2228rNTU10zE+Ptf+iOx2e5basntcAAAAAPgnrp5KblFFihRRcnKy/Pz85HQ65XQ6zYDl8Xj02GOPacmSJV5jjh8/rrfeektr1671am/VqpWmTp2qIUOGaOLEiRney9fXV/Xr15evr68k6fDhw+rdu7eCgoIkSefPn1ezZs0yjHO73Vc9B4/Hk/UTBgAAAIDrcFsGvlOnTsnPz0+S9MILL8jj8WjKlCmSpOeee84MZ5c4nU61b99e3bt3V61atXTu3DnFx8erfPnykqTZs2erevXqKlOmjJ5//nmvsYZhaNOmTSpcuLAkKSIiQpMmTVKTJk0kSePGjVNkZGSGGi9cuKCkpCRz3OXS09OveZ6XwuwlCQkJ1xwDAAAAAJfcdvcVut1u9e/fX8ePH5ckRUdHq2TJkub+2NhYpaamatCgQZKktLQ0denSRU6nU++++64kac6cOWratKlcLpckqWTJkvriiy80ZMgQvfjii15X57ISzDJTqVIlxcTEXHGrX7/+NY8xevRohYSEmFtYWNh11QIAAAAgd7rtAt/8+fP12WefKU+ePJIuzoZZsWJFc39sbKxKlSqlmTNnavny5Tp8+LB+//13rVy5UgEBAXK5XPrwww81duxY8yqhJD344INauXKl7rzzTq/n71JTU1WzZk1zVs5Dhw6pa9eu5utRo0Z51RcdHS2bzSa73W5u5cqVkyTz2Jc2Hx8fff3111c81yFDhig+Pt7cTpw48a98hgAAAAByh9sq8Lndbr399tt66aWXFBoaqtOnT+vgwYO67777zD5xcXEqXbq0+vbtq7Fjx6pKlSratWuXOZPnp59+quLFi6tDhw6SpGPHjmnjxo2SpMaNG6tv375ex5KkI0eOXHWWzr/z8/NT0aJF5Xa75Xa7dfjwYQUHB0u6OONnZGSkue+hhx666mydDodDwcHBXhsAAAAAZNVtFfj27dsnwzA0cOBASdL777+v+++/X0WLFjX7xMbGKiQkRL1799a2bdu0c+dOc0bMpKQkDR8+XOPHjzf7b9++XZ07d870+bhff/1VlStX9roSeC02my1D26VnCjObmZPZOgEAAADcKLdV2rj77rt18OBB5c2bV/v379f777+vN99806vPX3/9pZCQEJUrV07Lly9XRESEue/VV1/Vf/7zH/OKoGEYatCggcqVK6dXXnklw/stW7ZMTZs29WozDEOGYZiv09PTvUJeZoEvLS3tivsAAAAA4Ea5rQKfdHF9u507d+qRRx5Rjx491KhRI3PfmTNndO7cOfP2zUcffdRcFP21117TBx98oOPHj6tOnToKDw9XYGCg6tSpI39/f82aNUs7duwwj3X+/HnNnj1bXbt29Xp/t9ttBrhhw4bpgw8+8HqG0OPx6OzZs+ZzeuXLlzfX+EtPT1dERIS57/vvv7/uSWEAAAAA4Fpuq8AXFxen/v376/7771fr1q01efJkSdIvv/yi1q1b67777tP9999vLrfwdyVLltQTTzyhtm3batiwYfr+++8VHx+vqKgorV27Vr169TKPJ128NbR///6qWbOm13EmTZpkttWoUUPjx4/X4MGDzf0ul8vrGb6jR4/K399f0sVlFv7+DF+/fv2u+gwfAAAAAPwTNuPv9yfe4jwej4YPH66aNWuqTZs2Zrvb7dbrr7+uWrVq6dFHH72uEJWQkKA8efJk63m9my0hIeHi8gwvLJKPIzCnywEAAAByjagxLXK6BC+XskF8fPxVJ3e8rQJfbkfgAwAAAHLG7Rr4bqtbOgEAAAAAWUfgAwAAAACLIvABAAAAgEUR+AAAAADAogh8AAAAAGBRBD4AAAAAsCgCHwAAAABYlD2nC0D2RY5sdtW1NgAAAABA4gofAAAAAFgWgQ8AAAAALIrABwAAAAAWReADAAAAAIsi8AEAAACARRH4AAAAAMCiCHwAAAAAYFEEPgAAAACwKAIfAAAAAFgUgQ8AAAAALIrABwAAAAAWReADAAAAAIsi8AEAAACARRH4AAAAAMCiCHwAAAAAYFEEPgAAAACwKAIfAAAAAFgUgQ8AAAAALIrABwAAAAAWReADAAAAAIsi8AEAAACARRH4AAAAAMCiCHwAAAAAYFEEPgAAAACwKAIfAAAAAFgUgQ8AAAAALIrABwAAAAAWReADAAAAAIsi8AEAAACARRH4AAAAAMCiCHwAAAAAYFH2nC4A2Rcx/Dv5OAJzugwAuOGixrTI6RIAALitcYUPAAAAACyKwAcAAAAAFkXgAwAAAACLIvABAAAAgEUR+AAAAADAogh8AAAAAGBRBD4AAAAAsCgCHwAAAABYFIEPAAAAACyKwHcDGYaR0yUAAAAAyMUIfFk0ffp03XvvvRnaK1WqpB9++CHTMc2aNdP8+fPN1/Hx8bLZbEpPT79hdQIAAADAJfacLuBWN3HiRE2fPl1JSUmKi4tTRESE1/6jR4+qV69estlsmjp1qlq2bClJOnnypDZt2qQZM2YoLS1Ndrtd+fLlk6+vr3x9fXPiVAAAAADkMlzhu4YXX3xRBw8e1MiRI1W7dm1FRkZ6bSVKlNDMmTMVFRVlhj1Jmjp1qrp06aLSpUurTp06+v777+Xj4/1x79q162afDgAAAIBchMB3A8THx+vjjz9WrVq1JEl+fn5yOBySLj7XN2zYMN1xxx165JFHcrJMAAAAABbHLZ3ZsHPnTjPEXXL69OkM/d59913Fx8d7tc2cOVMvv/yyDMOQ2+3W2LFj9cILL1z1/ZxOp5xOp/k6ISHh+osHAAAAkOtwhS8b7rnnHu3YscNrK168uFefPXv2aPr06WrXrp02bdqkqlWraufOnQoODtb8+fPl4+OjUaNGqUKFCtd8lm/06NEKCQkxt7CwsBt5egAAAAAshsCXDdu3b1dERITXdurUKa8+RYoU0ZQpU1S8eHFFRERo3rx5ql27tjp06KCKFSua/dxut+z2q19gHTJkiOLj483txIkTN+S8AAAAAFgTt3RehcfjkWEY5pW42rVra8OGDV59ypQpY/Z1uVwqUaKEunTpou3bt6tAgQKqWbNmhuN+/vnnmj59eoYZPy/ncDjMZ/8AAAAAILsIfFexfft29e/fX35+frLZbJKkevXqefUpVaqURowYoeHDh8vpdGrNmjXKnz9/pse7tBB75cqV1b17d7Vr1+6G1g8AAAAgdyPwXUWdOnX0888/S5LOnDmj/v37a/LkySpRooQk6fvvv9eCBQs0ceLEK4a8v3O73apQoYKqVaumGjVq6Pjx41q4cKGeeuqpa97eCQAAAADZxTN8WfDjjz+qVq1aCg0NVcGCBc32qlWr6ty5c4qIiNC6deu8xng8HqWnp0u6uCxDVFSU/Pz8tH//fnM9vtWrV+vNN9/MsD4fAAAAAPwbuKx0DePHj9fo0aP1ySefqE2bNl77SpYsqVWrVun999/XI488os2bN5vLNjidTnMZhaeeekpDhgzJsAxDvnz5NH78eAIfAAAAgBvCZlx6sAyZOnXqlFwul8LDw6/a7+DBg16zcN4ICQkJF5dneGGRfByBN/S9AOBWEDWmRU6XAADALelSNoiPj1dwcPAV+3GF7xouPa93LTc67AEAAABAdnEvIQAAAABYFIEPAAAAACyKwAcAAAAAFkXgAwAAAACLIvABAAAAgEUR+AAAAADAogh8AAAAAGBRrMN3G4oc2eyqiysCAAAAgMQVPgAAAACwLAIfAAAAAFgUgQ8AAAAALIrABwAAAAAWReADAAAAAIsi8AEAAACARRH4AAAAAMCiCHwAAAAAYFEEPgAAAACwKAIfAAAAAFgUgQ8AAAAALIrABwAAAAAWReADAAAAAIsi8AEAAACARRH4AAAAAMCiCHwAAAAAYFEEPgAAAACwKAIfAAAAAFgUgQ8AAAAALIrABwAAAAAWReADAAAAAIsi8AEAAACARRH4AAAAAMCiCHwAAAAAYFEEPgAAAACwKAIfAAAAAFgUgQ8AAAAALIrABwAAAAAWReADAAAAAIsi8AEAAACARRH4AAAAAMCi7DldALIvYvh38nEE5nQZuEzUmBY5XQIAAADghSt8AAAAAGBRBD4AAAAAsCgCHwAAAABYFIEPAAAAACyKwAcAAAAAFkXgAwAAAACLIvABAAAAgEUR+AAAAADAogh8AAAAAGBRBL4bLD09PadLAAAAAJBL5brAd/78eS1dulRdunTRmTNnrto3Ojpavr6+KlOmjNdmt9sVFRVl9luyZIlatWplvk5PT5fb7ZYkLVu2TPXq1ZMkTZgwQa+++uq/f1IAAAAAkAl7Thdws3z88ccaNWqUfH19Vb58eVWpUkV//PGHvvnmG/Xp00eBgYGSpOTkZE2YMEH9+vVTQECAihQp4hXuJKlMmTJyOBzq1auXatSoofDwcOXPn9/c/+WXX2rYsGHy8/NTcnKyzp07p4iICMXExMjj8WjVqlXyeDzav3+/kpKSFBQUdBM/CQAAAAC5Ra4JfLGxsWrYsKHmzJnj1X748GF16NBB8+fPlyR17dpVAQEBkiSbzSaXy6Vt27Z5jXE6nTIMQw6HQwUKFJAk+fj8/8XSTp06qVOnTpKkDRs26O2339aaNWs0ZswYpaamasSIEYqLi1OBAgXk7+9/o04ZAAAAQC6XawKfr69vpu2ZBS67/eLHcuHCBbndbk2dOtVrf+PGjWW322Wz2byC3iUHDx5URESEKlasqPj4eMXFxXld4Vu8eLEkqWrVqnK73fLz8/unpwcAAAAAGeSawCdJixcv1oYNG+R0OpWUlKSNGzdetX9iYqLuvPNO8+rf888/r379+qlChQpXHXfq1Cnly5dPPXr00KZNmxQVFaUnn3xS69evl8vl0kMPPWT2/fLLL9WtW7dMj+N0OuV0Os3XCQkJWT1VAAAAAMhdga9du3YZbuk8evToFfufP39eoaGh+u677+RyubRixQpVrFhRBw8eVI0aNa44rnbt2tq0aZPef/997dixQwsXLlTx4sV19uxZOZ1OPfbYY4qNjdXs2bO1cuXKKwa+0aNHa+TIkdd1rgAAAACQqwJfdv32228qVaqUOnbsqH79+iklJUX79u3Thx9+qKFDh2Y65n//+59mzJih1atX6/Tp0ypdurT69u0rSeYtnevWrVORIkWUkpKiAQMGXPH9hwwZooEDB5qvExISFBYW9u+eJAAAAADLynXLMkgXb9V87bXXvG6XzMxvv/2m8uXLS5LeeOMNFStWTP369VNERIR8fX1lGIYMw/AaExoaKrvdrnnz5umOO+7Q8ePHFRkZqY8++ki7du3Sn3/+qalTp+rcuXPatm2bOnfufMX3dzgcCg4O9toAAAAAIKtyTeAzDENpaWmaOnWqypcvr23btikuLs7cHxsbq5EjR2rdunXKmzevJGnz5s265557rnhMt9ut5ORkr7ayZctq2rRpCg8PN9s8Ho86deqk1atXe7UBAAAAwI2Ua27p3LFjhxYtWqRjx47pyy+/1H/+8x9JMq/QFShQQCdPntTTTz+tRx55RHv37tWBAwd03333eV3FS0hI0Pnz52Wz2TRw4EAFBQVp9+7dGd7v71cP586dq9TUVLVr105TpkxR5cqVVbZs2Rt7wgAAAAByvVwT+AYPHqw2bdqY6+Nd8vdg9sknn5j/vG/fPnXo0EF58uRRSkqKpIvhcP369Tp69KiqVatm3u65Y8eODMs+lClTRpMmTdIbb7yh8ePHa/bs2YqOjtaMGTN05MgR9ejRQ0ePHlW5cuVu1CkDAAAAyOVyzS2dtWvXzhD2JMnlcmXav1OnTpo9e7YkadOmTXI4HEpOTlbTpk115MgRM+xNmzZN8+bN83oWr3v37ipbtqzatWun3bt3a+PGjerUqZOqVq2qvXv3asWKFfr11191//33Kykp6QacLQAAAABINuPyWUfwj/3222+KjY1VtWrVlC9fviv2O3v2rIoWLZrl4yYkJCgkJERhLyySjyPw3ygV/6KoMS1yugQAAADkEpeyQXx8/FUnd8w1t3TeTFWrVs1Sv+yEPQAAAADIrlxzSycAAAAA5DYEPgAAAACwKAIfAAAAAFgUgQ8AAAAALIrABwAAAAAWReADAAAAAIsi8AEAAACARbEO320ocmSzqy6uCAAAAAASV/gAAAAAwLIIfAAAAABgUQQ+AAAAALAoAh8AAAAAWBSBDwAAAAAsisAHAAAAABaV5WUZPvnkEzkcjmv2CwwM1IMPPqiCBQv+o8IAAAAAAP9MlgPft99+K39//2v227Nnj2bPnq3Vq1f/o8IAAAAAAP9MlgPfV199laV+GzZsUKtWra67IAAAAADAvyPLgU+S6tSpo8DAQNlsNq/29PR0paamasuWLapSpYo2bdr0rxYJAAAAAMi+bAW+1157TQ6HI0Pg83g8cjqdstvtCg0NVWho6L9aJAAAAAAg+2yGYRjXO9jtduu5555TQECAuRUtWlTdunVT3rx5/806ISkhIUEhISGKj49XcHBwTpcDAAAAIIdkNRtkeVmGYsWKKSwsTKVLl1aJEiV05swZpaen69NPP1XlypVVtGhR+fv7a968eXr11Vf/lZMAAAAAAFy/LN/Sef78eR08eFAej0eNGjXSpQuDdrtdzzzzjNnvjjvu0NixY//9SgEAAAAA2ZLlwGez2VSuXLmLg+x2TZ8+XYZhKD09XatWrVKNGjVUsmRJPfzww2rbtu0NKxgAAAAAkDVZvqXzcklJSTp//rxcLpdGjRqlSpUqqUWLFoqNjc3Sen0AAAAAgBsry4EvPT1dnTt31hNPPKGYmBgNGjRI48ePV548efTTTz/p2LFjKly4sO6//34dPXr0RtYMAAAAAMiCLM/S+cknn8hut8tms8ntdqtLly5KS0tT4cKF5XK5zH5du3bV7t279euvv2ZYvgH/DLN0AgAAAJCyng2y/Axf7969M7Q5HA7t3r1bkpSSkqI8efLoww8/1LZt2wh7AAAAAJDDsv0M34MPPmj+8++//24uuh4eHi5JCg4OVkpKigYMGPDvVQkAAAAAyLYsB76TJ09Kkvbt2ydJio2NVcuWLfXCCy/Iz8/Pa6KWVatWye12/8ulAgAAAACyI0u3dHo8HjVo0ECfffaZAgMDJUkdO3ZU/vz5tWzZMvn4+JiBLz4+XosXL9a2bdtuXNUAAAAAgGvKUuDz8fHR4sWL1blzZ7ndbg0bNkz79+/Xzp07lS9fPq++L730knr06KGKFSvekIIBAAAAAFmT5UlbatSooR9//FH33nuvSpQooaVLlyo0NNTcbxiG3nnnHe3bt08bNmy4EbUCAAAAALIhy4Gvb9++8vPzU2xsrPbt26fffvtNM2bMUGBgoCIjI3X8+HGtWbNG3333nez2LB8WAAAAAHCDZHnSlrCwMIWHhystLU1r165VeHi4SpcurapVq6pjx45yOBz67bff9MUXX9zIegEAAAAAWZTlhdelizN0Nm3aVCVLllTp0qW1cOFC82peuXLl9P333+vRRx/VqFGj1KZNmxtWdG7FwusAAAAApKxngyxf4duyZYvatm0ru92uTZs26fz58+rRo4dXn/Lly+uLL77Qiy++KKfTed3FAwAAAAD+uSwHvmnTpplLMDgcDi1ZskQbNmzQzJkzJV1cukGS7r77blWrVk3z5s27MRUDAAAAALIky4Hv008/VaVKlRQbGytJKlCggMaNG6eXX35ZZ8+elcvlMvu2b99eixcv/verBQAAAABkWZan0/TxuZgN//Of/8jlcsnPz0/t27dXUlKSgoKCVLRoUbNv3bp1WZoBAAAAAHJYtiZtQc669GBm2AuL5OMIzLRP1JgWN7kqAAAAADfbvz5pCwAAAADg9kLgAwAAAACLIvABAAAAgEUR+AAAAADAogh8AAAAAGBRBD4AAAAAsCgCHwAAAABYFIEPAAAAACyKwAcAAAAAFkXgy4bExER9+umnunDhQpbHpKen38CKAAAAAODKcl3gO378uIoXL+7VFhMTo6ioKHOLjo7OdKyfn59eeuklffHFF1d9j/T0dLndbknSsmXLVK9ePUnShAkT9Oqrr/4LZwEAAAAA12bP6QJuNn9/f/n7+3u1vfLKK0pKStJdd92l9evXq0CBAlq8eLEeeOABRUdHK2/evGbfIkWKaMKECZowYYLZdvToUS1ZskTNmzeXJH355ZcaNmyY/Pz8lJycrHPnzikiIkIxMTHyeDxatWqVPB6P9u/fr6SkJAUFBd2ckwcAAACQq+SawNerVy9t27ZNbrdbp0+fVkREhNnu5+enNm3aqFOnTvrpp5/Utm1bc9z06dPNICdJtWrV0vTp01WrVi2zrW7duvLx+f+LpZ06dVKnTp0kSRs2bNDbb7+tNWvWaMyYMUpNTdWIESMUFxenAgUKZAifAAAAAPBvyTWB7+TJkxo3bpyqV6+uunXrKjIyUlOnTtXZs2dlt1/8GJKTk/XTTz9p4cKFkqSxY8cqNjZWVapUUXBwsAzD0J49e/T888/LZrMpJSVF1atX19KlS5U/f37zvQ4ePKiIiAhVrFhR8fHxiouL87rCt3jxYklS1apV5Xa75efnd9M/DwAAAADWl2sC39+vwF2pffXq1brvvvsUHBwsSeazd48++qiOHj2qAQMGqFixYipQoICaNGmihx56SFWqVMlwzFOnTilfvnzq0aOHNm3apKioKD355JNav369XC6XHnroIbPvl19+qW7dumVam9PplNPpNF8nJCRk/8QBAAAA5Fq5ZtIWm812zfaUlBQVLVrUa/+bb76pWrVqqV27durTp49OnDihsWPH6sKFC+rQoYOqVKliTtBySe3atbVp0yYdPnxYO3bs0NSpU/XYY4+pSpUqqlSpkh577DE1aNBAR48e1cyZM69Y8+jRoxUSEmJuYWFh/+ATAAAAAJDb2AzDMHK6iJuhZcuWOnTokAIDA7V//37dddddOnfunHr27KmYmBjVq1dPHTp0UM2aNTV//nzZ7Xa9+eabioyMVGBgoCpVqpTpcaOjo1WiRAmlpaVpwYIF+t///qcZM2Zo9erVOn36tEqXLq18+fJJknlLZ2hoqIoUKaKUlBQNGDBAnTt3zvTYmV3hCwsLU9gLi+TjCMx0TNSYFv/wkwIAAABwq0tISFBISIji4+PNOxQzk2tu6TQMQ1OmTDGf4bt05S0mJsbs4+PjowEDBmjcuHF677331LZtWz3xxBNXvDoYFxenQYMG6dlnnzWDWWhoqOx2u+bNm6c+ffro8OHDkqQtW7aoXLlyKl68uDZs2KB+/fopMjLyqjU7HA45HI5/6RMAAAAAkNvkmsCXVffdd59GjBihIkWKqF27dtq8ebMaN27stXZfdHS0IiMjFRISojx58qhNmzbmvrJly2ratGlm0JMkj8ejTp06afjw4erVq5fZBgAAAAA3Uq4JfFe6c/XvwWvbtm2aM2eOChcubLY5HA7VqFFD27ZtM9vKly+vPHnyXPX9/n4r5ty5c5Wamqp27dppypQpqly5ssqWLXu9pwIAAAAAWZJrAp/b7dYzzzyjvHnzKm/evIqIiNBff/2l7t27m5Ou7NmzR8nJyfr888/NcXa7XZGRkV7r7p08eVJut/uqa+iVKVNGkyZN0htvvKHx48dr9uzZio6O1owZM3TkyBH16NFDR48eVbly5W7cSQMAAADI1XJN4Bs0aJAiIiJUsmRJs+3AgQNKTEzU9OnTJUl9+vRRnz59vMbly5dPr7zyioYOHWq2TZw4UQcOHNA333yT6QOS3bt31zfffKOEhAQ1adJEGzdu1L333itJ2rt3r9auXauRI0fq/vvv1+HDh5U3b94bccoAAAAAcrlcM0vnv23Dhg366quv1L17d6+rf5L022+/KTY2VtWqVTNn6MzM2bNnMywDcTWXZuJhlk4AAAAgd2OWzhusYcOGatiwYab7qlatmqVjZCfsAQAAAEB25ZqF1wEAAAAgtyHwAQAAAIBFEfgAAAAAwKIIfAAAAABgUQQ+AAAAALAoAh8AAAAAWBSBDwAAAAAsinX4bkORI5tddXFFAAAAAJC4wgcAAAAAlkXgAwAAAACLIvABAAAAgEUR+AAAAADAogh8AAAAAGBRBD4AAAAAsCgCHwAAAABYFIEPAAAAACyKwAcAAAAAFkXgAwAAAACLIvABAAAAgEUR+AAAAADAogh8AAAAAGBRBD4AAAAAsCgCHwAAAABYFIEPAAAAACyKwAcAAAAAFkXgAwAAAACLIvABAAAAgEUR+AAAAADAogh8AAAAAGBRBD4AAAAAsCgCHwAAAABYFIEPAAAAACyKwAcAAAAAFkXgAwAAAACLIvABAAAAgEUR+AAAAADAogh8AAAAAGBRBD4AAAAAsCgCHwAAAABYlD2nC0D2RQz/Tj6OQElS1JgWOVwNAAAAgFsVV/gAAAAAwKIIfAAAAABgUQQ+AAAAALAoAh8AAAAAWBSBDwAAAAAsisAHAAAAABZF4AMAAAAAiyLwAQAAAIBFEfgAAAAAwKJuu8B37tw5uVyuDO0xMTE5UA0AAAAA3LpyLPD9+eef6tatm/76668M+wYNGqRXX301Q3taWpqaN2+ud955R5Lk8XjkdDoVGxurihUr6uOPP5YkGYahtLQ0r7EjRoxQ27Zts13nuXPn5HA4vNrS09MVFRWl1atX67XXXtPUqVMzjPvss8/UqFGjTI85b9481a1bN9u1AAAAAEB25FjgCw0NVaFChdSgQQOdOXPGa5+fn58CAgK82jwej5566imdPn1aH330kUJCQhQREaFOnTqpd+/eCgoK0ujRo5UnTx5VrVpVDz/8sNf4PHnyKF++fObrt99+Ww6HQ/nz51dQUJBq167t1f+bb75RaGio7rnnHrlcLhUrVkxBQUEaOHCgSpQooWrVqmnlypVq2rSpunbtmuH88uTJozx58mR67g6HQyEhIdn6vAAAAAAgu3L0ls6JEyeqTp06+vHHH6/Zd8+ePXK73YqMjNRXX32l9u3ba9++fRo6dKgCAwN18OBB7dq1S08++aR2796ttWvXSpLcbrfcbrd8fX1ls9nk8XjkcrkUEBCgPn36KC4uTnPnzs0QMB9++GGdPn1ae/fuVWBgoM6cOaPExES99dZbOn36tGrUqKHHHntMjRo1Uv78+c1xTqdTqampstls8vHxUVpamtLS0pSSkmL28fHxkZ+fX4Z2AAAAAPg32XPiTRMTE5WSkiIfHx+NGzdOwcHBSk9Pl6+vb6b9PR6PatSooQULFmju3Lnq16+fKlasqFq1apl96tevL0natWuXBg4cqEqVKkmSJk2apHfffVfJycmSpLVr16p9+/YKCwuTzWYzx1/+3n/88Ydq1qypgIAAJScnq2TJknr00Uc1dOhQ/fjjjzp79qxmzZqlTz/9VCdPnjRD6/DhwzV16lR5PB653W4VKlRIL7/8snn10WazKSEhQZJUqFAh1apVS+vXr/+XPlkAAAAA+H85EviWLFmiUaNGyel0Kn/+/Jo8ebKaNWumoKAg2Ww2M5xdejYuOTlZ+/fvV3h4uAICAlSoUCG1bNky02Pv3btX/v7+5uvBgwdr8ODBqlmzpu6++27NmTNHkjRhwoQrBkxJKl26tGJiYhQXF6dSpUopOjpakjR37lytW7dOFy5cUHh4uFq1aqVy5cqZ48aMGaMxY8aoa9euiouL06pVqyRJQ4cOlXRxcpny5curXr165r4rcTqdcjqd5utLQREAAAAAsiJHbuns3r27Dh48qDlz5sjPz08NGjRQamqqzp8/r5iYGA0YMEAvv/yyYmJiFBMTo+TkZIWHh18s2MdH+fLlU61atTLdfH195ePjfVonTpzQ7t27FRsbqyVLlkiSXC6XVzC83DvvvKMiRYqoSpUqSk5OVlhYmIoWLaru3btr7ty5KleunBo3bqz69esrb968Xs8hnjlzRkuWLFFUVJTee+89r+O++eabKl26dJY+p9GjRyskJMTcwsLCsjQOAAAAAKQcusL3d1e7ypYZj8ejqKgojRgxItP9aWlpMgzDq+3DDz9Uvnz5lJycrD59+ig0NFQJCQkKCgq64vu8/vrrev311/XGG29o0qRJOnTokBwOhxYsWKBjx47pyJEj6tevnxITExUfH6+BAwdq1KhRkqS33npLYWFhKlKkiObOnavSpUurY8eOWrdundatW6ehQ4dq3rx51zzXIUOGaODAgebrhIQEQh8AAACALMvxwHdJfHx8lmauTEtLU/78+dWwYcNM9+/du9drnb6zZ89q2rRp6tKli5KTk9W9e3clJSXpxIkT5nN/VxITE6Pp06dLkp566ik1a9ZMixcv1h133CE/Pz/169dP7du3V4kSJcznAbdu3arFixdr7NixWrx4sSZPniy73a6jR4+qe/fuWrx4sU6fPp2lz8ThcGRYEgIAAAAAsuqWWHh99uzZevTRR7PUt2XLlvrmm29UtmxZbdy4UT169FCPHj1Uu3ZtLViwQL/88ot5+6d08Upd8+bNzefsunbtqubNm+vgwYOqUqXKVd+rb9++6tSpkySpc+fOevvtt7VixQpNmjRJpUuXVqVKlVSyZEmvyV9effVVTZkyxZy58+6775aPj48efPBBDRs2THXq1MnORwMAAAAA1y1Hr/Clp6fr0KFDevXVV/Xdd99dte+0adP07rvvKjAwUDabTTExMUpNTTUDWWpqqs6dO6dOnTrJ4/EoMTFRs2bN0quvvqqAgAAtXLjQPNalWzL/PsvnJefPn1ehQoU0atQo7dy5Uz/++KPmzJmjli1bqlq1al7h7pL169erZMmSuvPOO7Vw4UIVL15cy5Ytk3TxNswmTZro5ZdfVu/evf/BpwUAAAAA2ZOjgW///v3yeDz6+uuvVb169av27du3r/r27Su3262lS5dq8ODBWrZsmerWrSvp4q2U7du3V2RkZKbj09PTlZ6eLsMwNHDgQPXq1cu8XdJmsyktLU2JiYkqWbKkzp8/r9KlS2v+/PkKDAw0nwn8+/Nzvr6+Onv2rAzD0LRp01SsWDFNmTJFxYsXl3RxUhi3263g4GDt3btXBQsWlCQdOXJEW7du5VZNAAAAADdcjt7S2aVLF33//fe65557vNqvtCD5zp07VaZMGS1evFjffvutGfak/1/w/EpSU1OVmpqqn376ScePHzeXSZCkihUr6uDBg6pbt66aNm2qoKAgde3aVffff79SUlIyraVDhw568803VbZsWe3bt099+/b12v/3ei6FPUn67LPPtHHjRvXv3/8anw4AAAAA/DM24/IpLW9xaWlpV11OISuutsj7rSwhIeHi8gwvLJKPI1CSFDWmRQ5XBQAAAOBmu5QN4uPjFRwcfMV+t8SkLdnxT8OelP2lIAAAAADgdnTbBT4AAAAAQNYQ+AAAAADAogh8AAAAAGBRBD4AAAAAsCgCHwAAAABYFIEPAAAAACyKwAcAAAAAFmXP6QKQfZEjm111cUUAAAAAkLjCBwAAAACWReADAAAAAIsi8AEAAACARRH4AAAAAMCiCHwAAAAAYFEEPgAAAACwKAIfAAAAAFgUgQ8AAAAALIrABwAAAAAWReADAAAAAIsi8AEAAACARRH4AAAAAMCiCHwAAAAAYFEEPgAAAACwKAIfAAAAAFgUgQ8AAAAALIrABwAAAAAWReADAAAAAIsi8AEAAACARRH4AAAAAMCiCHwAAAAAYFEEPgAAAACwKAIfAAAAAFgUgQ8AAAAALIrABwAAAAAWReADAAAAAIsi8AEAAACARRH4AAAAAMCiCHwAAAAAYFEEPgAAAACwKAIfAAAAAFgUgQ8AAAAALIrABwAAAAAWReADAAAAAIsi8AEAAACARRH4AAAAAMCiCHwAAAAAYFEEPgAAAACwKAIfAAAAAFgUgQ8AAAAALIrABwAAAAAWReADAAAAAIsi8AEAAACARRH4ctjSpUu1YsWKnC4DAAAAgAUR+HKQx+PR3Llz9fDDD+d0KQAAAAAsiMCXgxYuXKi2bdvKz88vp0sBAAAAYEH2nC4gt3K73friiy+0ZMmSnC4FAAAAgEUR+HLI3Llz1bVrV/n6+l6xj9PplNPpNF8nJCTcjNIAAAAAWAS3dOaAtLQ0rVy5Uu3atbtqv9GjRyskJMTcwsLCblKFAAAAAKzAZhiGkdNF5DZTp05VeHi4Hn300av2y+wKX1hYmOLj4xUcHHyjywQAAABwi0pISFBISMg1swG3dN5kKSkpWrduXZae3XM4HHI4HDehKgAAAABWxC2dN9nUqVP17LPP5nQZAAAAAHIBAt9NlJiYqJ9//llNmjTJ6VIAAAAA5AIEvpto0qRJeuGFF3K6DAAAAAC5BM/w3URt27ZV1apVc7oMAAAAALkEV/huIsIeAAAAgJuJwAcAAAAAFkXgAwAAAACLIvABAAAAgEUR+AAAAADAogh8AAAAAGBRBD4AAAAAsCgCHwAAAABYFIEPAAAAACyKwAcAAAAAFkXgAwAAAACLIvABAAAAgEUR+AAAAADAogh8AAAAAGBRBD4AAAAAsCgCHwAAAABYFIEPAAAAACyKwAcAAAAAFkXgAwAAAACLIvABAAAAgEUR+AAAAADAogh8AAAAAGBRBD4AAAAAsCgCHwAAAABYlD2nC0DWGYYhSUpISMjhSgAAAADkpEuZ4FJGuBIC323k/PnzkqSwsLAcrgQAAADArSAxMVEhISFX3E/gu40ULFhQkvTHH39c9V8qco+EhASFhYXpxIkTCg4OzulycAvgO4HL8Z3A5fhO4HJ8J25PhmEoMTFRJUqUuGo/At9txMfn4iOXISEh/I8RXoKDg/lOwAvfCVyO7wQux3cCl+M7cfvJykUgJm0BAAAAAIsi8AEAAACARRH4biMOh0PDhw+Xw+HI6VJwi+A7gcvxncDl+E7gcnwncDm+E9ZmM641jycAAAAA4LbEFT4AAAAAsCgCHwAAAABYFIEPAAAAACyKwAcAwG0uJiZGZcuWVVRUVJb6t2rVSjabzdyaNGlyYwsEkKOWL1+ucuXKyW63q3r16tq/f/81x/A7YR0EvltIZGSkateurQIFCuill15SVubTWbx4scLDw1WiRAktWLDgJlSJm+l6vhN333231w90r169bkKluJmy+8f9xo0bVblyZRUuXFgTJky4scXhpouJiVHLli2z/H2QpB07dujXX39VbGysYmNjtXz58htXIG666/njnt8J6zpy5Ih69uypMWPGKDo6WnfeeWeW/jbgd8I6CHy3CKfTqUcffVT33HOPduzYoX379mnOnDlXHRMZGakuXbpo6NCh+u677zRs2DAdPHjw5hSMG+56vhPJyck6cuSI/vzzT/MH+v333785BeOmyO4f9+fOnVOrVq30xBNPaOvWrfrss8+0fv36G1skbqpOnTqpc+fOWe4fHR0twzAUERGh/PnzK3/+/AoKCrqBFeJmup4/7vmdsLb9+/drzJgx6tChg4oWLaq+fftq165dVx3D74TFGLglLF261ChQoIBx4cIFwzAMY/fu3cYDDzxw1THPP/+80axZM/P1pEmTjNdff/2G1omb53q+E5s3bzbq1q17M8pDDmncuLExefJkQ5Jx7Nixa/afOHGiUalSJcPj8RiGYRjLli0zunTpcoOrxM109OhRwzCMLH8nlixZYhQpUsQoWbKkERgYaHTs2NH466+/bnCVuFlWrlxpfPTRR+brdevWGXny5LnqGH4ncpdp06YZd99991X78DthLVzhu0Xs2bNHdevWVWBgoKSLt+Xt27fvmmMefPBB8/W9996rnTt33tA6cfNcz3fi559/1smTJ1WkSBHlz59fffv2ldPpvBnl4ib55JNPNGDAgCz337Nnjxo1aiSbzSaJ3wkrKlu2bLb6HzhwQNWqVdPq1au1bds2HTt2TEOGDLlB1eFma9mypZ5++mnz9cGDB1WhQoWrjuF3IvdIS0vT+PHj9cwzz1y1H78T1kLgu0UkJCR4/UfbZrPJ19dXsbGxWR4THBysU6dO3dA6cfNcz3fi4MGDqlevnjZv3qzvvvtOP/zwgyZOnHgzysVNkt0/7vmdwOWGDBmiH374QdWqVdNdd92l9957T4sXL87psnADZPWPe34nco/hw4crKCjomrf58jthLfacLgAX2e12ORwOr7aAgAAlJyerQIECWRpzqT+s4Xq+E9OnT/d6PWzYME2ZMkWvvvrqDasTtzZ+J3AtoaGhOn/+vJxOZ4bfHNzesvrHPb8TucO6dev0wQcfaNu2bfLz88vWWH4nbm9c4btFFCxYUOfOnfNqS0xMlL+/f5bHXKs/bi/X8524XGhoqKKjo//t0nAb4XcCl+vYsaM2b95svt66dauKFi3KH3EWc+mP+88///yaf9zzO2F9x44d0xNPPKEPPvhAVapUuWZ/fieshcB3i6hdu7a2bt1qvj527JicTqcKFiyY5TG7du1SyZIlb2iduHmu5ztx33336cSJE+brrVu3Kjw8/IbWiVsbvxO5V0JCglwuV4b2u+66Sy+++KI2b96sZcuWaciQIerbt28OVIgbJbt/3PM7YW0pKSlq2bKlWrdurTZt2igpKUlJSUkyDIPfidwip2eNwUUul8soUqSIMWvWLMMwDKNXr15Gy5YtDcMwjNjYWMPtdmcYs3v3biMoKMjYu3evkZiYaFSvXt0YN27cTa0bN871fCeeeuop4+GHHza2bdtmzJkzxwgKCjLmzJlzU+vGzaHLZmSMj4830tLSMvQ7d+6cERAQYPzwww9GWlqa0bx5c6Nfv343sVLcLJd/J8LDw42lS5dm6JeWlmb897//NYKCgoxixYoZI0eONFwu180rFDdUcnKyUaVKFaN3795GYmKiuXk8Hn4ncqlly5YZkjJsx44d43cilyDw3UKWL19uBAYGGoUKFTKKFCli/Pbbb4ZhXPyP+K5duzId89prrxn+/v5GcHCwcc899xjJyck3sWLcaNn9TsTGxhqPPfaYkSdPHiM8PNz48MMPb3LFuFmy+se9YVycgtvPz88oUKCAUbZsWePMmTM3p0gAN931/HFvGPxOAFZmMwzDyIELi7iCM2fOaOfOnapbt64KFSqUpTH79u1TdHS0GjRowD33FnQ93wngcseOHdOBAwdUv3595c2bN6fLAXAL4ncCsCYCHwAAAABYFJO2AAAAAIBFEfgAAAAAwKIIfAAAAABgUQQ+AABuQQMHDtSWLVuu2W/s2LFea6ilpKQoPT3dfG0YhlJSUm5IjQCA7IuJiVHZsmUVFRWVrXEdO3ZU//79s/1+9myPAAAAN9SCBQs0c+ZMHThwQMuXL5efn98V+65fv17Jycm67777JEktW7bUoUOHlJSUpPT0dBUoUEChoaHavn37zSofAHAFMTExatmyZbbD3tdff60NGzbo4MGD2X5PZukEAOAWsn37drVo0UJff/21Pv/8cx0+fFgLFy5UYGCg2efnn3/Wf//7X0nS6dOnZRiGSpQooQoVKmjp0qWSpBdffFHFihXTK6+8kiPnAQDIqEmTJmrVqpWef/55HTt2TGXKlLnmmAsXLqhq1aoaNmyY+dufHdzSCQDALWLz5s1q3bq1PvvsM9WqVUsTJkxQ2bJlVbduXe3YscPs53Q6VbhwYc2fP18ffPCB/vrrL33zzTeKiYkx+xw+fFi1atXKidMAAFzBJ598ogEDBmRo3759u+rUqaOQkBC1bdtW8fHx5r6RI0cqLS1NdrtdP/zwgzweT7bek8AHAEAOS09P13vvvadWrVppwYIFatq0qblv8uTJ6tmzp+6//361bdtWP/zwg+6880699dZbev7557V582ZJUlhYmDZt2qR33nlHDRs21MaNG/X666+rYcOGZh8AQM4qW7Zshra4uDg9/PDDevjhh7V3714lJCRo0KBBkqTjx49r8uTJKlu2rI4ePapXXnlFjz32WLZCH8/wAQCQg44cOaIWLVpIklq0aKFmzZrJ39/f/H9zfX195XQ69dJLLyklJUV9+vTR1q1b9eOPP+rAgQO6cOGCKlWqpPPnz+vw4cOKiopSuXLl1LVrV0nSzJkzlZCQkJOnCAC4itWrV8vPz0/Dhw+XzWbT4MGD1a1bN0nS3LlzVbRoUa1du1YBAQEaNGiQwsPDtWbNGj300ENZOj6BDwCAHHTHHXdoyJAh6tixowICAjRv3jxJUqdOndS8eXP16NHDq//48eP19ddfa9asWerWrZsKFSqk9evX6+2331ZISIh8fX31wAMPKDo6WuXLl9cdd9whHx9u6AGAW9XJkyd17tw5FShQQJLk8XiUmJio1NRUnTx5Uk2aNFFAQIAkKV++fKpQoYIOHz5M4AMA4HbRvXt3uVwupaeny9fXN9M+brdbhmHIz89PLVq0UL169XT48GHVr19fdrtd9erVU1JSktl/165dKlq06M06BQDAdSpVqpTuueceffHFF5IuLqcTHx8vPz8/lSpVSvv37zf7ejwenTx5UiVLlszy8fm//AAAuAX0799fISEhyp8/v/Lnz68lS5bo2WefVf78+c32oUOHSrr4TMfIkSPVs2dPff311zpw4ICOHj2qnj176tLk2/Hx8apUqVJOnhIAIAtatGihP/74Qz///LPy5MmjxYsXq3nz5jIMQ+3bt9eKFSv01Vdf6eTJkxoyZIhcLpeaNGmS5eNzhQ8AgFvA9OnTVbVqVbVs2VJly5b1uqVz5cqVOnr0qJ5//nlJFwPfvn37FBUVpcGDB8vtdis6OlpnzpxRz549lZ6erv379+uuu+6SJLlcrpw8NQDAVeTPn18rVqxQv3791LNnT1WtWlUrVqyQ3W5X5cqVtWDBAg0dOlSHDh1S+fLltXz5cgUFBWX5+KzDBwDALWLIkCHasmWLNmzYoM6dO5uBr169emrTpo05a5sk7d69W4MHD9aaNWt05swZ1a5dWydOnND+/fu1dOlSHTp0SHXq1FGhQoXUrFkzhYSE5OCZAQByCrd0AgCQw9LT0+XxeDRy5EidP39ev/76q7lv5cqVcjqdeuGFF5Seni632y3p4jMe27ZtU0REhBo2bGjeynno0CEtXLhQ48aNk2EYevrpp7V169YcOS8AQM7jCh8AADnsnXfe0YgRIxQQECAfHx/ZbDZ5PB7ZbDYzyHk8Hrndbv3nP//R999/r61bt2ro0KHmFb6IiAj17NlTW7Zs0aJFi1SqVClJ0owZM3T06FGNGjUqJ08RAJBDCHwAAFjEhQsX5HA4ZLfziD4A4CICHwAAAABYFM/wAQAAAIBFEfgAAAAAwKIIfAAAAABgUQQ+AAAAALAoAh8AAAAAWBSBDwAAAAAsisAHAAAAABZF4AMAAAAAiyLwAQAAAIBF/R8PAQyCg5n1YQAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 10. 可视化：各类别办公费用总额\n", "plt.figure(figsize=(10,5))\n", "df.groupby('类别')['金额'].sum().sort_values().plot(kind='barh')\n", "plt.title('各类别办公费用总额')\n", "plt.xlabel('金额')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 结论\n", "1. 通过机器学习模型，可以预测未来某个月的办公费用。\n", "2. 固定费用类别为：见上文输出\n", "3. 不固定费用类别为：见上文输出\n", "4. 每年都会有的办公费用类别为：见上文输出\n", "5. 你可以根据predict_future函数，输入不同的部门、类别、费用类型和年月，预测未来的费用。\n", "\n", "如需更详细分析或可视化，欢迎随时补充需求！"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 4}