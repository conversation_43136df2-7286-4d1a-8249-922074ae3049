import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

print("🔧 开始办公费用特征工程...")

# 1. 读取基础数据
print("📂 读取办公费用基础数据...")
try:
    df = pd.read_csv('办公费用办公费用（剔除专项）.csv', encoding='utf-8')
except:
    try:
        df = pd.read_csv('办公费用（剔除专项）.csv', encoding='gbk')
    except:
        print("❌ 无法读取办公费用.csv文件，请检查文件路径")
        exit()

print(f"原始数据形状: {df.shape}")
print("原始数据列名:", df.columns.tolist())

# 2. 数据清洗和预处理
print("\n🧹 数据清洗...")
# 处理金额字段
df['金额'] = df['金额'].astype(str).str.replace(',', '').astype(float)
# 删除全空列
df = df.dropna(axis=1, how='all')
# 处理类别统一
if '类别' in df.columns:
    df['类别'] = df['类别'].replace('邮寄', '邮寄费')
else:
    df['类别'] = '默认类别'

# 生成标准化日期字段
df['年月'] = df['年'].astype(str) + '-' + df['月'].astype(str).str.zfill(2)
df['日期'] = pd.to_datetime(df['年月'] + '-01')

print(f"清洗后数据形状: {df.shape}")

# 3. 创建月度时间序列基础框架
print("\n📅 创建月度时间序列框架...")
start_date = df['日期'].min()
end_date = df['日期'].max() + pd.DateOffset(months=12)  # 延伸12个月用于预测
date_range = pd.date_range(start=start_date, end=end_date, freq='MS')

# 创建基础时间框架
base_df = pd.DataFrame({
    '年月': date_range.strftime('%Y-%m'),
    '年': date_range.year,
    '月': date_range.month,
    '季度': date_range.quarter,
    '日期': date_range
})

print(f"时间框架: {base_df['年月'].min()} 到 {base_df['年月'].max()}")

# 4. 计算历史统计特征
print("\n📊 计算历史统计特征...")
# 按月汇总历史数据
monthly_summary = df.groupby('年月').agg({
    '金额': ['sum', 'mean', 'count', 'std']
}).round(2)

monthly_summary.columns = ['月度总金额', '月度平均金额', '月度交易次数', '月度金额标准差']

# 添加类别数量统计
if '类别' in df.columns:
    category_count = df.groupby('年月')['类别'].nunique()
    monthly_summary['月度类别数量'] = category_count

monthly_summary = monthly_summary.reset_index()
monthly_summary = monthly_summary.fillna(0)

# 5. 创建超宽特征
print("\n🎯 创建超宽特征...")
# 获取所有类别
categories = df['类别'].unique() if '类别' in df.columns else ['默认类别']
departments = df['部门'].unique() if '部门' in df.columns else ['默认部门']

print(f"发现类别: {categories}")
print(f"发现部门: {departments}")

# 为每个月创建类别金额特征
category_features = {}
for category in categories:
    cat_data = df[df['类别'] == category].groupby('年月')['金额'].sum()
    category_features[f'类别_{category}_金额'] = cat_data

# 为每个月创建部门金额特征
dept_features = {}
for dept in departments:
    if '部门' in df.columns:
        dept_data = df[df['部门'] == dept].groupby('年月')['金额'].sum()
    else:
        dept_data = df.groupby('年月')['金额'].sum()  # 默认处理
    dept_features[f'部门_{dept}_金额'] = dept_data

# 6. 计算年度预算特征
print("\n💰 计算年度预算特征...")
yearly_budget = df.groupby('年')['金额'].sum().to_dict()
print("年度预算统计:", yearly_budget)

# 7. 创建最终特征数据集
print("\n🔨 构建最终特征数据集...")
feature_df = base_df.copy()

# 合并月度统计特征
feature_df = feature_df.merge(monthly_summary, on='年月', how='left')

# 添加类别特征
for feature_name, feature_data in category_features.items():
    feature_df[feature_name] = feature_df['年月'].map(feature_data).fillna(0)

# 添加部门特征
for feature_name, feature_data in dept_features.items():
    feature_df[feature_name] = feature_df['年月'].map(feature_data).fillna(0)

# 添加年度预算特征
feature_df['年度预算'] = feature_df['年'].map(yearly_budget)
# 对于未来年份，使用历史平均值
avg_budget = np.mean(list(yearly_budget.values()))
feature_df['年度预算'] = feature_df['年度预算'].fillna(avg_budget)

# 8. 添加时间特征
print("\n⏰ 添加时间特征...")
feature_df['是否年初'] = (feature_df['月'] <= 3).astype(int)
feature_df['是否年中'] = ((feature_df['月'] >= 4) & (feature_df['月'] <= 9)).astype(int)
feature_df['是否年末'] = (feature_df['月'] >= 10).astype(int)
feature_df['是否上半年'] = (feature_df['月'] <= 6).astype(int)
feature_df['是否第一季度'] = (feature_df['季度'] == 1).astype(int)
feature_df['是否第二季度'] = (feature_df['季度'] == 2).astype(int)
feature_df['是否第三季度'] = (feature_df['季度'] == 3).astype(int)
feature_df['是否第四季度'] = (feature_df['季度'] == 4).astype(int)

# 添加月份的周期性特征
feature_df['月份_sin'] = np.sin(2 * np.pi * feature_df['月'] / 12)
feature_df['月份_cos'] = np.cos(2 * np.pi * feature_df['月'] / 12)

# 9. 添加滞后特征
print("\n📈 添加滞后特征...")
feature_df = feature_df.sort_values('日期')
feature_df['上月总金额'] = feature_df['月度总金额'].shift(1)
feature_df['前2月总金额'] = feature_df['月度总金额'].shift(2)
feature_df['前3月总金额'] = feature_df['月度总金额'].shift(3)
feature_df['上季度平均金额'] = feature_df['月度总金额'].rolling(window=3).mean().shift(1)
feature_df['上半年平均金额'] = feature_df['月度总金额'].rolling(window=6).mean().shift(1)
feature_df['同比去年同月'] = feature_df['月度总金额'].shift(12)

# 添加趋势特征
feature_df['3月移动平均'] = feature_df['月度总金额'].rolling(window=3).mean()
feature_df['6月移动平均'] = feature_df['月度总金额'].rolling(window=6).mean()
feature_df['月度增长率'] = feature_df['月度总金额'].pct_change()

# 10. 填充缺失值
print("\n🔧 处理缺失值...")
# 数值型特征用0填充
numeric_cols = feature_df.select_dtypes(include=[np.number]).columns
feature_df[numeric_cols] = feature_df[numeric_cols].fillna(0)

# 11. 添加目标变量（下月金额，用于预测）
feature_df['下月预测目标'] = feature_df['月度总金额'].shift(-1)

# 12. 添加累积特征
print("\n📊 添加累积特征...")
feature_df['年内累积金额'] = feature_df.groupby('年')['月度总金额'].cumsum()
feature_df['年内累积占比'] = feature_df['年内累积金额'] / feature_df['年度预算']

# 13. 输出结果
print("\n💾 保存特征工程结果...")
output_file = '办公费用特征工程数据.csv'
feature_df.to_csv(output_file, index=False, encoding='utf-8-sig')

print(f"✅ 特征工程完成！")
print(f"📊 最终数据形状: {feature_df.shape}")
print(f"📁 输出文件: {output_file}")

# 14. 显示特征统计
print("\n📋 特征统计信息:")
print("=" * 50)
print(f"时间范围: {feature_df['年月'].min()} 到 {feature_df['年月'].max()}")
print(f"总特征数: {len(feature_df.columns)}")
print(f"类别特征数: {len([col for col in feature_df.columns if '类别_' in col])}")
print(f"部门特征数: {len([col for col in feature_df.columns if '部门_' in col])}")

print("\n前5行数据预览:")
print(feature_df.head())

print("\n特征列表:")
for i, col in enumerate(feature_df.columns, 1):
    print(f"{i:2d}. {col}")

print(f"\n📈 数据统计:")
print(f"- 历史数据月份数: {len(feature_df[feature_df['月度总金额'] > 0])}")
print(f"- 预测月份数: {len(feature_df[feature_df['月度总金额'] == 0])}")
print(f"- 平均月度金额: {feature_df[feature_df['月度总金额'] > 0]['月度总金额'].mean():.2f}")
print(f"- 最高月度金额: {feature_df['月度总金额'].max():.2f}")