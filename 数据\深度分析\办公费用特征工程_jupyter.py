import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 尝试导入scipy，如果失败则使用替代方案
try:
    from scipy import stats
    HAS_SCIPY = True
except ImportError:
    HAS_SCIPY = False
    print("⚠️ scipy未安装，将使用简化的异常值检测方法")

# 配置参数
PREDICTION_CONFIG = {
    'target_month': 6,  # 预测目标月份
    'history_window': 3,  # 历史数据窗口长度（月）
    'reference_months': [3, 4, 5]  # 参考月份（3月、4月、5月）
}

print("🔧 开始办公费用特征工程...")

# 1. 读取基础数据
print("📂 读取办公费用基础数据...")
try:
    df = pd.read_csv('办公费用.csv', encoding='utf-8')
except:
    try:
        df = pd.read_csv('办公费用.csv', encoding='gbk')
    except:
        print("❌ 无法读取办公费用.csv文件，请检查文件路径")
        exit()

print(f"原始数据形状: {df.shape}")
print("原始数据列名:", df.columns.tolist())

# 2. 数据清洗和预处理
print("\n🧹 数据清洗...")
# 处理金额字段
df['金额'] = df['金额'].astype(str).str.replace(',', '').astype(float)
# 删除全空列
df = df.dropna(axis=1, how='all')
# 处理类别统一
if '类别' in df.columns:
    df['类别'] = df['类别'].replace('邮寄', '邮寄费')
else:
    df['类别'] = '默认类别'

# 生成标准化日期字段
df['年月'] = df['年'].astype(str) + '-' + df['月'].astype(str).str.zfill(2)
df['日期'] = pd.to_datetime(df['年月'] + '-01')

print(f"清洗后数据形状: {df.shape}")

# 3. 创建月度时间序列基础框架
print("\n📅 创建月度时间序列框架...")
start_date = df['日期'].min()
end_date = df['日期'].max() + pd.DateOffset(months=12)  # 延伸12个月用于预测
date_range = pd.date_range(start=start_date, end=end_date, freq='MS')

# 创建基础时间框架
base_df = pd.DataFrame({
    '年月': date_range.strftime('%Y-%m'),
    '年': date_range.year,
    '月': date_range.month,
    '季度': date_range.quarter,
    '日期': date_range
})

print(f"时间框架: {base_df['年月'].min()} 到 {base_df['年月'].max()}")

# 4. 计算历史统计特征
print("\n📊 计算历史统计特征...")
# 按月汇总历史数据
monthly_summary = df.groupby('年月').agg({
    '金额': ['sum', 'mean', 'count', 'std']
}).round(2)

monthly_summary.columns = ['月度总金额', '月度平均金额', '月度交易次数', '月度金额标准差']

# 添加类别数量统计
if '类别' in df.columns:
    category_count = df.groupby('年月')['类别'].nunique()
    monthly_summary['月度类别数量'] = category_count

monthly_summary = monthly_summary.reset_index()
monthly_summary = monthly_summary.fillna(0)

# 5. 创建超宽特征
print("\n🎯 创建超宽特征...")
# 获取所有类别
categories = df['类别'].unique() if '类别' in df.columns else ['默认类别']
departments = df['部门'].unique() if '部门' in df.columns else ['默认部门']

print(f"发现类别: {categories}")
print(f"发现部门: {departments}")

# 为每个月创建类别金额特征
category_features = {}
for category in categories:
    cat_data = df[df['类别'] == category].groupby('年月')['金额'].sum()
    category_features[f'类别_{category}_金额'] = cat_data

# 为每个月创建部门金额特征
dept_features = {}
for dept in departments:
    if '部门' in df.columns:
        dept_data = df[df['部门'] == dept].groupby('年月')['金额'].sum()
    else:
        dept_data = df.groupby('年月')['金额'].sum()  # 默认处理
    dept_features[f'部门_{dept}_金额'] = dept_data

# 6. 计算年度预算特征
print("\n💰 计算年度预算特征...")
yearly_budget = df.groupby('年')['金额'].sum().to_dict()
print("年度预算统计:", yearly_budget)

# 7. 创建最终特征数据集
print("\n🔨 构建最终特征数据集...")
feature_df = base_df.copy()

# 合并月度统计特征
feature_df = feature_df.merge(monthly_summary, on='年月', how='left')

# 添加类别特征
for feature_name, feature_data in category_features.items():
    feature_df[feature_name] = feature_df['年月'].map(feature_data).fillna(0)

# 添加部门特征
for feature_name, feature_data in dept_features.items():
    feature_df[feature_name] = feature_df['年月'].map(feature_data).fillna(0)

# 添加年度预算特征
feature_df['年度预算'] = feature_df['年'].map(yearly_budget)
# 对于未来年份，使用历史平均值
avg_budget = np.mean(list(yearly_budget.values()))
feature_df['年度预算'] = feature_df['年度预算'].fillna(avg_budget)

# 8. 添加时间特征
print("\n⏰ 添加时间特征...")
feature_df['是否年初'] = (feature_df['月'] <= 3).astype(int)
feature_df['是否年中'] = ((feature_df['月'] >= 4) & (feature_df['月'] <= 9)).astype(int)
feature_df['是否年末'] = (feature_df['月'] >= 10).astype(int)
feature_df['是否上半年'] = (feature_df['月'] <= 6).astype(int)
feature_df['是否第一季度'] = (feature_df['季度'] == 1).astype(int)
feature_df['是否第二季度'] = (feature_df['季度'] == 2).astype(int)
feature_df['是否第三季度'] = (feature_df['季度'] == 3).astype(int)
feature_df['是否第四季度'] = (feature_df['季度'] == 4).astype(int)

# 添加月份的周期性特征
feature_df['月份_sin'] = np.sin(2 * np.pi * feature_df['月'] / 12)
feature_df['月份_cos'] = np.cos(2 * np.pi * feature_df['月'] / 12)

# ==================== 辅助函数定义 ====================
def calculate_trend_slope(values):
    """计算数值序列的线性趋势斜率"""
    if len(values) < 2 or values.isna().all():
        return 0
    valid_values = values.dropna()
    if len(valid_values) < 2:
        return 0
    x = np.arange(len(valid_values))
    try:
        slope, _ = np.polyfit(x, valid_values, 1)
        return slope
    except:
        return 0

def calculate_compound_growth_rate(values):
    """计算复合月增长率"""
    if len(values) < 2:
        return 0
    valid_values = values.dropna()
    if len(valid_values) < 2:
        return 0

    first_val = valid_values.iloc[0]
    last_val = valid_values.iloc[-1]

    if first_val <= 0:
        return 0

    periods = len(valid_values) - 1
    if periods <= 0:
        return 0

    try:
        compound_rate = (last_val / first_val) ** (1/periods) - 1
        return compound_rate
    except:
        return 0

def calculate_budget_deviation(actual_amount, budget_amount):
    """计算预算偏差"""
    if budget_amount <= 0:
        return 0
    return (actual_amount - budget_amount) / budget_amount

# 9. 按需求重新设计特征工程
print("\n📈 按需求计算特征...")
feature_df = feature_df.sort_values('日期')

# 首先确保有类别特征列
category_cols = [col for col in feature_df.columns if col.startswith('类别_') and col.endswith('_金额')]
print(f"发现类别特征: {category_cols}")

# 9.1 上个月特征
print("📊 计算上个月特征...")
feature_df['上月总金额'] = feature_df['月度总金额'].shift(1)

# 上个月各类别金额
for col in category_cols:
    new_col_name = col.replace('类别_', '上月类别_')
    feature_df[new_col_name] = feature_df[col].shift(1)

# 上个月累计预算偏差（需要先计算月度预算）
feature_df['月度预算'] = feature_df['年度预算'] / 12
feature_df['上月预算偏差'] = feature_df.apply(
    lambda row: calculate_budget_deviation(row['上月总金额'], row['月度预算']), axis=1
)

# 上个月复合月增长率（基于前3个月数据计算）
feature_df['上月复合增长率'] = feature_df['月度总金额'].rolling(window=3).apply(
    lambda x: calculate_compound_growth_rate(x), raw=False
).shift(1)

# 较上个月的增长率
feature_df['较上月增长率'] = feature_df['月度总金额'].pct_change()

# 9.2 上2个月特征
print("� 计算上2个月特征...")
feature_df['上2月总金额'] = feature_df['月度总金额'].shift(2)

# 上2个月各类别金额
for col in category_cols:
    new_col_name = col.replace('类别_', '上2月类别_')
    feature_df[new_col_name] = feature_df[col].shift(2)

# 上2个月累计预算偏差
feature_df['上2月预算偏差'] = feature_df.apply(
    lambda row: calculate_budget_deviation(row['上2月总金额'], row['月度预算']), axis=1
)

# 上2个月复合月增长率
feature_df['上2月复合增长率'] = feature_df['月度总金额'].rolling(window=3).apply(
    lambda x: calculate_compound_growth_rate(x), raw=False
).shift(2)

# 9.3 上3个月特征
print("📊 计算上3个月特征...")
feature_df['上3月总金额'] = feature_df['月度总金额'].shift(3)

# 上3个月各类别金额
for col in category_cols:
    new_col_name = col.replace('类别_', '上3月类别_')
    feature_df[new_col_name] = feature_df[col].shift(3)

# 上3个月累计预算偏差
feature_df['上3月预算偏差'] = feature_df.apply(
    lambda row: calculate_budget_deviation(row['上3月总金额'], row['月度预算']), axis=1
)

# 上3个月复合月增长率
feature_df['上3月复合增长率'] = feature_df['月度总金额'].rolling(window=3).apply(
    lambda x: calculate_compound_growth_rate(x), raw=False
).shift(3)

# 9.4 去年同期特征
print("📊 计算去年同期特征...")
feature_df['去年同期总金额'] = feature_df['月度总金额'].shift(12)

# 去年同期各类别金额
for col in category_cols:
    new_col_name = col.replace('类别_', '去年同期类别_')
    feature_df[new_col_name] = feature_df[col].shift(12)

# 较去年同期增长率
feature_df['较去年同期增长率'] = ((feature_df['月度总金额'] - feature_df['去年同期总金额']) /
                              (feature_df['去年同期总金额'] + 1e-8))

# 9.5 斜率特征
print("📊 计算斜率特征...")
# 前3个月总金额的斜率
feature_df['前3月斜率'] = feature_df['月度总金额'].rolling(window=3).apply(
    lambda x: calculate_trend_slope(x), raw=False
)

# 前6个月总金额的斜率
feature_df['前6月斜率'] = feature_df['月度总金额'].rolling(window=6).apply(
    lambda x: calculate_trend_slope(x), raw=False
)

# 9.6 时间特征
print("📊 计算时间特征...")
feature_df['是否年初'] = (feature_df['月'] <= 3).astype(int)
feature_df['是否年中'] = ((feature_df['月'] >= 4) & (feature_df['月'] <= 9)).astype(int)
feature_df['是否年末'] = (feature_df['月'] >= 10).astype(int)
feature_df['是否上半年'] = (feature_df['月'] <= 6).astype(int)
feature_df['是否第一季度'] = (feature_df['季度'] == 1).astype(int)
feature_df['是否第二季度'] = (feature_df['季度'] == 2).astype(int)
feature_df['是否第三季度'] = (feature_df['季度'] == 3).astype(int)
feature_df['是否第四季度'] = (feature_df['季度'] == 4).astype(int)

# 9.7 平均值特征
print("📊 计算平均值特征...")
# 前3个月总金额平均值
feature_df['前3月平均值'] = feature_df['月度总金额'].rolling(window=3).mean()

# 去年同期三个月平均值
feature_df['去年同期3月平均值'] = feature_df['前3月平均值'].shift(12)

# 10. 填充缺失值
print("\n🔧 处理缺失值...")
# 数值型特征用0填充
numeric_cols = feature_df.select_dtypes(include=[np.number]).columns
feature_df[numeric_cols] = feature_df[numeric_cols].fillna(0)

# 11. 添加目标变量（下月金额，用于预测）
feature_df['下月预测目标'] = feature_df['月度总金额'].shift(-1)

# 12. 添加累积特征
print("\n📊 添加累积特征...")
feature_df['年内累积金额'] = feature_df.groupby('年')['月度总金额'].cumsum()
feature_df['年内累积占比'] = feature_df['年内累积金额'] / feature_df['年度预算']

# 13. 添加扩展的高级特征工程
print("\n🚀 添加扩展特征工程...")

# 13.1 季节性特征
print("🌸 计算季节性特征...")
# 基于历史数据计算每月的季节性指数
monthly_seasonal = feature_df[feature_df['月度总金额'] > 0].groupby('月')['月度总金额'].mean()
overall_mean = feature_df[feature_df['月度总金额'] > 0]['月度总金额'].mean()
seasonal_index = (monthly_seasonal / overall_mean).to_dict()
feature_df['季节性指数'] = feature_df['月'].map(seasonal_index).fillna(1.0)

# 13.2 波动性指标
print("📊 计算波动性指标...")
# 计算滚动标准差和变异系数
feature_df['3月滚动标准差'] = feature_df['月度总金额'].rolling(window=3).std()
feature_df['6月滚动标准差'] = feature_df['月度总金额'].rolling(window=6).std()
feature_df['变异系数'] = feature_df['3月滚动标准差'] / (feature_df['参考期平均金额'] + 1e-8)

# 计算极差（最大值-最小值）
feature_df['3月极差'] = (feature_df['月度总金额'].rolling(window=3).max() -
                      feature_df['月度总金额'].rolling(window=3).min())

# 13.3 同比/环比增长率特征
print("📈 计算增长率特征...")
# 同比增长率（与去年同月比较）
feature_df['同比增长率'] = ((feature_df['月度总金额'] - feature_df['去年同期金额']) /
                        (feature_df['去年同期金额'] + 1e-8))

# 季度环比增长率
feature_df['季度环比增长率'] = ((feature_df['月度总金额'] - feature_df['前3月总金额']) /
                           (feature_df['前3月总金额'] + 1e-8))

# 13.4 类别占比特征
print("🏷️ 计算类别占比特征...")
# 计算各类别在总费用中的占比
category_cols = [col for col in feature_df.columns if col.startswith('类别_') and col.endswith('_金额')]
total_category_amount = feature_df[category_cols].sum(axis=1)

for col in category_cols:
    ratio_col = col.replace('_金额', '_占比')
    feature_df[ratio_col] = feature_df[col] / (total_category_amount + 1e-8)

# 计算类别集中度（赫芬达尔指数）
category_ratios = [col for col in feature_df.columns if col.startswith('类别_') and col.endswith('_占比')]
feature_df['类别集中度'] = sum(feature_df[col] ** 2 for col in category_ratios)

# 13.5 异常值检测特征
print("🔍 计算异常值检测特征...")
# 基于Z-score的异常值检测
historical_data = feature_df[feature_df['月度总金额'] > 0]['月度总金额']
if len(historical_data) > 0:
    mean_val = historical_data.mean()
    std_val = historical_data.std()
    feature_df['Z_score'] = (feature_df['月度总金额'] - mean_val) / (std_val + 1e-8)
    feature_df['是否异常值'] = (np.abs(feature_df['Z_score']) > 2).astype(int)
else:
    feature_df['Z_score'] = 0
    feature_df['是否异常值'] = 0

# 基于IQR的异常值检测
if len(historical_data) > 0:
    Q1 = historical_data.quantile(0.25)
    Q3 = historical_data.quantile(0.75)
    IQR = Q3 - Q1
    lower_bound = Q1 - 1.5 * IQR
    upper_bound = Q3 + 1.5 * IQR
    feature_df['IQR异常值'] = ((feature_df['月度总金额'] < lower_bound) |
                             (feature_df['月度总金额'] > upper_bound)).astype(int)
else:
    feature_df['IQR异常值'] = 0

# 13. 输出结果
print("\n💾 保存特征工程结果...")
output_file = '办公费用特征工程数据.csv'
feature_df.to_csv(output_file, index=False, encoding='utf-8-sig')

print(f"✅ 特征工程完成！")
print(f"📊 最终数据形状: {feature_df.shape}")
print(f"📁 输出文件: {output_file}")

# 15. 显示特征统计
print("\n📋 特征统计信息:")
print("=" * 60)
print(f"时间范围: {feature_df['年月'].min()} 到 {feature_df['年月'].max()}")
print(f"总特征数: {len(feature_df.columns)}")
print(f"类别特征数: {len([col for col in feature_df.columns if '类别_' in col])}")
print(f"部门特征数: {len([col for col in feature_df.columns if '部门_' in col])}")

# 新增特征统计
print(f"动态窗口特征数: {len([col for col in feature_df.columns if '参考期' in col])}")
print(f"累计费用特征数: {len([col for col in feature_df.columns if '累计' in col])}")
print(f"季节性特征数: {len([col for col in feature_df.columns if '季节' in col or '波动' in col])}")
print(f"增长率特征数: {len([col for col in feature_df.columns if '增长率' in col])}")
print(f"异常值特征数: {len([col for col in feature_df.columns if '异常' in col or 'Z_score' in col])}")

print(f"\n🎯 预测配置:")
print(f"- 目标预测月份: {PREDICTION_CONFIG['target_month']}月")
print(f"- 历史数据窗口: {PREDICTION_CONFIG['history_window']}个月")
print(f"- 参考月份: {PREDICTION_CONFIG['reference_months']}")

print("\n前5行数据预览:")
print(feature_df.head())

print("\n📊 特征分类列表:")
print("=" * 60)

# 按类别显示特征
feature_categories = {
    '基础时间特征': [col for col in feature_df.columns if col in ['年月', '年', '月', '季度', '日期']],
    '基础统计特征': [col for col in feature_df.columns if any(x in col for x in ['月度总金额', '月度平均金额', '月度交易次数', '月度标准差', '月度类别数量'])],
    '类别金额特征': [col for col in feature_df.columns if col.startswith('类别_') and col.endswith('_金额')],
    '类别占比特征': [col for col in feature_df.columns if col.startswith('类别_') and col.endswith('_占比')],
    '部门特征': [col for col in feature_df.columns if col.startswith('部门_')],
    '时间周期特征': [col for col in feature_df.columns if any(x in col for x in ['是否', '月份_sin', '月份_cos'])],
    '滞后特征': [col for col in feature_df.columns if any(x in col for x in ['上月', '前', '去年'])],
    '动态窗口特征': [col for col in feature_df.columns if '参考期' in col],
    '累计费用特征': [col for col in feature_df.columns if '累计' in col],
    '趋势特征': [col for col in feature_df.columns if any(x in col for x in ['移动平均', '增长率', '斜率'])],
    '季节性特征': [col for col in feature_df.columns if '季节' in col],
    '波动性特征': [col for col in feature_df.columns if any(x in col for x in ['标准差', '变异系数', '极差'])],
    '异常值特征': [col for col in feature_df.columns if any(x in col for x in ['异常', 'Z_score'])],
    '其他特征': [col for col in feature_df.columns if any(x in col for x in ['年度预算', '集中度', '下月预测目标'])]
}

for category, features in feature_categories.items():
    if features:
        print(f"\n{category} ({len(features)}个):")
        for i, feature in enumerate(features, 1):
            print(f"  {i:2d}. {feature}")

print(f"\n📈 数据统计:")
print(f"- 历史数据月份数: {len(feature_df[feature_df['月度总金额'] > 0])}")
print(f"- 预测月份数: {len(feature_df[feature_df['月度总金额'] == 0])}")
print(f"- 平均月度金额: {feature_df[feature_df['月度总金额'] > 0]['月度总金额'].mean():.2f}")
print(f"- 最高月度金额: {feature_df['月度总金额'].max():.2f}")

# 显示预测6月的特征示例
june_data = feature_df[feature_df['月'] == PREDICTION_CONFIG['target_month']]
if len(june_data) > 0:
    print(f"\n🎯 {PREDICTION_CONFIG['target_month']}月预测特征示例:")
    sample_june = june_data.iloc[0]
    key_features = ['参考期平均金额', '参考期总金额', '参考期变化斜率',
                   '目标月前累计费用', '参考期累计变化斜率', '季节性指数']
    for feature in key_features:
        if feature in sample_june:
            print(f"- {feature}: {sample_june[feature]:.2f}")

print(f"\n✅ 优化后的特征工程完成！新增了 {len(feature_df.columns) - 50} 个高级特征")