-- =====================================================
-- 办公费用特征工程数据库脚本（修正版）
-- 生成时间: 2025-07-28
-- 数据来源: 新特征工程数据1.csv
-- 说明: 修正了列数匹配问题的完整特征工程数据
-- =====================================================

-- 删除已存在的表（如果存在）
DROP TABLE IF EXISTS `office_expense_features`;

-- 创建办公费用特征工程数据表
CREATE TABLE `office_expense_features` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
  `year_month` VARCHAR(10) NOT NULL COMMENT '年月标识，格式如Jan-22',
  `year` INT NOT NULL COMMENT '年份',
  `month` INT NOT NULL COMMENT '月份（1-12）',
  `quarter` INT NOT NULL COMMENT '季度（1-4）',
  `date` DATE NOT NULL COMMENT '日期（每月1号）',
  
  -- 基础金额特征
  `monthly_total_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '月度总金额',
  `monthly_avg_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '月度平均金额',
  `monthly_transaction_count` INT DEFAULT 0 COMMENT '月度交易次数',
  `monthly_amount_std` DECIMAL(15,2) DEFAULT 0 COMMENT '月度金额标准差',
  
  -- 类别金额特征
  `category_slash_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '类别_/_金额',
  `category_other_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '类别_其他_金额',
  `category_office_supplies_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '类别_办公用品_金额',
  `category_printing_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '类别_印刷费_金额',
  `category_computer_consumables_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '类别_计算机耗材_金额',
  `category_postage_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '类别_邮寄费_金额',
  
  -- 预算相关特征
  `annual_budget` DECIMAL(15,2) DEFAULT 0 COMMENT '年度预算',
  `cumulative_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '累计金额',
  
  -- 上月特征组
  `last_month_total_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '上月总金额',
  `last_month_slash_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '上月_/_金额',
  `last_month_other_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '上月_其他_金额',
  `last_month_office_supplies_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '上月_办公用品_金额',
  `last_month_printing_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '上月_印刷费_金额',
  `last_month_computer_consumables_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '上月_计算机耗材_金额',
  `last_month_postage_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '上月_邮寄费_金额',
  `last_month_budget_deviation` DECIMAL(10,6) DEFAULT 0 COMMENT '上月预算偏差',
  `last_month_compound_growth_rate` DECIMAL(10,6) DEFAULT 0 COMMENT '上月复合增长率',
  `month_over_month_growth_rate` DECIMAL(10,6) DEFAULT 0 COMMENT '较上月增长率',
  
  -- 上2月特征组
  `last_2month_total_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '上2月总金额',
  `last_2month_slash_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '上2月_/_金额',
  `last_2month_other_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '上2月_其他_金额',
  `last_2month_office_supplies_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '上2月_办公用品_金额',
  `last_2month_printing_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '上2月_印刷费_金额',
  `last_2month_computer_consumables_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '上2月_计算机耗材_金额',
  `last_2month_postage_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '上2月_邮寄费_金额',
  `last_2month_budget_deviation` DECIMAL(10,6) DEFAULT 0 COMMENT '上2月预算偏差',
  `last_2month_compound_growth_rate` DECIMAL(10,6) DEFAULT 0 COMMENT '上2月复合增长率',
  
  -- 上3月特征组
  `last_3month_total_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '上3月总金额',
  `last_3month_slash_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '上3月_/_金额',
  `last_3month_other_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '上3月_其他_金额',
  `last_3month_office_supplies_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '上3月_办公用品_金额',
  `last_3month_printing_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '上3月_印刷费_金额',
  `last_3month_computer_consumables_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '上3月_计算机耗材_金额',
  `last_3month_postage_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '上3月_邮寄费_金额',
  `last_3month_budget_deviation` DECIMAL(10,6) DEFAULT 0 COMMENT '上3月预算偏差',
  `last_3month_compound_growth_rate` DECIMAL(10,6) DEFAULT 0 COMMENT '上3月复合增长率',
  
  -- 去年同期特征组
  `last_year_same_period_total_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '去年同期总金额',
  `last_year_same_period_slash_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '去年同期_/_金额',
  `last_year_same_period_other_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '去年同期_其他_金额',
  `last_year_same_period_office_supplies_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '去年同期_办公用品_金额',
  `last_year_same_period_printing_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '去年同期_印刷费_金额',
  `last_year_same_period_computer_consumables_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '去年同期_计算机耗材_金额',
  `last_year_same_period_postage_amount` DECIMAL(15,2) DEFAULT 0 COMMENT '去年同期_邮寄费_金额',
  `year_over_year_growth_rate` DECIMAL(10,6) DEFAULT 0 COMMENT '较去年同期增长率',
  
  -- 趋势特征
  `last_3month_slope` DECIMAL(15,6) DEFAULT 0 COMMENT '前3月斜率',
  `last_6month_slope` DECIMAL(15,6) DEFAULT 0 COMMENT '前6月斜率',
  
  -- 时间特征
  `is_early_year` TINYINT(1) DEFAULT 0 COMMENT '是否年初（1-3月）',
  `is_mid_year` TINYINT(1) DEFAULT 0 COMMENT '是否年中（4-9月）',
  `is_late_year` TINYINT(1) DEFAULT 0 COMMENT '是否年末（10-12月）',
  `is_first_half` TINYINT(1) DEFAULT 0 COMMENT '是否上半年（1-6月）',
  `is_q1` TINYINT(1) DEFAULT 0 COMMENT '是否第一季度',
  `is_q2` TINYINT(1) DEFAULT 0 COMMENT '是否第二季度',
  `is_q3` TINYINT(1) DEFAULT 0 COMMENT '是否第三季度',
  `is_q4` TINYINT(1) DEFAULT 0 COMMENT '是否第四季度',
  
  -- 平均值特征
  `last_3month_avg` DECIMAL(15,2) DEFAULT 0 COMMENT '前3月平均值',
  `last_year_same_3month_avg` DECIMAL(15,2) DEFAULT 0 COMMENT '去年同期3月平均值',
  
  -- 索引
  UNIQUE KEY `uk_year_month` (`year_month`),
  KEY `idx_year_month_date` (`year`, `month`, `date`),
  KEY `idx_date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='办公费用特征工程数据表';

-- 插入数据（注意：id字段为AUTO_INCREMENT，不需要在INSERT中指定）
-- 总共65个字段（不包括自增id）
INSERT INTO `office_expense_features` (
  `year_month`, `year`, `month`, `quarter`, `date`,
  `monthly_total_amount`, `monthly_avg_amount`, `monthly_transaction_count`, `monthly_amount_std`,
  `category_slash_amount`, `category_other_amount`, `category_office_supplies_amount`, 
  `category_printing_amount`, `category_computer_consumables_amount`, `category_postage_amount`,
  `annual_budget`, `cumulative_amount`,
  `last_month_total_amount`, `last_month_slash_amount`, `last_month_other_amount`, 
  `last_month_office_supplies_amount`, `last_month_printing_amount`, 
  `last_month_computer_consumables_amount`, `last_month_postage_amount`,
  `last_month_budget_deviation`, `last_month_compound_growth_rate`, `month_over_month_growth_rate`,
  `last_2month_total_amount`, `last_2month_slash_amount`, `last_2month_other_amount`,
  `last_2month_office_supplies_amount`, `last_2month_printing_amount`,
  `last_2month_computer_consumables_amount`, `last_2month_postage_amount`,
  `last_2month_budget_deviation`, `last_2month_compound_growth_rate`,
  `last_3month_total_amount`, `last_3month_slash_amount`, `last_3month_other_amount`,
  `last_3month_office_supplies_amount`, `last_3month_printing_amount`,
  `last_3month_computer_consumables_amount`, `last_3month_postage_amount`,
  `last_3month_budget_deviation`, `last_3month_compound_growth_rate`,
  `last_year_same_period_total_amount`, `last_year_same_period_slash_amount`, 
  `last_year_same_period_other_amount`, `last_year_same_period_office_supplies_amount`,
  `last_year_same_period_printing_amount`, `last_year_same_period_computer_consumables_amount`,
  `last_year_same_period_postage_amount`, `year_over_year_growth_rate`,
  `last_3month_slope`, `last_6month_slope`,
  `is_early_year`, `is_mid_year`, `is_late_year`, `is_first_half`,
  `is_q1`, `is_q2`, `is_q3`, `is_q4`,
  `last_3month_avg`, `last_year_same_3month_avg`
) VALUES
-- 2022年数据
('Jan-22', 2022, 1, 1, '2022-01-01', 147810.96, 6718.68, 22, 10475.58, 0, 60784.46, 72969.5, 12981, 0, 1076, 1370388.28, 147810.96, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0),
('Feb-22', 2022, 2, 1, '2022-02-01', 93613.4, 6240.89, 15, 11671.61, 0, 1099, 79611.4, 12145, 0, 758, 1370388.28, 241424.36, 147810.96, 0, 60784.46, 72969.5, 12981, 0, 1076, 0.209595, 0, -0.366668, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0);

-- =====================================================
-- 数据验证查询
-- =====================================================

-- 验证数据插入结果
SELECT
    COUNT(*) as total_records,
    MIN(date) as earliest_date,
    MAX(date) as latest_date,
    AVG(monthly_total_amount) as avg_monthly_amount
FROM office_expense_features;

-- 显示表结构
DESCRIBE office_expense_features;

-- 显示前5条记录
SELECT year_month, monthly_total_amount, month_over_month_growth_rate
FROM office_expense_features
ORDER BY date
LIMIT 5;
