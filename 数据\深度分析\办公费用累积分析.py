# 办公费用累积分析
# 统计每个类别费用的累积情况

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# 显示中文和负号
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

print("📊 开始分析办公费用累积情况...")

# 1. 读取数据
print("📂 读取数据...")
df = pd.read_csv('办公费用（剔除专项）.csv', encoding='utf-8')
print(f"原始数据形状: {df.shape}")

# 2. 数据清洗
print("\n🧹 数据清洗...")
# 处理金额字段，去掉千分位逗号
df['金额'] = df['金额'].astype(str).str.replace(',', '').astype(float)
# 删除全空列
df = df.dropna(axis=1, how='all')
# 处理类别中的不一致
df['类别'] = df['类别'].replace('邮寄', '邮寄费')
# 生成年月字段
df['年月'] = df['年'].astype(str) + '-' + df['月'].astype(str).str.zfill(2)

# 3. 按类别、年份和月份统计每月费用
print("\n📈 按类别、年份和月份统计每月费用...")
monthly_expenses = df.groupby(['类别', '年', '月', '年月'])['金额'].sum().reset_index()
print("每月各类别费用统计：")
print(monthly_expenses.head(10))

# 4. 按年份分组计算累积费用
print("\n💰 按年份分组计算累积费用...")
# 按类别和年份分组，然后按月份排序计算累积值
cumulative_expenses = monthly_expenses.groupby(['类别', '年']).apply(
    lambda x: x.sort_values('月').assign(累积金额=x['金额'].cumsum())
).reset_index(drop=True)

print("各类别累积费用统计：")
print(cumulative_expenses.head(15))

# 5. 创建透视表，便于查看
print("\n📋 创建累积费用透视表...")
cumulative_pivot = cumulative_expenses.pivot_table(
    index='年月', 
    columns='类别', 
    values='累积金额', 
    aggfunc='sum'
).fillna(method='ffill').fillna(0)

print("累积费用透视表（前10行）：")
print(cumulative_pivot.head(10))

# 6. 可视化累积费用趋势
print("\n📊 生成累积费用趋势图...")
plt.figure(figsize=(16, 10))

# 创建子图
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))

# 子图1：每月费用
monthly_pivot = monthly_expenses.pivot_table(
    index='年月', 
    columns='类别', 
    values='金额', 
    aggfunc='sum'
).fillna(0)

monthly_pivot.plot(kind='bar', ax=ax1, width=0.8)
ax1.set_title('📅 每月各类别费用', fontsize=14, fontweight='bold')
ax1.set_xlabel('年月')
ax1.set_ylabel('金额（元）')
ax1.legend(title='费用类别', bbox_to_anchor=(1.05, 1), loc='upper left')
ax1.tick_params(axis='x', rotation=45)

# 子图2：累积费用
cumulative_pivot.plot(kind='line', ax=ax2, marker='o', linewidth=2, markersize=4)
ax2.set_title('💰 各类别按年累积费用趋势（每年重新开始）', fontsize=14, fontweight='bold')
ax2.set_xlabel('年月')
ax2.set_ylabel('累积金额（元）')
ax2.legend(title='费用类别', bbox_to_anchor=(1.05, 1), loc='upper left')
ax2.tick_params(axis='x', rotation=45)
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# 7. 按年份统计各类别总累积费用
print("\n📊 按年份统计各类别总累积费用：")
yearly_cumulative = cumulative_expenses.groupby(['类别', '年']).agg({
    '金额': ['sum', 'mean', 'count'],
    '累积金额': 'max'
}).round(2)

yearly_cumulative.columns = ['年总费用', '平均月费用', '月数', '年末累积金额']
yearly_cumulative = yearly_cumulative.reset_index()
print(yearly_cumulative)

# 8. 导出结果到CSV
print("\n💾 导出结果...")
cumulative_expenses.to_csv('办公费用按年累积分析结果.csv', index=False, encoding='utf-8-sig')
cumulative_pivot.to_csv('办公费用按年累积透视表.csv', encoding='utf-8-sig')
yearly_cumulative.to_csv('办公费用按年统计汇总.csv', index=False, encoding='utf-8-sig')
print("✅ 结果已导出到CSV文件")

# 9. 生成详细分析报告
print("\n📋 生成详细分析报告...")
print("=" * 60)
print("📊 办公费用按年累积分析报告")
print("=" * 60)

# 统计每年的情况
years = sorted(cumulative_expenses['年'].unique())
print(f"\n📅 分析年份: {years}")

# 找出每年增长最快的类别
for year in years:
    year_data = cumulative_expenses[cumulative_expenses['年'] == year]
    categories = year_data['类别'].unique()
    
    print(f"\n📈 {year}年各类别累积费用情况：")
    for category in categories:
        cat_data = year_data[year_data['类别'] == category]
        if len(cat_data) > 0:
            max_cumulative = cat_data['累积金额'].max()
            total_amount = cat_data['金额'].sum()
            print(f"  - {category}: 年末累积 {max_cumulative:,.2f}元, 年总计 {total_amount:,.2f}元")

print(f"\n📊 数据统计：")
print(f"- 涉及类别数量: {len(cumulative_expenses['类别'].unique())}")
print(f"- 总记录数: {len(cumulative_expenses)}")
print(f"- 每年重新开始计算累积费用")

print("\n✅ 办公费用按年累积分析完成！") 