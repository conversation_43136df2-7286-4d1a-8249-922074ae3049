#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
办公费用新特征工程脚本
按照具体需求设计的特征计算
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

print("🔧 开始新特征工程计算...")

# ==================== 辅助函数 ====================
def calculate_trend_slope(values):
    """计算数值序列的线性趋势斜率"""
    if len(values) < 2 or values.isna().all():
        return 0
    valid_values = values.dropna()
    if len(valid_values) < 2:
        return 0
    x = np.arange(len(valid_values))
    try:
        slope, _ = np.polyfit(x, valid_values, 1)
        return slope
    except:
        return 0

def calculate_compound_growth_rate(values):
    """计算复合月增长率"""
    if len(values) < 2:
        return 0
    valid_values = values.dropna()
    if len(valid_values) < 2:
        return 0
    
    first_val = valid_values.iloc[0]
    last_val = valid_values.iloc[-1]
    
    if first_val <= 0:
        return 0
    
    periods = len(valid_values) - 1
    if periods <= 0:
        return 0
    
    try:
        compound_rate = (last_val / first_val) ** (1/periods) - 1
        return compound_rate
    except:
        return 0

def calculate_budget_deviation(actual_amount, budget_amount):
    """计算预算偏差"""
    if budget_amount <= 0:
        return 0
    return (actual_amount - budget_amount) / budget_amount

# ==================== 读取数据 ====================
print("📂 读取基础数据...")
try:
    df = pd.read_csv('办公费用.csv', encoding='utf-8')
    print(f"✅ 数据读取成功: {df.shape}")
except Exception as e:
    print(f"❌ 数据读取失败: {e}")
    exit()

# ==================== 数据预处理 ====================
print("🧹 数据预处理...")
# 处理金额字段
df['金额'] = df['金额'].astype(str).str.replace(',', '').astype(float)
# 处理类别统一
if '类别' in df.columns:
    df['类别'] = df['类别'].replace('邮寄', '邮寄费')
else:
    df['类别'] = '默认类别'

# 生成标准化日期字段
df['年月'] = df['年'].astype(str) + '-' + df['月'].astype(str).str.zfill(2)
df['日期'] = pd.to_datetime(df['年月'] + '-01')

print(f"清洗后数据形状: {df.shape}")

# ==================== 按月汇总基础数据 ====================
print("📊 按月汇总数据...")
# 按月汇总总金额
monthly_summary = df.groupby('年月').agg({
    '金额': ['sum', 'mean', 'count', 'std']
}).round(2)
monthly_summary.columns = ['月度总金额', '月度平均金额', '月度交易次数', '月度金额标准差']

# 按月按类别汇总
category_pivot = df.groupby(['年月', '类别'])['金额'].sum().unstack(fill_value=0)
category_pivot.columns = [f'类别_{col}_金额' for col in category_pivot.columns]

# 合并数据
monthly_data = monthly_summary.join(category_pivot, how='left').fillna(0)
monthly_data = monthly_data.reset_index()

print(f"月度汇总数据形状: {monthly_data.shape}")
print("类别特征:", [col for col in monthly_data.columns if '类别_' in col])

# ==================== 创建完整时间序列 ====================
print("📅 创建完整时间序列...")
start_date = monthly_data['年月'].min()
end_date = '2024-12'  # 扩展到2024年12月
date_range = pd.date_range(start=pd.to_datetime(start_date + '-01'), 
                          end=pd.to_datetime(end_date + '-01'), 
                          freq='MS')

# 创建基础时间框架
base_df = pd.DataFrame({
    '年月': date_range.strftime('%Y-%m'),
    '年': date_range.year,
    '月': date_range.month,
    '季度': date_range.quarter,
    '日期': date_range
})

# 合并月度数据
feature_df = base_df.merge(monthly_data, on='年月', how='left')
feature_df = feature_df.fillna(0)

# 添加年度预算（基于历史数据计算）
yearly_budget = monthly_data.groupby(monthly_data['年月'].str[:4])['月度总金额'].sum().to_dict()
avg_budget = np.mean(list(yearly_budget.values())) if yearly_budget else 1000000
feature_df['年度预算'] = feature_df['年'].map(yearly_budget).fillna(avg_budget)
feature_df['月度预算'] = feature_df['年度预算'] / 12

print(f"完整特征数据形状: {feature_df.shape}")

# ==================== 按需求计算特征 ====================
print("\n🎯 按需求计算特征...")
feature_df = feature_df.sort_values('日期')

# 获取类别特征列
category_cols = [col for col in feature_df.columns if col.startswith('类别_') and col.endswith('_金额')]
print(f"发现类别特征: {len(category_cols)}个")

# 1. 上个月特征
print("📊 1. 计算上个月特征...")
feature_df['上月总金额'] = feature_df['月度总金额'].shift(1)

# 上个月各类别金额
for col in category_cols:
    new_col_name = col.replace('类别_', '上月_')
    feature_df[new_col_name] = feature_df[col].shift(1)

# 上个月累计预算偏差
feature_df['上月预算偏差'] = feature_df.apply(
    lambda row: calculate_budget_deviation(row['上月总金额'], row['月度预算']), axis=1
)

# 上个月复合月增长率
feature_df['上月复合增长率'] = feature_df['月度总金额'].rolling(window=3).apply(
    lambda x: calculate_compound_growth_rate(x), raw=False
).shift(1)

# 较上个月的增长率
feature_df['较上月增长率'] = feature_df['月度总金额'].pct_change()

# 2. 上2个月特征
print("📊 2. 计算上2个月特征...")
feature_df['上2月总金额'] = feature_df['月度总金额'].shift(2)

# 上2个月各类别金额
for col in category_cols:
    new_col_name = col.replace('类别_', '上2月_')
    feature_df[new_col_name] = feature_df[col].shift(2)

# 上2个月累计预算偏差
feature_df['上2月预算偏差'] = feature_df.apply(
    lambda row: calculate_budget_deviation(row['上2月总金额'], row['月度预算']), axis=1
)

# 上2个月复合月增长率
feature_df['上2月复合增长率'] = feature_df['月度总金额'].rolling(window=3).apply(
    lambda x: calculate_compound_growth_rate(x), raw=False
).shift(2)

# 3. 上3个月特征
print("📊 3. 计算上3个月特征...")
feature_df['上3月总金额'] = feature_df['月度总金额'].shift(3)

# 上3个月各类别金额
for col in category_cols:
    new_col_name = col.replace('类别_', '上3月_')
    feature_df[new_col_name] = feature_df[col].shift(3)

# 上3个月累计预算偏差
feature_df['上3月预算偏差'] = feature_df.apply(
    lambda row: calculate_budget_deviation(row['上3月总金额'], row['月度预算']), axis=1
)

# 上3个月复合月增长率
feature_df['上3月复合增长率'] = feature_df['月度总金额'].rolling(window=3).apply(
    lambda x: calculate_compound_growth_rate(x), raw=False
).shift(3)

# 4. 去年同期特征
print("📊 4. 计算去年同期特征...")
feature_df['去年同期总金额'] = feature_df['月度总金额'].shift(12)

# 去年同期各类别金额
for col in category_cols:
    new_col_name = col.replace('类别_', '去年同期_')
    feature_df[new_col_name] = feature_df[col].shift(12)

# 较去年同期增长率
feature_df['较去年同期增长率'] = ((feature_df['月度总金额'] - feature_df['去年同期总金额']) / 
                              (feature_df['去年同期总金额'] + 1e-8))

# 5. 斜率特征
print("📊 5. 计算斜率特征...")
# 前3个月总金额的斜率
feature_df['前3月斜率'] = feature_df['月度总金额'].rolling(window=3).apply(
    lambda x: calculate_trend_slope(x), raw=False
)

# 前6个月总金额的斜率
feature_df['前6月斜率'] = feature_df['月度总金额'].rolling(window=6).apply(
    lambda x: calculate_trend_slope(x), raw=False
)

# 6. 时间特征
print("📊 6. 计算时间特征...")
feature_df['是否年初'] = (feature_df['月'] <= 3).astype(int)
feature_df['是否年中'] = ((feature_df['月'] >= 4) & (feature_df['月'] <= 9)).astype(int)
feature_df['是否年末'] = (feature_df['月'] >= 10).astype(int)
feature_df['是否上半年'] = (feature_df['月'] <= 6).astype(int)
feature_df['是否第一季度'] = (feature_df['季度'] == 1).astype(int)
feature_df['是否第二季度'] = (feature_df['季度'] == 2).astype(int)
feature_df['是否第三季度'] = (feature_df['季度'] == 3).astype(int)
feature_df['是否第四季度'] = (feature_df['季度'] == 4).astype(int)

# 7. 平均值特征
print("📊 7. 计算平均值特征...")
# 前3个月总金额平均值
feature_df['前3月平均值'] = feature_df['月度总金额'].rolling(window=3).mean()

# 去年同期三个月平均值
feature_df['去年同期3月平均值'] = feature_df['前3月平均值'].shift(12)

# ==================== 处理缺失值 ====================
print("\n🔧 处理缺失值...")
numeric_cols = feature_df.select_dtypes(include=[np.number]).columns
feature_df[numeric_cols] = feature_df[numeric_cols].fillna(0)

# ==================== 输出结果 ====================
print("\n💾 保存新特征工程结果...")
output_file = '新特征工程数据.csv'
feature_df.to_csv(output_file, index=False, encoding='utf-8-sig')

print(f"✅ 新特征工程完成！")
print(f"📊 最终数据形状: {feature_df.shape}")
print(f"📁 输出文件: {output_file}")

# ==================== 特征统计 ====================
print("\n📋 新特征统计:")
print("=" * 60)

feature_categories = {
    '基础特征': ['年月', '年', '月', '季度', '月度总金额', '月度平均金额', '月度交易次数'],
    '上月特征': [col for col in feature_df.columns if '上月' in col],
    '上2月特征': [col for col in feature_df.columns if '上2月' in col],
    '上3月特征': [col for col in feature_df.columns if '上3月' in col],
    '去年同期特征': [col for col in feature_df.columns if '去年同期' in col],
    '斜率特征': [col for col in feature_df.columns if '斜率' in col],
    '时间特征': [col for col in feature_df.columns if '是否' in col],
    '平均值特征': [col for col in feature_df.columns if '平均值' in col],
    '增长率特征': [col for col in feature_df.columns if '增长率' in col],
    '预算特征': [col for col in feature_df.columns if '预算' in col]
}

total_features = 0
for category, features in feature_categories.items():
    if features:
        print(f"{category}: {len(features)}个")
        total_features += len(features)

print(f"\n总特征数: {total_features}")
print(f"数据时间范围: {feature_df['年月'].min()} 到 {feature_df['年月'].max()}")

print("\n前5行数据预览:")
print(feature_df[['年月', '月度总金额', '上月总金额', '较上月增长率', '前3月斜率']].head())

print("\n✅ 新特征工程脚本执行完成！")
