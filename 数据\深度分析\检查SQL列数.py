#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查SQL INSERT语句的列数和值数量是否匹配
"""

import re

def check_sql_column_count():
    """检查SQL列数匹配"""
    
    print("🔧 检查SQL列数匹配...")
    
    # 读取SQL文件
    with open('办公费用特征工程数据.sql', 'r', encoding='utf-8') as f:
        sql_content = f.read()
    
    # 1. 提取INSERT语句中的列定义
    insert_pattern = r'INSERT INTO.*?\((.*?)\) VALUES'
    insert_match = re.search(insert_pattern, sql_content, re.DOTALL)
    
    if insert_match:
        columns_text = insert_match.group(1)
        # 提取所有列名
        column_pattern = r'`([^`]+)`'
        columns = re.findall(column_pattern, columns_text)
        
        print(f"INSERT语句中的列数: {len(columns)}")
        print("列名列表:")
        for i, col in enumerate(columns, 1):
            print(f"  {i:2d}. {col}")
    
    # 2. 提取第一行VALUES数据
    values_pattern = r"VALUES\s*\n\('([^']+)'.*?\),"
    values_match = re.search(values_pattern, sql_content, re.DOTALL)
    
    if values_match:
        # 提取完整的第一行数据
        first_row_pattern = r"VALUES\s*\n\((.*?)\),"
        first_row_match = re.search(first_row_pattern, sql_content, re.DOTALL)
        
        if first_row_match:
            values_text = first_row_match.group(1)
            # 分割值，考虑字符串中的逗号
            values = []
            current_value = ""
            in_quotes = False
            
            for char in values_text:
                if char == "'" and not in_quotes:
                    in_quotes = True
                    current_value += char
                elif char == "'" and in_quotes:
                    in_quotes = False
                    current_value += char
                elif char == "," and not in_quotes:
                    values.append(current_value.strip())
                    current_value = ""
                else:
                    current_value += char
            
            # 添加最后一个值
            if current_value.strip():
                values.append(current_value.strip())
            
            print(f"\n第一行VALUES中的值数量: {len(values)}")
            print("前10个值:")
            for i, val in enumerate(values[:10], 1):
                print(f"  {i:2d}. {val}")
            
            # 检查匹配
            if len(columns) == len(values):
                print(f"\n✅ 列数匹配！列数={len(columns)}, 值数={len(values)}")
            else:
                print(f"\n❌ 列数不匹配！列数={len(columns)}, 值数={len(values)}")
                print(f"差异: {len(columns) - len(values)}")
                
                # 如果值比列多，显示多余的值
                if len(values) > len(columns):
                    print("多余的值:")
                    for i in range(len(columns), len(values)):
                        print(f"  {i+1}. {values[i]}")
                
                # 如果列比值多，显示缺失的列
                if len(columns) > len(values):
                    print("缺失值的列:")
                    for i in range(len(values), len(columns)):
                        print(f"  {i+1}. {columns[i]}")

if __name__ == "__main__":
    check_sql_column_count()
