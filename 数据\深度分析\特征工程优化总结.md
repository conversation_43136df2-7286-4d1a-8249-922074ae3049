# 办公费用特征工程优化总结

## 📋 优化概述

本次对 `办公费用特征工程_jupyter.py` 进行了全面的特征工程优化，主要针对预测6月办公费用的场景进行了专门的时间窗口调整和特征扩展。

## 🎯 核心优化内容

### 1. 时间窗口调整
**原有问题**: 使用固定的滞后特征，不适合特定预测场景
**优化方案**:
- 对于预测6月数据，只使用3月、4月、5月的历史数据作为特征
- 3月移动平均值调整为基于3-5月数据计算
- "12个月前"特征改为"去年同期6月"数据作为参考

**实现效果**:
```python
# 配置参数
PREDICTION_CONFIG = {
    'target_month': 6,  # 预测目标月份
    'history_window': 3,  # 历史数据窗口长度（月）
    'reference_months': [3, 4, 5]  # 参考月份（3月、4月、5月）
}
```

### 2. 新增累计费用特征
**新增特征**:
- `目标月前累计费用`: 计算6月之前的累计费用总额
- `参考期累计变化斜率`: 计算3-5月累计金额的线性趋势斜率

**技术实现**:
- 使用 `numpy.polyfit` 计算线性回归斜率
- 动态计算不同年份的累计费用模式

### 3. 动态窗口特征系统
**新增函数**: `calculate_dynamic_window_features()`
**新增特征**:
- `参考期平均金额`: 3-5月平均金额
- `参考期最大金额`: 3-5月最大金额  
- `参考期最小金额`: 3-5月最小金额
- `参考期总金额`: 3-5月总金额
- `参考期标准差`: 3-5月标准差
- `参考期变化斜率`: 3-5月变化趋势

### 4. 扩展特征工程

#### 4.1 季节性特征
- `季节性指数`: 基于历史数据计算每月的季节性模式
- 反映办公费用的季节性变化规律

#### 4.2 波动性指标
- `3月滚动标准差`: 3个月滚动标准差
- `6月滚动标准差`: 6个月滚动标准差
- `变异系数`: 标准差与均值的比值，衡量相对波动性
- `3月极差`: 3个月内最大值与最小值的差

#### 4.3 增长率特征
- `同比增长率`: 与去年同月的增长率
- `季度环比增长率`: 与前3个月的增长率
- `环比增长率`: 与上月的增长率

#### 4.4 类别占比特征
- 各类别在总费用中的占比
- `类别集中度`: 基于赫芬达尔指数的类别集中度
- 反映费用结构的集中程度

#### 4.5 异常值检测特征
- `Z_score`: 基于Z-score的异常值评分
- `是否异常值`: Z-score > 2 的异常值标识
- `IQR异常值`: 基于四分位距的异常值检测

## 🔧 技术改进

### 1. 模块化设计
- 将特征计算拆分为独立函数，提高代码可维护性
- `calculate_trend_slope()`: 计算线性趋势斜率
- `calculate_dynamic_window_features()`: 动态窗口特征计算
- `calculate_cumulative_features()`: 累计费用特征计算

### 2. 参数化配置
- 通过 `PREDICTION_CONFIG` 支持不同预测场景
- 可以轻松调整目标月份和历史窗口长度
- 支持自定义参考月份范围

### 3. 错误处理增强
- 增加了对scipy依赖的可选处理
- 改进了数据缺失情况的处理
- 增强了异常情况的容错能力

## 📊 数据验证示例

基于2022年数据的验证：

**2022年6月预测特征**:
- 参考期数据（3-5月）: 3月、4月、5月的费用数据
- 6月前累计费用: 1-5月的累计总额
- 参考期变化斜率: 3-5月的线性趋势
- 去年同期对比: 2021年6月数据（如有）

**特征数量对比**:
- 原有特征: ~50个
- 优化后特征: 80+个
- 新增高级特征: 30+个

## 🎯 预期效果

### 1. 预测精度提升
- 针对性的时间窗口提高了特征的相关性
- 累计费用特征捕捉了费用的累积模式
- 多维度特征提供了更全面的信息

### 2. 模型解释性增强
- 季节性特征帮助理解费用的周期性规律
- 波动性指标反映费用的稳定性
- 异常值检测有助于识别特殊情况

### 3. 业务价值提升
- 类别占比特征支持费用结构分析
- 增长率特征支持趋势分析
- 动态窗口特征适应不同预测场景

## 📁 相关文件

- **主要修改文件**: `办公费用特征工程_jupyter.py`
- **开发日志**: `dev-log.md`
- **测试脚本**: `verify_features.py`, `simple_test.py`
- **输出文件**: `办公费用特征工程数据.csv`

## 🚀 后续优化建议

1. **时间序列分解**: 可以考虑添加趋势、季节性、残差分解特征
2. **外部指标**: 引入经济指标、政策变化等外部特征
3. **自动特征工程**: 使用机器学习方法自动生成特征组合
4. **特征选择**: 实现特征重要性评估和自动选择机制
5. **实时更新**: 支持增量特征计算和实时更新

## ✅ 验证清单

- [x] 时间窗口调整：6月预测使用3-5月数据
- [x] 累计费用特征：计算6月前累计和变化斜率  
- [x] 动态窗口特征：基于参考月份的统计特征
- [x] 去年同期特征：使用去年6月数据作为参考
- [x] 扩展特征工程：季节性、波动性、增长率等
- [x] 代码模块化：函数化设计提高可维护性
- [x] 参数化配置：支持不同预测场景
- [x] 错误处理：增强容错能力

本次优化大幅提升了特征工程的质量和针对性，为办公费用预测模型提供了更丰富、更相关的特征输入。
