#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
办公费用特征工程脚本
功能：根据办公费用数据生成多维度特征用于预测分析
作者：AI助手
日期：2024
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')
from datetime import datetime, timedelta
import calendar
import holidays

class OfficeExpenseFeatureEngineering:
    """办公费用特征工程类"""
    
    def __init__(self, csv_file_path):
        """
        初始化特征工程类
        
        Args:
            csv_file_path (str): CSV文件路径
        """
        self.csv_file_path = csv_file_path
        self.df = None
        self.features_df = None
        
    def load_and_preprocess_data(self):
        """加载和预处理数据"""
        print("正在加载数据...")
        
        # 读取CSV文件
        self.df = pd.read_csv(self.csv_file_path, encoding='utf-8')
        
        # 打印列名用于调试
        print(f"原始列名: {list(self.df.columns)}")
        
        # 删除空的列
        self.df = self.df.dropna(axis=1, how='all')
        
        # 重命名列名（处理可能的命名问题）
        expected_columns = ['年', '月', '日', '部门', '摘要', '金额', '类别', '费用类型']
        if len(self.df.columns) >= len(expected_columns):
            # 如果列数超过预期，只取前面的列
            self.df = self.df.iloc[:, :len(expected_columns)]
            self.df.columns = expected_columns
        
        print(f"处理后列名: {list(self.df.columns)}")
        
        # 清理数据 - 移除年月日为空的行
        before_clean = len(self.df)
        self.df = self.df.dropna(subset=['年', '月', '日'])
        after_clean = len(self.df)
        if before_clean != after_clean:
            print(f"移除了 {before_clean - after_clean} 行缺失日期的数据")
        
        # 确保年月日为数值类型
        self.df['年'] = pd.to_numeric(self.df['年'], errors='coerce')
        self.df['月'] = pd.to_numeric(self.df['月'], errors='coerce')
        self.df['日'] = pd.to_numeric(self.df['日'], errors='coerce')
        
        # 再次清理转换失败的行
        self.df = self.df.dropna(subset=['年', '月', '日'])
        
        # 清理金额列（去除逗号和空格）
        self.df['金额'] = self.df['金额'].astype(str).str.replace(',', '').str.replace(' ', '')
        self.df['金额'] = pd.to_numeric(self.df['金额'], errors='coerce')
        
        # 移除金额为空的行
        self.df = self.df.dropna(subset=['金额'])
        
        # 创建日期列 - 使用更安全的方法
        try:
            self.df['日期'] = pd.to_datetime(self.df[['年', '月', '日']])
        except Exception as e:
            print(f"日期转换出错，尝试逐行处理: {e}")
            # 尝试逐行转换日期
            dates = []
            for idx, row in self.df.iterrows():
                try:
                    date = pd.to_datetime(f"{int(row['年'])}-{int(row['月'])}-{int(row['日'])}")
                    dates.append(date)
                except:
                    dates.append(pd.NaT)
            self.df['日期'] = dates
            # 移除日期转换失败的行
            self.df = self.df.dropna(subset=['日期'])
        
        # 按日期排序
        self.df = self.df.sort_values('日期').reset_index(drop=True)
        
        # 创建年月列
        self.df['年月'] = self.df['日期'].dt.strftime('%Y-%m')
        
        print(f"数据加载完成，共 {len(self.df)} 条记录")
        print(f"时间范围：{self.df['日期'].min()} 到 {self.df['日期'].max()}")
        print(f"数据预览：")
        print(self.df[['年', '月', '日', '金额', '类别']].head())
        
    def create_time_features(self):
        """创建时间类特征"""
        print("正在创建时间类特征...")
        
        # 按年月汇总数据
        monthly_summary = self.df.groupby(['年月', '年', '月']).agg({
            '金额': 'sum',
            '日期': 'first'
        }).reset_index()
        
        # 基础时间特征
        monthly_summary['季度'] = monthly_summary['月'].apply(lambda x: (x-1)//3 + 1)
        
        # 季节特征
        def get_season(month):
            if month in [3, 4, 5]:
                return '春'
            elif month in [6, 7, 8]:
                return '夏'
            elif month in [9, 10, 11]:
                return '秋'
            else:
                return '冬'
        
        monthly_summary['季节'] = monthly_summary['月'].apply(get_season)
        
        # 工作日数量
        def get_workdays(year, month):
            # 获取中国节假日
            china_holidays = holidays.China(years=year)
            
            # 计算该月的工作日数
            start_date = datetime(year, month, 1)
            if month == 12:
                end_date = datetime(year + 1, 1, 1) - timedelta(days=1)
            else:
                end_date = datetime(year, month + 1, 1) - timedelta(days=1)
            
            workdays = 0
            current_date = start_date
            while current_date <= end_date:
                # 检查是否为工作日（不是周末且不是节假日）
                if current_date.weekday() < 5 and current_date not in china_holidays:
                    workdays += 1
                current_date += timedelta(days=1)
            
            return workdays
        
        monthly_summary['当月工作日数'] = monthly_summary.apply(
            lambda row: get_workdays(int(row['年']), int(row['月'])), axis=1
        )
        
        self.features_df = monthly_summary.copy()
        
    def create_historical_features(self):
        """创建历史费用特征"""
        print("正在创建历史费用特征...")
        
        # 确保数据按时间排序
        self.features_df = self.features_df.sort_values('日期').reset_index(drop=True)
        
        # 滞后特征
        for lag in [1, 2, 3]:
            self.features_df[f'费用_lag{lag}'] = self.features_df['金额'].shift(lag)
        
        # 移动平均特征
        for window in [3, 6, 12]:
            self.features_df[f'费用_ma{window}'] = self.features_df['金额'].rolling(window=window, min_periods=1).mean()
        
        # 上月费用
        self.features_df['上月费用'] = self.features_df['金额'].shift(1)
        
        # 上季度费用（3个月前）
        self.features_df['上季度费用'] = self.features_df['金额'].shift(3)
        
        # 去年同期费用（12个月前）
        self.features_df['去年同期费用'] = self.features_df['金额'].shift(12)
        
        # 同比增长率
        self.features_df['费用同比增长率'] = (
            (self.features_df['金额'] - self.features_df['去年同期费用']) / 
            self.features_df['去年同期费用'].replace(0, np.nan) * 100
        )
        
        # 环比增长率
        self.features_df['费用环比增长率'] = (
            (self.features_df['金额'] - self.features_df['上月费用']) / 
            self.features_df['上月费用'].replace(0, np.nan) * 100
        )
        
        # 增减额
        self.features_df['月环比增减额'] = self.features_df['金额'] - self.features_df['上月费用']
        self.features_df['年同比增减额'] = self.features_df['金额'] - self.features_df['去年同期费用']
        
        # 统计量特征（过去12个月）
        for window in [6, 12]:
            self.features_df[f'费用_max_{window}m'] = self.features_df['金额'].rolling(window=window, min_periods=1).max()
            self.features_df[f'费用_min_{window}m'] = self.features_df['金额'].rolling(window=window, min_periods=1).min()
            self.features_df[f'费用_median_{window}m'] = self.features_df['金额'].rolling(window=window, min_periods=1).median()
            self.features_df[f'费用_std_{window}m'] = self.features_df['金额'].rolling(window=window, min_periods=1).std()
        
        # 分位数特征
        for q in [0.25, 0.75]:
            self.features_df[f'费用_q{int(q*100)}_12m'] = self.features_df['金额'].rolling(window=12, min_periods=1).quantile(q)
    
    def create_category_historical_features(self):
        """创建各类别的历史费用特征"""
        print("正在创建费用类别历史特征...")
        
        # 按月汇总各类别费用
        category_monthly = self.df.groupby(['年月', '类别'])['金额'].sum().unstack(fill_value=0)
        category_monthly.index = pd.to_datetime(category_monthly.index)
        category_monthly = category_monthly.sort_index()
        
        # 重建索引以确保与主数据框匹配
        main_dates = pd.to_datetime(self.features_df['年月'])
        category_monthly = category_monthly.reindex(main_dates, fill_value=0)
        
        # 为主要费用类别创建历史特征
        target_categories = ['办公用品', '印刷费', '邮寄费', '其他', '计算机耗材']
        
        for category in target_categories:
            if category in category_monthly.columns:
                # 上月、上季度、去年同期
                self.features_df[f'上月{category}费用'] = category_monthly[category].shift(1).values
                self.features_df[f'上季度{category}费用'] = category_monthly[category].shift(3).values
                self.features_df[f'去年同期{category}费用'] = category_monthly[category].shift(12).values
                
                # 移动平均
                self.features_df[f'{category}费用_ma3'] = category_monthly[category].rolling(window=3, min_periods=1).mean().values
                self.features_df[f'{category}费用_ma6'] = category_monthly[category].rolling(window=6, min_periods=1).mean().values
        
        # 特殊类别映射（根据实际业务场景）
        # 注：由于数据中没有明确的业务招待、会议、修理、劳务费用，这里用相近的类别代替
        special_mapping = {
            '业务招待': '其他',  # 可能包含在其他类别中
            '会议': '其他',     # 会议费用可能在其他类别中
            '修理': '其他',     # 修理费用可能在其他类别中  
            '劳务': '其他'      # 劳务费用可能在其他类别中
        }
        
        for special_name, actual_category in special_mapping.items():
            if actual_category in category_monthly.columns:
                self.features_df[f'上月{special_name}费用'] = category_monthly[actual_category].shift(1).values
                self.features_df[f'上季度{special_name}费用'] = category_monthly[actual_category].shift(3).values
                self.features_df[f'去年同期{special_name}费用'] = category_monthly[actual_category].shift(12).values
    
    def create_expense_type_features(self):
        """创建费用类型特征"""
        print("正在创建费用类型特征...")
        
        # 费用类别编码
        category_mapping = {
            '办公用品': 1,
            '印刷费': 2,
            '邮寄费': 3,
            '其他': 4,
            '计算机耗材': 5,
            '邮寄': 3  # 处理数据中的不一致
        }
        
        # 为当月主要费用类别创建特征
        monthly_category = self.df.groupby(['年月', '类别'])['金额'].sum().unstack(fill_value=0)
        monthly_category.index = pd.to_datetime(monthly_category.index)
        
        # 重建索引
        main_dates = pd.to_datetime(self.features_df['年月'])
        monthly_category = monthly_category.reindex(main_dates, fill_value=0)
        
        # 主要费用类别金额
        for category in monthly_category.columns:
            self.features_df[f'当月{category}费用'] = monthly_category[category].values
        
        # 费用类别占比
        total_monthly = monthly_category.sum(axis=1)
        for category in monthly_category.columns:
            self.features_df[f'{category}费用占比'] = (monthly_category[category] / total_monthly.replace(0, np.nan) * 100).values
        
        # 费用是否为周期性（基于历史数据标注）
        self.features_df['费用是否周期性'] = self._label_periodic_expenses()
        
        # 费用是否为临时性（基于金额异常检测）
        self.features_df['费用是否临时性'] = self._label_temporary_expenses()
        
        # 费用类型（管理费用 vs 销售费用）
        expense_type_monthly = self.df.groupby(['年月', '费用类型'])['金额'].sum().unstack(fill_value=0)
        expense_type_monthly.index = pd.to_datetime(expense_type_monthly.index)
        expense_type_monthly = expense_type_monthly.reindex(main_dates, fill_value=0)
        
        if '管理费用' in expense_type_monthly.columns:
            self.features_df['当月管理费用'] = expense_type_monthly['管理费用'].values
        if '销售费用' in expense_type_monthly.columns:
            self.features_df['当月销售费用'] = expense_type_monthly['销售费用'].values
    
    def _label_periodic_expenses(self):
        """标注周期性费用（基于费用变异系数）"""
        # 计算过去12个月的变异系数，低变异系数表示周期性
        cv = self.features_df['金额'].rolling(window=12, min_periods=3).apply(
            lambda x: x.std() / x.mean() if x.mean() > 0 else np.nan
        )
        
        # 变异系数低于0.3的认为是周期性费用
        return (cv <= 0.3).astype(int)
    
    def _label_temporary_expenses(self):
        """标注临时性费用（基于异常检测）"""
        # 使用Z-score检测异常值
        mean_expense = self.features_df['金额'].rolling(window=12, min_periods=3).mean()
        std_expense = self.features_df['金额'].rolling(window=12, min_periods=3).std()
        
        z_score = abs((self.features_df['金额'] - mean_expense) / std_expense.replace(0, np.nan))
        
        # Z-score大于2的认为是临时性费用
        return (z_score > 2).astype(int)
    
    def create_budget_features(self):
        """创建预算特征"""
        print("正在创建预算特征...")
        
        # 由于没有实际预算数据，这里基于历史数据估算预算
        # 使用去年同期+10%作为预算估算
        self.features_df['预算数'] = self.features_df['去年同期费用'] * 1.1
        
        # 当前累积总费用
        self.features_df['累积总费用'] = self.features_df['金额'].cumsum()
        
        # 累积总费用同比增长率
        cumsum_last_year = self.features_df['金额'].shift(12).cumsum()
        self.features_df['累积总费用同比增长率'] = (
            (self.features_df['累积总费用'] - cumsum_last_year) / 
            cumsum_last_year.replace(0, np.nan) * 100
        )
        
        # 费用执行率（当月实际/预算）
        self.features_df['费用执行率'] = (
            self.features_df['金额'] / self.features_df['预算数'].replace(0, np.nan) * 100
        )
        
        # 执行进度差值
        self.features_df['执行进度差值'] = self.features_df['金额'] - self.features_df['预算数']
        
        # 年度累积执行率
        yearly_budget = self.features_df.groupby('年')['预算数'].sum()
        yearly_actual = self.features_df.groupby('年')['金额'].cumsum()
        
        # 为每个年度计算累积执行率
        self.features_df['年度累积执行率'] = 0.0
        for year in self.features_df['年'].unique():
            year_mask = self.features_df['年'] == year
            year_budget = yearly_budget.get(year, 1)
            self.features_df.loc[year_mask, '年度累积执行率'] = (
                self.features_df.loc[year_mask, '金额'].cumsum() / year_budget * 100
            )
    
    def create_target_variable(self):
        """创建目标变量（下月费用）"""
        print("正在创建目标变量...")
        
        # 目标变量：下月费用
        self.features_df['下月费用'] = self.features_df['金额'].shift(-1)
        
        # 目标变量：下月费用是否超过预算
        next_month_budget = self.features_df['预算数'].shift(-1)
        self.features_df['下月是否超预算'] = (
            self.features_df['下月费用'] > next_month_budget
        ).astype(int)
        
        # 目标变量：下月费用增长率
        self.features_df['下月费用增长率'] = (
            (self.features_df['下月费用'] - self.features_df['金额']) / 
            self.features_df['金额'].replace(0, np.nan) * 100
        )
    
    def add_department_features(self):
        """添加部门维度特征"""
        print("正在创建部门特征...")
        
        # 各部门月度费用
        dept_monthly = self.df.groupby(['年月', '部门'])['金额'].sum().unstack(fill_value=0)
        dept_monthly.index = pd.to_datetime(dept_monthly.index)
        
        # 重建索引
        main_dates = pd.to_datetime(self.features_df['年月'])
        dept_monthly = dept_monthly.reindex(main_dates, fill_value=0)
        
        # 各部门费用
        for dept in dept_monthly.columns:
            clean_dept = dept.replace('分公司', '').replace('公司', '')
            self.features_df[f'{clean_dept}部门费用'] = dept_monthly[dept].values
        
        # 部门费用占比
        total_monthly = dept_monthly.sum(axis=1)
        for dept in dept_monthly.columns:
            clean_dept = dept.replace('分公司', '').replace('公司', '')
            self.features_df[f'{clean_dept}部门费用占比'] = (
                dept_monthly[dept] / total_monthly.replace(0, np.nan) * 100
            ).values
    
    def run_feature_engineering(self):
        """运行完整的特征工程流程"""
        print("开始特征工程流程...")
        
        # 1. 加载和预处理数据
        self.load_and_preprocess_data()
        
        # 2. 创建时间特征
        self.create_time_features()
        
        # 3. 创建历史费用特征
        self.create_historical_features()
        
        # 4. 创建类别历史特征
        self.create_category_historical_features()
        
        # 5. 创建费用类型特征
        self.create_expense_type_features()
        
        # 6. 创建预算特征
        self.create_budget_features()
        
        # 7. 添加部门特征
        self.add_department_features()
        
        # 8. 创建目标变量
        self.create_target_variable()
        
        print(f"特征工程完成！生成了 {len(self.features_df.columns)} 个特征")
        return self.features_df
    
    def save_features(self, output_path):
        """保存特征数据"""
        if self.features_df is not None:
            self.features_df.to_csv(output_path, index=False, encoding='utf-8')
            print(f"特征数据已保存到: {output_path}")
        else:
            print("错误：没有特征数据可保存，请先运行特征工程流程")
    
    def get_feature_summary(self):
        """获取特征摘要"""
        if self.features_df is None:
            return "请先运行特征工程流程"
        
        summary = {
            '数据行数': len(self.features_df),
            '特征数量': len(self.features_df.columns),
            '时间范围': f"{self.features_df['日期'].min()} 到 {self.features_df['日期'].max()}",
            '特征列表': list(self.features_df.columns)
        }
        
        return summary

def main():
    """主函数"""
    # 文件路径
    input_file = "办公费用1.csv"
    output_file = "办公费用特征工程数据1.csv"
    
    try:
        # 创建特征工程实例
        fe = OfficeExpenseFeatureEngineering(input_file)
        
        # 运行特征工程
        features_df = fe.run_feature_engineering()
        
        # 保存结果
        fe.save_features(output_file)
        
        # 打印特征摘要
        summary = fe.get_feature_summary()
        print("\n=== 特征工程摘要 ===")
        print(f"数据行数: {summary['数据行数']}")
        print(f"特征数量: {summary['特征数量']}")
        print(f"时间范围: {summary['时间范围']}")
        
        print("\n=== 生成的特征列表 ===")
        feature_categories = {
            '基础信息': ['年月', '年', '月', '季度', '季节', '当月工作日数', '日期'],
            '历史费用特征': [col for col in summary['特征列表'] if any(x in col for x in ['lag', 'ma', '上月', '上季度', '去年同期', '增长率', '增减额', 'max', 'min', 'median', 'std', 'q25', 'q75'])],
            '费用类型特征': [col for col in summary['特征列表'] if any(x in col for x in ['当月', '费用占比', '周期性', '临时性'])],
            '预算特征': [col for col in summary['特征列表'] if any(x in col for x in ['预算', '执行率', '累积'])],
            '部门特征': [col for col in summary['特征列表'] if '部门' in col],
            '目标变量': [col for col in summary['特征列表'] if '下月' in col]
        }
        
        for category, features in feature_categories.items():
            if features:
                print(f"\n{category} ({len(features)}个):")
                for feature in features[:10]:  # 只显示前10个
                    print(f"  - {feature}")
                if len(features) > 10:
                    print(f"  ... 还有 {len(features) - 10} 个特征")
        
        print(f"\n✅ 特征工程完成！结果已保存到 {output_file}")
        
    except Exception as e:
        print(f"❌ 执行过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 