#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
办公费用特征工程测试脚本
简化版本用于测试基本功能
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

def test_basic_feature_engineering():
    """测试基本特征工程功能"""
    print("开始测试特征工程...")
    
    try:
        # 1. 读取数据
        print("1. 正在读取数据...")
        df = pd.read_csv("办公费用1.csv", encoding='utf-8')
        print(f"   数据读取成功，共 {len(df)} 行")
        print(f"   列名: {list(df.columns)}")
        
        # 2. 数据预处理
        print("2. 正在进行数据预处理...")
        
        # 清理金额列
        df['金额'] = df['金额'].astype(str).str.replace(',', '').str.replace(' ', '')
        df['金额'] = pd.to_numeric(df['金额'], errors='coerce')
        
        # 创建日期列
        df['日期'] = pd.to_datetime(df[['年', '月', '日']])
        df['年月'] = df['日期'].dt.strftime('%Y-%m')
        
        print(f"   预处理完成，时间范围: {df['日期'].min()} 到 {df['日期'].max()}")
        
        # 3. 按月汇总
        print("3. 正在按月汇总数据...")
        monthly_summary = df.groupby(['年月', '年', '月']).agg({
            '金额': 'sum',
            '日期': 'first'
        }).reset_index()
        
        monthly_summary = monthly_summary.sort_values('日期').reset_index(drop=True)
        print(f"   月度汇总完成，共 {len(monthly_summary)} 个月")
        
        # 4. 创建基础特征
        print("4. 正在创建基础特征...")
        
        # 季度
        monthly_summary['季度'] = monthly_summary['月'].apply(lambda x: (x-1)//3 + 1)
        
        # 季节
        def get_season(month):
            if month in [3, 4, 5]:
                return '春'
            elif month in [6, 7, 8]:
                return '夏'
            elif month in [9, 10, 11]:
                return '秋'
            else:
                return '冬'
        
        monthly_summary['季节'] = monthly_summary['月'].apply(get_season)
        
        # 5. 创建历史特征
        print("5. 正在创建历史特征...")
        
        # 滞后特征
        for lag in [1, 2, 3]:
            monthly_summary[f'费用_lag{lag}'] = monthly_summary['金额'].shift(lag)
        
        # 移动平均
        for window in [3, 6]:
            monthly_summary[f'费用_ma{window}'] = monthly_summary['金额'].rolling(window=window, min_periods=1).mean()
        
        # 同比环比
        monthly_summary['上月费用'] = monthly_summary['金额'].shift(1)
        monthly_summary['去年同期费用'] = monthly_summary['金额'].shift(12)
        
        # 增长率
        monthly_summary['环比增长率'] = (
            (monthly_summary['金额'] - monthly_summary['上月费用']) / 
            monthly_summary['上月费用'].replace(0, np.nan) * 100
        )
        
        monthly_summary['同比增长率'] = (
            (monthly_summary['金额'] - monthly_summary['去年同期费用']) / 
            monthly_summary['去年同期费用'].replace(0, np.nan) * 100
        )
        
        # 6. 创建目标变量
        print("6. 正在创建目标变量...")
        monthly_summary['下月费用'] = monthly_summary['金额'].shift(-1)
        
        # 7. 保存结果
        print("7. 正在保存结果...")
        output_file = "办公费用特征工程数据1.csv"
        monthly_summary.to_csv(output_file, index=False, encoding='utf-8')
        
        print(f"✅ 特征工程测试完成！")
        print(f"   生成特征数: {len(monthly_summary.columns)}")
        print(f"   数据行数: {len(monthly_summary)}")
        print(f"   输出文件: {output_file}")
        
        # 显示前几行
        print("\n前5行数据预览:")
        print(monthly_summary.head())
        
        print("\n特征列表:")
        for i, col in enumerate(monthly_summary.columns, 1):
            print(f"  {i:2d}. {col}")
            
        return monthly_summary
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = test_basic_feature_engineering() 