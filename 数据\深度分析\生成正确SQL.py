#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
生成正确的SQL INSERT语句
确保列数和值数量完全匹配
"""

import pandas as pd
from datetime import datetime

def generate_correct_sql():
    """生成正确的SQL INSERT语句"""
    
    print("🔧 生成正确的SQL INSERT语句...")
    
    # 读取CSV文件
    df = pd.read_csv('新特征工程数据1.csv', encoding='utf-8')
    print(f"CSV数据: {df.shape}")
    print(f"CSV列数: {len(df.columns)}")
    
    # 定义INSERT语句的列顺序（65个字段，不包括自增id）
    insert_columns = [
        'year_month', 'year', 'month', 'quarter', 'date',
        'monthly_total_amount', 'monthly_avg_amount', 'monthly_transaction_count', 'monthly_amount_std',
        'category_slash_amount', 'category_other_amount', 'category_office_supplies_amount', 
        'category_printing_amount', 'category_computer_consumables_amount', 'category_postage_amount',
        'annual_budget', 'cumulative_amount',
        'last_month_total_amount', 'last_month_slash_amount', 'last_month_other_amount', 
        'last_month_office_supplies_amount', 'last_month_printing_amount', 
        'last_month_computer_consumables_amount', 'last_month_postage_amount',
        'last_month_budget_deviation', 'last_month_compound_growth_rate', 'month_over_month_growth_rate',
        'last_2month_total_amount', 'last_2month_slash_amount', 'last_2month_other_amount',
        'last_2month_office_supplies_amount', 'last_2month_printing_amount',
        'last_2month_computer_consumables_amount', 'last_2month_postage_amount',
        'last_2month_budget_deviation', 'last_2month_compound_growth_rate',
        'last_3month_total_amount', 'last_3month_slash_amount', 'last_3month_other_amount',
        'last_3month_office_supplies_amount', 'last_3month_printing_amount',
        'last_3month_computer_consumables_amount', 'last_3month_postage_amount',
        'last_3month_budget_deviation', 'last_3month_compound_growth_rate',
        'last_year_same_period_total_amount', 'last_year_same_period_slash_amount', 
        'last_year_same_period_other_amount', 'last_year_same_period_office_supplies_amount',
        'last_year_same_period_printing_amount', 'last_year_same_period_computer_consumables_amount',
        'last_year_same_period_postage_amount', 'year_over_year_growth_rate',
        'last_3month_slope', 'last_6month_slope',
        'is_early_year', 'is_mid_year', 'is_late_year', 'is_first_half',
        'is_q1', 'is_q2', 'is_q3', 'is_q4',
        'last_3month_avg', 'last_year_same_3month_avg'
    ]
    
    print(f"INSERT列数: {len(insert_columns)}")
    
    # CSV列名到INSERT列名的映射
    csv_to_insert_mapping = {
        '年月': 'year_month',
        '年': 'year', 
        '月': 'month',
        '季度': 'quarter',
        '日期': 'date',
        '月度总金额': 'monthly_total_amount',
        '月度平均金额': 'monthly_avg_amount',
        '月度交易次数': 'monthly_transaction_count',
        '月度金额标准差': 'monthly_amount_std',
        '类别_/_金额': 'category_slash_amount',
        '类别_其他_金额': 'category_other_amount',
        '类别_办公用品_金额': 'category_office_supplies_amount',
        '类别_印刷费_金额': 'category_printing_amount',
        '类别_计算机耗材_金额': 'category_computer_consumables_amount',
        '类别_邮寄费_金额': 'category_postage_amount',
        '年度预算': 'annual_budget',
        '累计金额': 'cumulative_amount',
        '上月总金额': 'last_month_total_amount',
        '上月_/_金额': 'last_month_slash_amount',
        '上月_其他_金额': 'last_month_other_amount',
        '上月_办公用品_金额': 'last_month_office_supplies_amount',
        '上月_印刷费_金额': 'last_month_printing_amount',
        '上月_计算机耗材_金额': 'last_month_computer_consumables_amount',
        '上月_邮寄费_金额': 'last_month_postage_amount',
        '上月预算偏差': 'last_month_budget_deviation',
        '上月复合增长率': 'last_month_compound_growth_rate',
        '较上月增长率': 'month_over_month_growth_rate',
        '上2月总金额': 'last_2month_total_amount',
        '上2月_/_金额': 'last_2month_slash_amount',
        '上2月_其他_金额': 'last_2month_other_amount',
        '上2月_办公用品_金额': 'last_2month_office_supplies_amount',
        '上2月_印刷费_金额': 'last_2month_printing_amount',
        '上2月_计算机耗材_金额': 'last_2month_computer_consumables_amount',
        '上2月_邮寄费_金额': 'last_2month_postage_amount',
        '上2月预算偏差': 'last_2month_budget_deviation',
        '上2月复合增长率': 'last_2month_compound_growth_rate',
        '上3月总金额': 'last_3month_total_amount',
        '上3月_/_金额': 'last_3month_slash_amount',
        '上3月_其他_金额': 'last_3month_other_amount',
        '上3月_办公用品_金额': 'last_3month_office_supplies_amount',
        '上3月_印刷费_金额': 'last_3month_printing_amount',
        '上3月_计算机耗材_金额': 'last_3month_computer_consumables_amount',
        '上3月_邮寄费_金额': 'last_3month_postage_amount',
        '上3月预算偏差': 'last_3month_budget_deviation',
        '上3月复合增长率': 'last_3month_compound_growth_rate',
        '去年同期总金额': 'last_year_same_period_total_amount',
        '去年同期_/_金额': 'last_year_same_period_slash_amount',
        '去年同期_其他_金额': 'last_year_same_period_other_amount',
        '去年同期_办公用品_金额': 'last_year_same_period_office_supplies_amount',
        '去年同期_印刷费_金额': 'last_year_same_period_printing_amount',
        '去年同期_计算机耗材_金额': 'last_year_same_period_computer_consumables_amount',
        '去年同期_邮寄费_金额': 'last_year_same_period_postage_amount',
        '较去年同期增长率': 'year_over_year_growth_rate',
        '前3月斜率': 'last_3month_slope',
        '前6月斜率': 'last_6month_slope',
        '是否年初': 'is_early_year',
        '是否年中': 'is_mid_year',
        '是否年末': 'is_late_year',
        '是否上半年': 'is_first_half',
        '是否第一季度': 'is_q1',
        '是否第二季度': 'is_q2',
        '是否第三季度': 'is_q3',
        '是否第四季度': 'is_q4',
        '前3月平均值': 'last_3month_avg',
        '去年同期3月平均值': 'last_year_same_3month_avg'
    }
    
    # 生成INSERT语句
    insert_statements = []
    
    for index, row in df.iterrows():
        values = []
        
        for insert_col in insert_columns:
            # 找到对应的CSV列
            csv_col = None
            for csv_name, insert_name in csv_to_insert_mapping.items():
                if insert_name == insert_col:
                    csv_col = csv_name
                    break
            
            if csv_col and csv_col in row:
                value = row[csv_col]
                
                # 处理特殊值
                if pd.isna(value) or value == '':
                    value = 0
                
                # 处理日期
                if insert_col == 'date':
                    try:
                        if '/' in str(value):
                            date_obj = datetime.strptime(str(value), '%Y/%m/%d')
                            value = f"'{date_obj.strftime('%Y-%m-%d')}'"
                        else:
                            value = "'2022-01-01'"
                    except:
                        value = "'2022-01-01'"
                # 处理字符串
                elif insert_col == 'year_month':
                    value = f"'{value}'"
                # 处理数值
                else:
                    try:
                        num_val = float(value)
                        if abs(num_val) < 0.000001:
                            value = 0
                        else:
                            value = round(num_val, 6)
                    except:
                        value = 0
                
                values.append(str(value))
            else:
                values.append('0')
        
        # 构建INSERT语句
        values_str = '(' + ', '.join(values) + ')'
        insert_statements.append(values_str)
    
    # 生成完整SQL
    sql_content = ',\n'.join(insert_statements) + ';'
    
    # 保存到文件
    with open('完整INSERT语句.sql', 'w', encoding='utf-8') as f:
        f.write(sql_content)
    
    print(f"✅ 生成完成！")
    print(f"📊 生成了 {len(insert_statements)} 条INSERT语句")
    print(f"📁 输出文件: 完整INSERT语句.sql")
    
    # 验证第一行
    if insert_statements:
        first_row = insert_statements[0]
        value_count = first_row.count(',') + 1
        print(f"🔍 第一行值数量: {value_count}")
        print(f"🔍 预期列数: {len(insert_columns)}")
        if value_count == len(insert_columns):
            print("✅ 列数匹配！")
        else:
            print("❌ 列数不匹配！")

if __name__ == "__main__":
    generate_correct_sql()
