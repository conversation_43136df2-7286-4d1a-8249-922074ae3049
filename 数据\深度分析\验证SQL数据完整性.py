#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
验证SQL脚本数据完整性
检查CSV文件和SQL脚本中的数据是否一致
"""

import pandas as pd
import re

def verify_sql_data_completeness():
    """验证SQL数据完整性"""
    
    print("🔧 开始验证SQL数据完整性...")
    
    # 1. 读取CSV文件
    print("📂 读取CSV文件...")
    try:
        csv_df = pd.read_csv('新特征工程数据1.csv', encoding='utf-8')
        print(f"CSV数据: {csv_df.shape}")
    except Exception as e:
        print(f"❌ 读取CSV失败: {e}")
        return
    
    # 2. 读取SQL文件
    print("📂 读取SQL文件...")
    try:
        with open('办公费用特征工程数据.sql', 'r', encoding='utf-8') as f:
            sql_content = f.read()
    except Exception as e:
        print(f"❌ 读取SQL失败: {e}")
        return
    
    # 3. 提取SQL中的INSERT语句
    print("🔍 分析SQL INSERT语句...")
    
    # 查找所有INSERT VALUES中的数据行
    insert_pattern = r"\('([^']+)',\s*(\d{4}),\s*(\d{1,2}),"
    matches = re.findall(insert_pattern, sql_content)
    
    print(f"SQL中找到的数据行数: {len(matches)}")
    
    # 4. 提取年月信息进行对比
    csv_year_months = set(csv_df['年月'].tolist())
    sql_year_months = set([match[0] for match in matches])
    
    print(f"\nCSV中的年月数据: {len(csv_year_months)}个")
    print(f"SQL中的年月数据: {len(sql_year_months)}个")
    
    # 5. 检查缺失的数据
    missing_in_sql = csv_year_months - sql_year_months
    extra_in_sql = sql_year_months - csv_year_months
    
    if missing_in_sql:
        print(f"\n❌ SQL中缺失的数据:")
        for ym in sorted(missing_in_sql):
            print(f"  - {ym}")
    
    if extra_in_sql:
        print(f"\n⚠️ SQL中多余的数据:")
        for ym in sorted(extra_in_sql):
            print(f"  - {ym}")
    
    if not missing_in_sql and not extra_in_sql:
        print(f"\n✅ 数据完整性验证通过！")
        print(f"CSV和SQL中的年月数据完全一致")
    
    # 6. 详细对比
    print(f"\n📊 详细数据对比:")
    print("CSV中的年月数据:")
    csv_sorted = sorted(csv_year_months)
    for i, ym in enumerate(csv_sorted):
        print(f"  {i+1:2d}. {ym}")
    
    print(f"\nSQL中的年月数据:")
    sql_sorted = sorted(sql_year_months)
    for i, ym in enumerate(sql_sorted):
        print(f"  {i+1:2d}. {ym}")
    
    # 7. 验证特定数据点
    print(f"\n🎯 验证关键数据点...")
    
    # 检查2023年8月数据
    aug_2023_csv = csv_df[csv_df['年月'] == 'Aug-23']
    if len(aug_2023_csv) > 0:
        aug_amount = aug_2023_csv.iloc[0]['月度总金额']
        print(f"CSV中2023年8月总金额: {aug_amount}")
        
        # 在SQL中查找对应数据
        aug_pattern = r"\('Aug-23',.*?(\d+(?:\.\d+)?),.*?\)"
        aug_match = re.search(aug_pattern, sql_content)
        if aug_match:
            sql_amount = float(aug_match.group(1))
            print(f"SQL中2023年8月总金额: {sql_amount}")
            if abs(aug_amount - sql_amount) < 0.01:
                print("✅ 2023年8月数据匹配")
            else:
                print("❌ 2023年8月数据不匹配")
    
    # 检查2024年9月数据
    sep_2024_csv = csv_df[csv_df['年月'] == 'Sep-24']
    if len(sep_2024_csv) > 0:
        sep_amount = sep_2024_csv.iloc[0]['月度总金额']
        print(f"CSV中2024年9月总金额: {sep_amount}")
        
        # 在SQL中查找对应数据
        sep_pattern = r"\('Sep-24',.*?(\d+(?:\.\d+)?),.*?\)"
        sep_match = re.search(sep_pattern, sql_content)
        if sep_match:
            sql_amount = float(sep_match.group(1))
            print(f"SQL中2024年9月总金额: {sql_amount}")
            if abs(sep_amount - sql_amount) < 0.01:
                print("✅ 2024年9月数据匹配")
            else:
                print("❌ 2024年9月数据不匹配")
    
    # 8. 统计信息
    print(f"\n📈 统计信息:")
    print(f"- CSV总记录数: {len(csv_df)}")
    print(f"- SQL INSERT语句数: {len(matches)}")
    print(f"- 数据时间跨度: {min(csv_sorted)} 到 {max(csv_sorted)}")
    
    return len(missing_in_sql) == 0 and len(extra_in_sql) == 0

if __name__ == "__main__":
    success = verify_sql_data_completeness()
    if success:
        print(f"\n🎉 验证完成！SQL脚本数据完整性良好。")
    else:
        print(f"\n⚠️ 验证发现问题，请检查并修复。")
