#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
将 `费用指标初始化_完整版.sql` 中的多行 VALUES 插入语句，
转换为按 `利润指标初始化.sql` 风格的逐条 INSERT 语句。

输入文件：同目录下 `费用指标初始化_完整版.sql`
输出文件：同目录下 `费用指标初始化.sql`

使用方式：
  在本目录执行：python generate_fee_single_inserts.py
"""

from __future__ import annotations

import io
import os
import re
from datetime import datetime


CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
INPUT_FILENAME = os.path.join(CURRENT_DIR, "费用指标初始化_完整版.sql")
OUTPUT_FILENAME = os.path.join(CURRENT_DIR, "费用指标初始化.sql")


def split_sql_tuples(values_block: str) -> list[str]:
    """将 VALUES 后的整体块分割为单个 "( ... )" 元组字符串列表。

    要求：正确处理 SQL 字符串内的逗号与括号、以及单引号转义（''）。
    返回的每个元素包含外围括号，不包含结尾分隔逗号。
    """
    tuples: list[str] = []
    buff_chars: list[str] = []
    depth = 0
    in_str = False
    i = 0
    n = len(values_block)

    while i < n:
        ch = values_block[i]

        if in_str:
            # 处理 SQL 内部的单引号转义：'' 表示一个单引号字符
            if ch == "'":
                if i + 1 < n and values_block[i + 1] == "'":
                    # 转义单引号，作为内容加入，并跳过下一个引号
                    buff_chars.append("''")
                    i += 2
                    continue
                else:
                    in_str = False
            buff_chars.append(ch)
            i += 1
            continue

        # 非字符串状态
        if ch == "'":
            # 仅在进入 tuple 后才会出现字符串内容
            if depth > 0:
                in_str = True
                buff_chars.append(ch)
            # 若尚未进入 tuple，忽略字符串起始（例如注释里的引号）
            i += 1
            continue

        if ch == '(':
            # 仅在发现左括号时开始收集 tuple
            depth += 1
            buff_chars.append(ch)
            i += 1
            continue

        if ch == ')':
            if depth > 0:
                depth -= 1
                buff_chars.append(ch)
                i += 1
                # 若闭合到顶层，尝试收束一个 tuple
                if depth == 0:
                    tuple_str = ''.join(buff_chars).strip()
                    tuples.append(tuple_str)
                    buff_chars = []

                    # 跳过 tuple 后的空白与逗号
                    while i < n and values_block[i].isspace():
                        i += 1
                    if i < n and values_block[i] == ',':
                        i += 1
                    while i < n and values_block[i].isspace():
                        i += 1
                    continue
                continue
            # 若 depth==0 的右括号，忽略
            i += 1
            continue

        # 其他字符：仅在 tuple 内部收集
        if depth > 0:
            buff_chars.append(ch)
        i += 1

    # 理论上 values_block 在顶层应当被 ; 结束，若仍有残留则清理
    residual = ''.join(buff_chars).strip()
    if residual:
        # 若残留不是纯分号或空白，保守处理：尝试作为一个 tuple 加入
        # 这通常不会发生，但作为兜底
        if residual != ';':
            tuples.append(residual.rstrip(','))

    return tuples


def generate_single_inserts(sql_text: str) -> tuple[str, int]:
    """将输入的 SQL 文本中的多值 INSERT 拆分为单条 INSERT。

    返回：(输出SQL文本, 生成的 INSERT 条数)
    """
    # 正则匹配：INSERT INTO "table"(columns) VALUES <values_block>;
    # values_block 以分号结束，点任意多行匹配
    pattern = re.compile(
        r"(?m)^[ \t]*INSERT\s+INTO\s+\"(?P<table>[^\"]+)\"\s*\((?P<columns>[\s\S]*?)\)\s*VALUES\s*(?P<values>[\s\S]*?)\s*;",
        re.IGNORECASE,
    )

    output_lines: list[str] = []
    total_count = 0

    # 写文件头部说明
    output_lines.append("-- 费用指标初始化SQL")
    output_lines.append(f"-- 生成时间：{datetime.now():%Y-%m-%d}")
    output_lines.append("-- 说明：由费用指标初始化_完整版.sql 自动拆分生成，每条独立 INSERT")
    output_lines.append("")

    pos = 0
    for m in pattern.finditer(sql_text):
        table = m.group('table')
        columns = m.group('columns').strip()
        values_block = m.group('values')

        # 构造单行 INSERT 前缀，保持与利润SQL一致的紧凑风格
        prefix = f"INSERT INTO \"{table}\"({columns}) VALUES "

        # 拆分 tuple 列表
        tuples = split_sql_tuples(values_block)
        for t in tuples:
            # t 形如 "( ... )"，确保末尾无多余逗号
            single_line = prefix + t + ";"
            # 压成单行：去除内部的换行与多余空白（不改变字符串字面值）
            # 仅对空白字符进行压缩，不动引号与标点
            single_line = re.sub(r"\s+", " ", single_line).strip()
            output_lines.append(single_line)
            total_count += 1

        # 记录原文中 INSERT 块之间的非 INSERT 内容（通常为注释），
        # 如果未来有需要保留，可在此处理。本需求不要求保留，暂忽略。
        pos = m.end()

    if total_count == 0:
        raise ValueError("未在输入文件中找到符合格式的 INSERT ... VALUES (...) 多值语句。")

    return "\n".join(output_lines) + "\n", total_count


def main() -> None:
    if not os.path.exists(INPUT_FILENAME):
        raise FileNotFoundError(f"找不到输入文件：{INPUT_FILENAME}")

    with io.open(INPUT_FILENAME, 'r', encoding='utf-8') as f:
        sql_text = f.read()

    output_sql, count = generate_single_inserts(sql_text)

    with io.open(OUTPUT_FILENAME, 'w', encoding='utf-8', newline='') as f:
        f.write(output_sql)

    print(f"已生成 {OUTPUT_FILENAME}，共 {count} 条 INSERT 语句。")


if __name__ == '__main__':
    main()


