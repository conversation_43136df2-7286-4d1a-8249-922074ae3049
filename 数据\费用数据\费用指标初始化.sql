-- 费用指标初始化SQL
-- 生成时间：2025-08-08
-- 说明：由费用指标初始化_完整版.sql 自动拆分生成，每条独立 INSERT

INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458174978, '办公费（销售费用）', 'bangong_xiaoshou', '销售费用中的办公费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458174979, '业务招待费（销售费用）', 'yewuzhaodai_xiaoshou', '销售费用中的业务招待费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458174980, '会议费（销售费用）', 'huiyi_xiaoshou', '销售费用中的会议费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458174981, '职工薪酬（销售费用）', 'zhigongxinchou_xiaoshou', '销售费用中的职工薪酬', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458174982, '职工薪酬-短期薪酬（销售费用）', 'zhigongxinchou_duanqi_xiaoshou', '销售费用中职工薪酬的短期薪酬部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458174983, '职工薪酬-短期薪酬-工资（销售费用）', 'zhigongxinchou_duanqi_gongzi_xiaoshou', '销售费用中职工薪酬短期薪酬的工资部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458174984, '职工薪酬-短期薪酬-员工福利（销售费用）', 'zhigongxinchou_duanqi_yuangongfuli_xiaoshou', '销售费用中职工薪酬短期薪酬的员工福利部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458174985, '职工薪酬-短期薪酬-医疗保险（含生育保险）（销售费用）', 'zhigongxinchou_duanqi_yiliao_xiaoshou', '销售费用中职工薪酬短期薪酬的医疗保险部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458174986, '职工薪酬-短期薪酬-工伤保险（销售费用）', 'zhigongxinchou_duanqi_gongshang_xiaoshou', '销售费用中职工薪酬短期薪酬的工伤保险部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458174987, '职工薪酬-短期薪酬-生育保险（销售费用）', 'zhigongxinchou_duanqi_shengyu_xiaoshou', '销售费用中职工薪酬短期薪酬的生育保险部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458174988, '职工薪酬-短期薪酬-补充医疗保险（销售费用）', 'zhigongxinchou_duanqi_buchongyiliao_xiaoshou', '销售费用中职工薪酬短期薪酬的补充医疗保险部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458174989, '职工薪酬-短期薪酬-住房公积金（销售费用）', 'zhigongxinchou_duanqi_zhufang_xiaoshou', '销售费用中职工薪酬短期薪酬的住房公积金部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458174990, '职工薪酬-短期薪酬-工会经费（销售费用）', 'zhigongxinchou_duanqi_gonghui_xiaoshou', '销售费用中职工薪酬短期薪酬的工会经费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458174991, '职工薪酬-短期薪酬-职工教育经费（销售费用）', 'zhigongxinchou_duanqi_jiaoyu_xiaoshou', '销售费用中职工薪酬短期薪酬的职工教育经费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458174992, '职工薪酬-短期薪酬-其他（销售费用）', 'zhigongxinchou_duanqi_qita_xiaoshou', '销售费用中职工薪酬短期薪酬的其他部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458174993, '职工薪酬-离职后福利（销售费用）', 'zhigongxinchou_lizhi_xiaoshou', '销售费用中职工薪酬的离职后福利部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458174994, '职工薪酬-离职后福利-养老保险（销售费用）', 'zhigongxinchou_lizhi_yanglao_xiaoshou', '销售费用中职工薪酬离职后福利的养老保险部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458174995, '职工薪酬-离职后福利-企业年金（销售费用）', 'zhigongxinchou_lizhi_nianjin_xiaoshou', '销售费用中职工薪酬离职后福利的企业年金部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458174996, '职工薪酬-离职后福利-失业保险（销售费用）', 'zhigongxinchou_lizhi_shiye_xiaoshou', '销售费用中职工薪酬离职后福利的失业保险部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458174997, '职工薪酬-离职后福利-其他（销售费用）', 'zhigongxinchou_lizhi_qita_xiaoshou', '销售费用中职工薪酬离职后福利的其他部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458174998, '职工薪酬-其他长期职工福利（销售费用）', 'zhigongxinchou_changqi_xiaoshou', '销售费用中职工薪酬的其他长期职工福利部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458174999, '租赁费（销售费用）', 'zulin_xiaoshou', '销售费用中的租赁费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175000, '运杂费（销售费用）', 'yunza_xiaoshou', '销售费用中的运杂费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175001, '装卸费（销售费用）', 'zhuangxie_xiaoshou', '销售费用中的装卸费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175002, '挑选整理费（销售费用）', 'tiaoxuan_xiaoshou', '销售费用中的挑选整理费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175003, '包装费（销售费用）', 'baozhuang_xiaoshou', '销售费用中的包装费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175004, '检验费（销售费用）', 'jianyan_xiaoshou', '销售费用中的检验费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175005, '保管费（销售费用）', 'baoguan_xiaoshou', '销售费用中的保管费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175006, '保险费（销售费用）', 'baoxian_xiaoshou', '销售费用中的保险费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175007, '保险费-车辆保险费（销售费用）', 'baoxian_cheliang_xiaoshou', '销售费用中保险费的车辆保险费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175008, '保险费-房屋保险费（销售费用）', 'baoxian_fangwu_xiaoshou', '销售费用中保险费的房屋保险费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175009, '保险费-存货保险费（销售费用）', 'baoxian_cunhuo_xiaoshou', '销售费用中保险费的存货保险费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175010, '保险费-货物运输保险费（销售费用）', 'baoxian_huowu_xiaoshou', '销售费用中保险费的货物运输保险费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175011, '保险费-其他（销售费用）', 'baoxian_qita_xiaoshou', '销售费用中保险费的其他部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175012, '车杂费（销售费用）', 'chezha_xiaoshou', '销售费用中的车杂费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175013, '燃料费（销售费用）', 'ranliao_xiaoshou', '销售费用中的燃料费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175014, '燃料费-车辆燃料费（销售费用）', 'ranliao_cheliang_xiaoshou', '销售费用中燃料费的车辆燃料费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175015, '折旧费（销售费用）', 'zhejiu_xiaoshou', '销售费用中的折旧费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175016, '修理费（销售费用）', 'xiuli_xiaoshou', '销售费用中的修理费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175017, '修理费-车辆修理费（销售费用）', 'xiuli_cheliang_xiaoshou', '销售费用中修理费的车辆修理费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175018, '国内市场营销费（销售费用）', 'guonei_shichang_xiaoshou', '销售费用中的国内市场营销费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175019, '国内市场营销费-媒介费（销售费用）', 'guonei_shichang_meijie_xiaoshou', '销售费用中国内市场营销费的媒介费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175020, '国内市场营销费-物料费（销售费用）', 'guonei_shichang_wuliao_xiaoshou', '销售费用中国内市场营销费的物料费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175021, '国际市场营销费（销售费用）', 'guoji_shichang_xiaoshou', '销售费用中的国际市场营销费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175022, '国际市场营销费-物料费（销售费用）', 'guoji_shichang_wuliao_xiaoshou', '销售费用中国际市场营销费的物料费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175023, '国际市场营销费-渠道建设费（销售费用）', 'guoji_shichang_qudao_xiaoshou', '销售费用中国际市场营销费的渠道建设费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175024, '国际市场营销费-其他（销售费用）', 'guoji_shichang_qita_xiaoshou', '销售费用中国际市场营销费的其他部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175025, '技术使用费（销售费用）', 'jishu_shiyong_xiaoshou', '销售费用中的技术使用费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175026, '手续费（销售费用）', 'shouxu_xiaoshou', '销售费用中的手续费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175027, '劳务费（销售费用）', 'laowu_xiaoshou', '销售费用中的劳务费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175028, '商品损耗（销售费用）', 'shangpin_sunhao_xiaoshou', '销售费用中的商品损耗', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175029, '烟叶临时人员工资（销售费用）', 'yanye_linshi_xiaoshou', '销售费用中的烟叶临时人员工资', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175030, '差旅费（销售费用）', 'chailv_xiaoshou', '销售费用中的差旅费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175031, '海关费（销售费用）', 'haiguan_xiaoshou', '销售费用中的海关费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175032, '熏蒸费（销售费用）', 'xunzheng_xiaoshou', '销售费用中的熏蒸费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175033, '寄样费（销售费用）', 'jiyang_xiaoshou', '销售费用中的寄样费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175034, '滞报滞港费（销售费用）', 'zhibao_zhigang_xiaoshou', '销售费用中的滞报滞港费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175035, '码头费（销售费用）', 'matou_xiaoshou', '销售费用中的码头费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175036, '交易手续费（销售费用）', 'jiaoyi_shouxu_xiaoshou', '销售费用中的交易手续费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175037, '低值易耗品摊销（销售费用）', 'dizhi_yihao_xiaoshou', '销售费用中的低值易耗品摊销', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175038, '零售终端建设费（销售费用）', 'lingshou_zhongduan_xiaoshou', '销售费用中的零售终端建设费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175039, '水电费（销售费用）', 'shuidian_xiaoshou', '销售费用中的水电费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175040, '长期待摊费用摊销（销售费用）', 'changqi_daitan_xiaoshou', '销售费用中的长期待摊费用摊销', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175041, '无形资产摊销（销售费用）', 'wuxing_zichan_xiaoshou', '销售费用中的无形资产摊销', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175042, '其他（销售费用）', 'qita_xiaoshou', '销售费用中的其他费用', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175043, '职工薪酬（管理费用）', 'zhigongxinchou_guanli', '管理费用中的职工薪酬', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175044, '职工薪酬-短期薪酬（管理费用）', 'zhigongxinchou_duanqi_guanli', '管理费用中职工薪酬的短期薪酬部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175045, '职工薪酬-短期薪酬-工资（管理费用）', 'zhigongxinchou_duanqi_gongzi_guanli', '管理费用中职工薪酬短期薪酬的工资部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175046, '职工薪酬-短期薪酬-员工福利（管理费用）', 'zhigongxinchou_duanqi_yuangongfuli_guanli', '管理费用中职工薪酬短期薪酬的员工福利部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175047, '职工薪酬-短期薪酬-医疗保险（含生育保险）（管理费用）', 'zhigongxinchou_duanqi_yiliao_guanli', '管理费用中职工薪酬短期薪酬的医疗保险部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175048, '职工薪酬-短期薪酬-工伤保险（管理费用）', 'zhigongxinchou_duanqi_gongshang_guanli', '管理费用中职工薪酬短期薪酬的工伤保险部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175049, '职工薪酬-短期薪酬-生育保险（管理费用）', 'zhigongxinchou_duanqi_shengyu_guanli', '管理费用中职工薪酬短期薪酬的生育保险部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175050, '职工薪酬-短期薪酬-补充医疗保险（管理费用）', 'zhigongxinchou_duanqi_buchongyiliao_guanli', '管理费用中职工薪酬短期薪酬的补充医疗保险部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175051, '职工薪酬-短期薪酬-住房公积金（管理费用）', 'zhigongxinchou_duanqi_zhufang_gongjijin_guanli', '管理费用中职工薪酬短期薪酬的住房公积金部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175052, '职工薪酬-短期薪酬-工会经费（管理费用）', 'zhigongxinchou_duanqi_gonghui_guanli', '管理费用中职工薪酬短期薪酬的工会经费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175053, '职工薪酬-短期薪酬-职工教育经费（管理费用）', 'zhigongxinchou_duanqi_zhigongjiaoyu_guanli', '管理费用中职工薪酬短期薪酬的职工教育经费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175054, '职工薪酬-短期薪酬-其他（管理费用）', 'zhigongxinchou_duanqi_qita_guanli', '管理费用中职工薪酬短期薪酬的其他部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175055, '职工薪酬-离职后福利（管理费用）', 'zhigongxinchou_lizhi_guanli', '管理费用中职工薪酬的离职后福利部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175056, '职工薪酬-离职后福利-养老保险（管理费用）', 'zhigongxinchou_lizhi_yanglao_guanli', '管理费用中职工薪酬离职后福利的养老保险部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175057, '职工薪酬-离职后福利-企业年金（管理费用）', 'zhigongxinchou_lizhi_qiyenianjin_guanli', '管理费用中职工薪酬离职后福利的企业年金部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175058, '职工薪酬-离职后福利-失业保险（管理费用）', 'zhigongxinchou_lizhi_shiye_guanli', '管理费用中职工薪酬离职后福利的失业保险部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175059, '职工薪酬-离职后福利-其他（管理费用）', 'zhigongxinchou_lizhi_qita_guanli', '管理费用中职工薪酬离职后福利的其他部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175060, '职工薪酬-辞退福利（管理费用）', 'zhigongxinchou_citui_guanli', '管理费用中职工薪酬的辞退福利部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175061, '职工薪酬-其他长期职工福利（管理费用）', 'zhigongxinchou_qitachangqi_guanli', '管理费用中职工薪酬的其他长期职工福利部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175062, '劳动保护费（管理费用）', 'laodong_baohu_guanli', '管理费用中的劳动保护费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175063, '车杂费（管理费用）', 'chezha_guanli', '管理费用中的车杂费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175064, '燃料费（管理费用）', 'ranliao_guanli', '管理费用中的燃料费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175065, '燃料费-车辆燃料费（管理费用）', 'ranliao_cheliang_guanli', '管理费用中燃料费的车辆燃料费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175066, '折旧费（管理费用）', 'zhejiu_guanli', '管理费用中的折旧费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175067, '修理费（管理费用）', 'xiuli_guanli', '管理费用中的修理费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175068, '修理费-车辆修理费（管理费用）', 'xiuli_cheliang_guanli', '管理费用中修理费的车辆修理费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175069, '租赁费（管理费用）', 'zulin_guanli', '管理费用中的租赁费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175070, '通讯费（管理费用）', 'tongxun_guanli', '管理费用中的通讯费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175071, '差旅费（管理费用）', 'chailv_guanli', '管理费用中的差旅费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175072, '会议费（管理费用）', 'huiyi_guanli', '管理费用中的会议费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175073, '业务招待费（管理费用）', 'yewuzhaodai_guanli', '管理费用中的业务招待费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175074, '企业文化建设费（管理费用）', 'qiyewenhua_guanli', '管理费用中的企业文化建设费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175075, '企业文化建设费-媒介费（管理费用）', 'qiyewenhua_meijie_guanli', '管理费用中企业文化建设费的媒介费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175076, '企业文化建设费-物料费（管理费用）', 'qiyewenhua_wuliao_guanli', '管理费用中企业文化建设费的物料费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175077, '办公费（管理费用）', 'bangong_guanli', '管理费用中的办公费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175078, '水电费（管理费用）', 'shuidian_guanli', '管理费用中的水电费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175079, '物业管理费（管理费用）', 'wuye_guanli', '管理费用中的物业管理费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175080, '书报费（管理费用）', 'shubao_guanli', '管理费用中的书报费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175081, '保险费（管理费用）', 'baoxian_guanli', '管理费用中的保险费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175082, '保险费-车辆保险费（管理费用）', 'baoxian_cheliang_guanli', '管理费用中保险费的车辆保险费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175083, '保险费-房屋保险费（管理费用）', 'baoxian_fangwu_guanli', '管理费用中保险费的房屋保险费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175084, '保险费-存货保险费（管理费用）', 'baoxian_cunhuo_guanli', '管理费用中保险费的存货保险费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175085, '保险费-货物运输保险费（管理费用）', 'baoxian_huowu_yunshu_guanli', '管理费用中保险费的货物运输保险费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175086, '保险费-其他（管理费用）', 'baoxian_qita_guanli', '管理费用中保险费的其他部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175087, '企业研发费用（管理费用）', 'qiyeyanfa_guanli', '管理费用中的企业研发费用', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175088, '涉外费（管理费用）', 'shewai_guanli', '管理费用中的涉外费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175089, '涉外费-临时出国(境)费（管理费用）', 'shewai_linshi_chuguo_guanli', '管理费用中涉外费的临时出国(境)费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175090, '涉外费-驻外费用（管理费用）', 'shewai_zhuwai_guanli', '管理费用中涉外费的驻外费用部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175091, '涉外费-外事招待费（管理费用）', 'shewai_waishi_guanli', '管理费用中涉外费的外事招待费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175092, '涉外费-贸易经营临时出国(境)经费（管理费用）', 'shewai_maoyi_chuguo_guanli', '管理费用中涉外费的贸易经营临时出国(境)经费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175093, '诉讼费（管理费用）', 'susong_guanli', '管理费用中的诉讼费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175094, '中介费（管理费用）', 'zhongjie_guanli', '管理费用中的中介费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175095, '打假经费（管理费用）', 'dajia_guanli', '管理费用中的打假经费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175096, '专卖管理经费（管理费用）', 'zhuangmai_guanli', '管理费用中的专卖管理经费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175097, '低值易耗品摊销（管理费用）', 'dizhi_yihaopin_guanli', '管理费用中的低值易耗品摊销', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175098, '长期待摊费用摊销（管理费用）', 'changqi_daitan_guanli', '管理费用中的长期待摊费用摊销', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175099, '无形资产摊销（管理费用）', 'wuxing_zichan_guanli', '管理费用中的无形资产摊销', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175100, '存货盘盈盘亏（管理费用）', 'cunhuo_panying_guanli', '管理费用中的存货盘盈盘亏', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175101, '董事会费（管理费用）', 'dongshihui_guanli', '管理费用中的董事会费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175102, '排污费（管理费用）', 'paiwu_guanli', '管理费用中的排污费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175103, '绿化费（管理费用）', 'luhua_guanli', '管理费用中的绿化费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175104, '信息系统维护费（管理费用）', 'xinxixitong_guanli', '管理费用中的信息系统维护费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175105, '网络通讯费（管理费用）', 'wangluo_tongxun_guanli', '管理费用中的网络通讯费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175106, '警卫消防费（管理费用）', 'jingwei_xiaofang_guanli', '管理费用中的警卫消防费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175107, '政府性基金（管理费用）', 'zhengfuxing_jijin_guanli', '管理费用中的政府性基金', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175108, '政府性基金-水利建设基金（管理费用）', 'zhengfuxing_shuili_guanli', '管理费用中政府性基金的水利建设基金部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175109, '政府性基金-河道管理费（管理费用）', 'zhengfuxing_hedao_guanli', '管理费用中政府性基金的河道管理费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175110, '政府性基金-价格调节基金（管理费用）', 'zhengfuxing_jiage_guanli', '管理费用中政府性基金的价格调节基金部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175111, '政府性基金-堤围防护费（管理费用）', 'zhengfuxing_diwei_guanli', '管理费用中政府性基金的堤围防护费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175112, '政府性基金-残疾人就业保障金（管理费用）', 'zhengfuxing_canji_guanli', '管理费用中政府性基金的残疾人就业保障金部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175113, '政府性基金-其他政府性基金（管理费用）', 'zhengfuxing_qita_guanli', '管理费用中政府性基金的其他政府性基金部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175114, '劳务费用（管理费用）', 'laowu_feiyong_guanli', '管理费用中的劳务费用', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175115, '打私经费（管理费用）', 'dasi_guanli', '管理费用中的打私经费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175116, '打私经费-举报费（管理费用）', 'dasi_jubao_guanli', '管理费用中打私经费的举报费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175117, '打私经费-办案费（管理费用）', 'dasi_banan_guanli', '管理费用中打私经费的办案费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175118, '打私经费-处置费（管理费用）', 'dasi_chuzhi_guanli', '管理费用中打私经费的处置费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175119, '打私经费-奖励费（管理费用）', 'dasi_jiangli_guanli', '管理费用中打私经费的奖励费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175120, '打私经费-补助抚恤费（管理费用）', 'dasi_buzhu_guanli', '管理费用中打私经费的补助抚恤费部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175121, '打私经费-其他（管理费用）', 'dasi_qita_guanli', '管理费用中打私经费的其他部分', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175122, '协会会费（管理费用）', 'xiehui_guanli', '管理费用中的协会会费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175123, '党组织工作经费（管理费用）', 'dangzuzhi_guanli', '管理费用中的党组织工作经费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175124, '文明吸烟环境建设费（管理费用）', 'wenming_xiyan_guanli', '管理费用中的文明吸烟环境建设费', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507458175125, '其他（管理费用）', 'qita_guanli', '管理费用中的其他费用', 1952617432726556674, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
