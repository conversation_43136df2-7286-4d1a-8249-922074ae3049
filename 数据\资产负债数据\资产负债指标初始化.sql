-- 资产负债指标初始化SQL
-- 生成时间：2025-01-27
-- 说明：根据资产负债列表.md生成的124个资产负债指标数据

-- 流动资产类 (27个项目)
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175057, '货币资金', 'huobi_zijin', '企业库存现金、银行存款、其他货币资金等', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175058, '结算备付金', 'jiesuan_beifujin', '金融企业为客户结算而准备的资金', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175059, '拆出资金', 'chaichu_zijin', '金融企业拆借给其他金融机构的资金', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175060, '交易性金融资产', 'jiaoyi_jinrong_zichan', '企业为交易目的而持有的金融资产', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175061, '以公允价值计量且其变动计入当期损益的金融资产', 'gongyun_jiazhi_jiliang_jinrong_zichan', '以公允价值计量且其变动计入当期损益的金融资产', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175062, '衍生金融资产', 'yansheng_jinrong_zichan', '企业持有的各类金融衍生工具资产', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175063, '应收票据', 'yingshou_piaoju', '企业因销售商品、提供劳务等而收到的商业汇票', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175064, '应收账款', 'yingshou_zhangkuan', '企业因销售商品、提供劳务等经营活动应收取的款项', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175065, '应收款项融资', 'yingshou_kuanxiang_rongzi', '企业通过应收款项进行融资的金额', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175066, '预付款项', 'yufu_kuanxiang', '企业按照合同规定预付给供应商的款项', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175067, '应收保费', 'yingshou_baofei', '保险企业应收取的保险费', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175068, '应收分保账款', 'yingshou_fenbao_zhangkuan', '保险企业应收的分保账款', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175069, '应收分保合同准备金', 'yingshou_fenbao_hetong_zhunbeijin', '保险企业应收的分保合同准备金', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175070, '应收资金集中管理款', 'yingshou_zijin_jizhong_guanli_kuan', '企业应收的资金集中管理款项', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175071, '其他应收款', 'qita_yingshou_kuan', '企业除应收票据、应收账款以外的其他各种应收款项', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175072, '应收股利', 'yingshou_guli', '企业应收取的股利', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175073, '买入返售金融资产', 'mairu_fanshou_jinrong_zichan', '金融企业买入并承诺在未来某一日期返售的金融资产', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175074, '存货', 'cunhuo', '企业在日常活动中持有以备出售的产成品或商品、处在生产过程中的在产品、在生产过程或提供劳务过程中耗用的材料和物料等', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175075, '原材料', 'yuancailiao', '企业在生产过程中耗用的各种原材料', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175076, '库存商品（产成品）', 'kucun_shangpin_chanchengpin', '企业已完成全部生产过程并已验收入库、合乎标准规格和技术条件、可以按照合同规定的条件送交订货单位的产品', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175077, '合同资产', 'hetong_zichan', '企业已向客户转让商品而有权收取对价的权利', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175078, '保险合同资产', 'baoxian_hetong_zichan', '保险企业的保险合同资产', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175079, '分出再保险合同资产', 'fenchu_zaibaoxian_hetong_zichan', '保险企业分出再保险合同资产', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175080, '持有待售资产', 'chiyou_daishou_zichan', '企业主要通过出售而非持续使用来回收其账面价值的非流动资产或处置组', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175081, '一年内到期的非流动资产', 'yinian_neidaoqi_feiliudong_zichan', '企业持有的将在一年内到期的非流动资产', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175082, '其他流动资产', 'qita_liudong_zichan', '企业除上述流动资产以外的其他流动资产', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175083, '流动资产合计', 'liudong_zichan_heji', '企业流动资产的合计金额', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);

-- 非流动资产类 (26个项目)
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175084, '发放贷款和垫款', 'fafang_daikuan_hediankuan', '金融企业向客户发放的贷款和垫款', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175085, '债权投资', 'zhaiquan_touzi', '企业持有的债权投资', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175086, '可供出售金融资产', 'kegong_chushou_jinrong_zichan', '企业可供出售的金融资产（旧准则项目）', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175087, '其他债权投资', 'qita_zhaiquan_touzi', '企业持有的其他债权投资', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175088, '持有至到期投资', 'chiyou_zhidaoqi_touzi', '企业持有至到期的投资（旧准则项目）', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175089, '长期应收款', 'changqi_yingshou_kuan', '企业融资租赁产生的应收款项和其他经营活动产生的长期应收款项', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175090, '长期股权投资', 'changqi_guquan_touzi', '企业通过投资取得被投资单位的股份，对被投资单位具有重大影响或共同控制的权益性投资', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175091, '其他权益工具投资', 'qita_quanyi_gongju_touzi', '企业持有的其他权益工具投资', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175092, '其他非流动金融资产', 'qita_feiliudong_jinrong_zichan', '企业持有的其他非流动金融资产', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175093, '投资性房地产', 'touzi_fangdichan', '企业为赚取租金或资本增值，或两者兼有而持有的房地产', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175094, '固定资产', 'guding_zichan', '企业为生产商品、提供劳务、出租或经营管理而持有的、使用寿命超过一个会计年度的有形资产', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175095, '固定资产原价', 'guding_zichan_yuanjia', '固定资产的原始成本', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175096, '累计折旧', 'leiji_zhejiu', '固定资产的累计折旧', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175097, '固定资产减值准备', 'guding_zichan_jianzhi_zhunbei', '固定资产的减值准备', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175098, '在建工程', 'zaijian_gongcheng', '企业尚未达到预定可使用状态的建筑工程、安装工程、技术改造工程等', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175099, '生产性生物资产', 'shengchanxing_shengwu_zichan', '企业为产出农产品、提供劳务或出租等目的而持有的生物资产', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175100, '油气资产', 'youqi_zichan', '石油天然气开采企业拥有或控制的井及相关设施和矿区权益', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175101, '使用权资产', 'shiyongquan_zichan', '承租人在租赁期内使用租赁资产的权利', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175102, '无形资产', 'wuxing_zichan', '企业拥有或者控制的没有实物形态的可辨认非货币性资产', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175103, '开发支出', 'kaifa_zhichu', '企业进行研究与开发无形资产过程中发生的各项支出', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175104, '商誉', 'shangyu', '企业合并成本超过合并中取得的被购买方可辨认净资产公允价值份额的差额', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175105, '长期待摊费用', 'changqi_daitan_feiyong', '企业已经发生但应由本期和以后各期负担的分摊期限在一年以上的各项费用', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175106, '递延所得税资产', 'diyan_suodeshui_zichan', '企业因可抵扣暂时性差异产生的资产', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175107, '其他非流动资产', 'qita_feiliudong_zichan', '企业除上述非流动资产以外的其他非流动资产', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175108, '特准储备物资', 'tezhun_chubei_wuzi', '企业的特准储备物资', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175109, '非流动资产合计', 'feiliudong_zichan_heji', '企业非流动资产的合计金额', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);

-- 流动负债类 (29个项目)
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175110, '短期借款', 'duanqi_jiekuan', '企业向银行或其他金融机构等借入的期限在一年以下的各种借款', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175111, '向中央银行借款', 'xiang_zhongyang_yinhang_jiekuan', '金融企业向中央银行的借款', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175112, '拆入资金', 'chairu_zijin', '金融企业从其他金融机构拆借的资金', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175113, '交易性金融负债', 'jiaoyi_jinrong_fuzhai', '企业承担的交易性金融负债', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175114, '以公允价值计量且其变动计入当期损益的金融负债', 'gongyun_jiazhi_jiliang_jinrong_fuzhai', '以公允价值计量且其变动计入当期损益的金融负债', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175115, '衍生金融负债', 'yansheng_jinrong_fuzhai', '企业承担的各类金融衍生工具负债', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175116, '应付票据', 'yingfu_piaoju', '企业购买材料、商品和接受劳务供应等而开出、承兑的商业汇票', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175117, '应付账款', 'yingfu_zhangkuan', '企业因购买材料、商品和接受劳务等经营活动应支付的款项', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175118, '预收款项', 'yushou_kuanxiang', '企业按照合同规定预先收取的款项', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175119, '合同负债', 'hetong_fuzhai', '企业已收或应收客户对价而应向客户转让商品的义务', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175120, '卖出回购金融资产款', 'maichu_huigou_jinrong_zichan_kuan', '金融企业卖出并承诺在未来某一日期回购的金融资产款', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175121, '吸收存款及同业存放', 'xishou_cunkuan_jitongye_cunfang', '金融企业吸收的存款及同业存放款项', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175122, '代理买卖证券款', 'daili_maimai_zhengquan_kuan', '证券公司代理客户买卖证券收到的款项', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175123, '代理承销证券款', 'daili_chengxiao_zhengquan_kuan', '证券公司代理承销证券收到的款项', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175124, '预收保费', 'yushou_baofei', '保险企业预收的保险费', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175125, '应付职工薪酬', 'yingfu_zhigong_xinchoup', '企业根据有关规定应付给职工的各种薪酬', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175126, '应付工资', 'yingfu_gongzi', '企业应付给职工的工资', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175127, '应付福利费', 'yingfu_fulifei', '企业应付给职工的福利费', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175128, '职工奖励及福利基金', 'zhigong_jiangli_jifulijin', '外商投资企业的职工奖励及福利基金', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175129, '应交税费', 'yingjiao_shuifei', '企业按照税法等规定计算应交纳的各种税费', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175130, '应交税金', 'yingjiao_shuijin', '企业应交纳的税金', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175131, '其他应付款', 'qita_yingfu_kuan', '企业除应付票据、应付账款、预收账款、应付职工薪酬、应交税费等以外的其他各项应付、暂收款项', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175132, '应付股利', 'yingfu_guli', '企业分配给股东的现金股利或利润', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175133, '应付手续费及佣金', 'yingfu_shouxufei_jiyongjin', '企业应付的手续费及佣金', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175134, '应付分保账款', 'yingfu_fenbao_zhangkuan', '保险企业应付的分保账款', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175135, '持有待售负债', 'chiyou_daishou_fuzhai', '企业持有待售负债', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175136, '一年内到期的非流动负债', 'yinian_neidaoqi_feiliudong_fuzhai', '企业将在一年内到期的非流动负债', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175137, '其他流动负债', 'qita_liudong_fuzhai', '企业除上述流动负债以外的其他流动负债', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175138, '流动负债合计', 'liudong_fuzhai_heji', '企业流动负债的合计金额', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);

-- 非流动负债类 (17个项目)
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175139, '保险合同准备金', 'baoxian_hetong_zhunbeijin', '保险企业的保险合同准备金', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175140, '长期借款', 'changqi_jiekuan', '企业向银行或其他金融机构等借入的期限在一年以上的各种借款', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175141, '应付债券', 'yingfu_zhaiquan', '企业为筹集长期资金而实际发行和承担的债券', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175142, '优先股', 'youxian_gu', '企业发行的优先股', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175143, '永续债', 'yongxu_zhai', '企业发行的永续债券', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175144, '保险合同负债', 'baoxian_hetong_fuzhai', '保险企业的保险合同负债', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175145, '分出再保险合同负债', 'fenchu_zaibaoxian_hetong_fuzhai', '保险企业分出再保险合同负债', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175146, '租赁负债', 'zulin_fuzhai', '承租人应付的租赁负债', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175147, '长期应付款', 'changqi_yingfu_kuan', '企业除长期借款和应付债券以外的其他各种长期应付款项', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175148, '长期应付职工薪酬', 'changqi_yingfu_zhigong_xinchoup', '企业应付职工的长期薪酬', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175149, '预计负债', 'yuji_fuzhai', '企业承担的现时义务，履行该义务很可能导致经济利益流出企业，且其金额能够可靠计量的负债', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175150, '递延收益', 'diyan_shouyi', '企业确认的应在以后期间计入当期损益的收益', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175151, '递延所得税负债', 'diyan_suodeshui_fuzhai', '企业因应纳税暂时性差异产生的负债', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175152, '其他非流动负债', 'qita_feiliudong_fuzhai', '企业除上述非流动负债以外的其他非流动负债', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175153, '特准储备基金', 'tezhun_chubei_jijin', '企业的特准储备基金', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175154, '非流动负债合计', 'feiliudong_fuzhai_heji', '企业非流动负债的合计金额', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175155, '负债合计', 'fuzhai_heji', '企业负债的合计金额', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);

-- 所有者权益（或股东权益）类 (25个项目)
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175156, '实收资本（或股本）', 'shishou_ziben_hugubeng', '企业实际收到的投资者投入的资本', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175157, '国家资本', 'guojia_ziben', '国家投入企业的资本', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175158, '国有法人资本', 'guoyou_faren_ziben', '国有法人投入企业的资本', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175159, '集体资本', 'jiti_ziben', '集体投入企业的资本', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175160, '民营资本', 'minying_ziben', '民营投资者投入企业的资本', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175161, '外商资本', 'waishang_ziben', '外商投入企业的资本', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175162, '已归还投资', 'yiguihuan_touzi', '外商投资企业已归还的投资', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175163, '实收资本（或股本）净额', 'shishou_ziben_jinge', '实收资本（或股本）的净额', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175164, '其他权益工具', 'qita_quanyi_gongju', '企业发行的除普通股以外的归类为权益工具的金融工具', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175165, '优先股', 'youxian_gu_quanyi', '企业发行的优先股（权益工具）', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175166, '永续债', 'yongxu_zhai_quanyi', '企业发行的永续债券（权益工具）', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175167, '资本公积', 'ziben_gongjie', '企业收到投资者出资额超出其在注册资本或股本中所占份额的部分', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175168, '库存股', 'kucun_gu', '企业收购、转让或注销本公司股份时，应设置的科目', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175169, '其他综合收益', 'qita_zonghe_shouyi', '企业根据企业会计准则规定未在当期损益中确认的各项利得和损失', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175170, '外币报表折算差额', 'waibi_baobiao_zhesuan_chae', '企业对境外经营财务报表进行折算产生的折算差额', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175171, '专项储备', 'zhuanxiang_chubei', '企业按规定提取的具有特定用途的储备', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175172, '盈余公积', 'yingyu_gongjie', '企业从税后利润中提取形成的、存留于企业内部、具有特定用途的收益积累', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175173, '法定公积金', 'fading_gongjijin', '企业按照法律规定提取的公积金', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175174, '任意公积金', 'renyi_gongjijin', '企业按照章程或股东会决议提取的公积金', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175175, '储备基金', 'chubei_jijin', '外商投资企业的储备基金', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175176, '企业发展基金', 'qiye_fazhan_jijin', '外商投资企业的企业发展基金', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175177, '利润归还投资', 'lirun_guihuan_touzi', '外商投资企业利润归还投资', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175178, '一般风险准备', 'yiban_fengxian_zhunbei', '金融企业的一般风险准备', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175179, '未分配利润', 'weifenpei_lirun', '企业实现的净利润经过弥补亏损、提取盈余公积和向投资者分配利润后留存在企业的、历年结存的利润', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);
INSERT INTO "yjzb_indicator"("id", "name", "code", "description", "indicator_type_id", "data_type", "unit", "calculation_formula", "data_source_config", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1952675507468175180, '归属于母公司所有者权益（或股东权益）合计', 'guishu_mugongsi_suoyouzhe_quanyi_heji', '归属于母公司所有者权益（或股东权益）的合计金额', 1952621133444849665, 'NUMERIC', '万元', '', '', 1123598821738675201, 1123598813738675201, '2025-01-27 10:00:00', 1123598821738675201, '2025-01-27 10:00:00', 1, 0);