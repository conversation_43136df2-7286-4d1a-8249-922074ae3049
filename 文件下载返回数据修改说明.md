# 文件下载返回数据修改说明

## 概述

将文档下载接口从重定向方式修改为直接返回文件数据的方式，以提供更好的用户体验和更可靠的下载功能。

## 修改内容

### 1. 控制器层修改

**文件**: `backend/src/main/java/org/springblade/modules/yjzb/controller/FinanceDocumentController.java`

**修改前**:
```java
public void download(@PathVariable Long id, HttpServletResponse response) throws IOException {
    String downloadUrl = financeDocumentService.getDocumentDownloadUrl(id);
    if (Func.isNotBlank(downloadUrl)) {
        if (downloadUrl.startsWith("http://") || downloadUrl.startsWith("https://")) {
            response.sendRedirect(downloadUrl);  // 重定向方式
        } else {
            response.setHeader("Content-Disposition", "attachment; filename=" + downloadUrl);
        }
    } else {
        response.setStatus(HttpServletResponse.SC_NOT_FOUND);
    }
}
```

**修改后**:
```java
public void download(@PathVariable Long id, HttpServletResponse response) throws IOException {
    byte[] fileData = financeDocumentService.downloadDocumentData(id);
    if (fileData != null && fileData.length > 0) {
        // 获取文档信息用于设置响应头
        var document = financeDocumentService.getById(id);
        if (document != null) {
            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setContentLength(fileData.length);
            response.setHeader("Content-Disposition", 
                "attachment; filename=\"" + document.getFileName() + "\"");
            
            // 写入文件数据到响应流
            try (var outputStream = response.getOutputStream()) {
                outputStream.write(fileData);
                outputStream.flush();
            }
        } else {
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
        }
    } else {
        response.setStatus(HttpServletResponse.SC_NOT_FOUND);
    }
}
```

### 2. 服务接口扩展

**文件**: `backend/src/main/java/org/springblade/modules/yjzb/service/IFinanceDocumentService.java`

新增方法：
```java
/**
 * 下载文档数据
 *
 * @param id 文档ID
 * @return 文件数据字节数组
 */
byte[] downloadDocumentData(Long id);
```

### 3. 服务实现类修改

**文件**: `backend/src/main/java/org/springblade/modules/yjzb/service/impl/FinanceDocumentServiceImpl.java`

#### 3.1 新增导入
```java
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import io.minio.GetObjectArgs;
```

#### 3.2 新增依赖
```java
private final RestTemplate restTemplate;
```

#### 3.3 核心下载方法实现
```java
@Override
public byte[] downloadDocumentData(Long id) {
    try {
        FinanceDocumentEntity document = getById(id);
        if (document == null) {
            log.error("文档不存在，ID: {}", id);
            return null;
        }

        // 如果文档已同步到Dify，优先从Dify下载
        if (Func.isNotBlank(document.getDocumentId())) {
            // 获取知识库信息
            FinanceKnowledgeEntity knowledge = financeKnowledgeService.getById(document.getKnowledgeId());
            if (knowledge != null && Func.isNotBlank(knowledge.getDatasetId())) {
                // 先获取Dify文件信息
                String fileInfo = difyService.getDocumentUploadFile(knowledge.getDatasetId(), document.getDocumentId());
                if (Func.isNotBlank(fileInfo)) {
                    try {
                        JsonNode jsonNode = objectMapper.readTree(fileInfo);
                        if (jsonNode.has("download_url")) {
                            String downloadUrl = jsonNode.get("download_url").asText();
                            // 从Dify URL下载文件数据
                            byte[] fileData = downloadFromUrl(downloadUrl);
                            if (fileData != null) {
                                // 增加下载次数
                                incrementDownloadCount(id);
                                return fileData;
                            }
                        }
                    } catch (Exception e) {
                        log.error("解析Dify文件信息失败", e);
                    }
                }
            }
        }

        // 如果从Dify下载失败，尝试从本地存储下载
        if (Func.isNotBlank(document.getFilePath())) {
            byte[] fileData = downloadFromLocalStorage(document.getFilePath());
            if (fileData != null) {
                // 增加下载次数
                incrementDownloadCount(id);
                return fileData;
            }
        }

        return null;
    } catch (Exception e) {
        log.error("下载文档数据失败，ID: {}", id, e);
        return null;
    }
}
```

#### 3.4 辅助方法实现

**从URL下载文件**:
```java
private byte[] downloadFromUrl(String url) {
    try {
        HttpHeaders headers = new HttpHeaders();
        HttpEntity<Void> entity = new HttpEntity<>(headers);
        
        ResponseEntity<byte[]> response = restTemplate.exchange(url, HttpMethod.GET, entity, byte[].class);
        
        if (response.getStatusCode().is2xxSuccessful()) {
            return response.getBody();
        }
        
        log.warn("从URL下载文件失败，响应状态: {}, URL: {}", response.getStatusCode(), url);
        return null;
    } catch (Exception e) {
        log.error("从URL下载文件异常, URL: {}", url, e);
        return null;
    }
}
```

**从MinIO下载文件**:
```java
private byte[] downloadFromMinio(String objectName) {
    try {
        MinioClient minioClient = getMinioClient();
        try (var inputStream = minioClient.getObject(
                GetObjectArgs.builder()
                        .bucket(bucketName)
                        .object(objectName)
                        .build())) {
            return inputStream.readAllBytes();
        }
    } catch (Exception e) {
        log.error("从MinIO下载文件失败，对象名: {}", objectName, e);
        return null;
    }
}
```

**从OSS下载文件**:
```java
private byte[] downloadFromOss(String filePath) {
    try {
        // 使用OSS模板获取文件链接，然后通过HTTP下载
        String fileLink = ossTemplate.fileLink(filePath);
        if (Func.isNotBlank(fileLink)) {
            return downloadFromUrl(fileLink);
        } else {
            log.warn("无法获取OSS文件链接，路径: {}", filePath);
            return null;
        }
    } catch (Exception e) {
        log.error("从OSS下载文件失败，路径: {}", filePath, e);
        return null;
    }
}
```

## 功能特点

### ✅ **智能下载策略**
1. **Dify优先**: 如果文档已同步到Dify，优先从Dify下载
2. **本地降级**: 如果Dify不可用，自动降级到本地存储
3. **多存储支持**: 支持MinIO和OSS等多种存储方式

### ✅ **完整的文件处理**
1. **正确的响应头**: 设置合适的Content-Type和Content-Disposition
2. **文件名处理**: 使用原始文件名作为下载文件名
3. **流式传输**: 高效的文件数据传输

### ✅ **错误处理和日志**
1. **完善的异常处理**: 各个环节都有适当的错误处理
2. **详细的日志记录**: 便于问题排查和监控
3. **降级机制**: 确保在部分服务不可用时仍能提供服务

### ✅ **统计功能**
1. **下载次数统计**: 正确记录文档下载次数
2. **统一统计**: 无论使用哪种下载方式都会统计

## 优势对比

| 特性 | 重定向方式 | 返回数据方式 |
|------|------------|--------------|
| 用户体验 | 可能跳转页面 | 直接下载，无跳转 |
| 错误处理 | 难以控制 | 完全可控 |
| 统计准确性 | 可能遗漏 | 100%准确 |
| 跨域问题 | 可能存在 | 无跨域问题 |
| 文件名控制 | 依赖外部服务 | 完全可控 |
| 安全性 | 暴露外部URL | 隐藏实际存储位置 |

## 测试建议

1. **Dify文档下载测试**: 测试已同步到Dify的文档下载
2. **本地文档下载测试**: 测试未同步或Dify不可用时的本地下载
3. **大文件下载测试**: 测试大文件的下载性能和稳定性
4. **并发下载测试**: 测试多用户同时下载的性能
5. **错误场景测试**: 测试各种异常情况的处理
6. **统计功能测试**: 验证下载次数统计的准确性

## 注意事项

1. **内存使用**: 大文件下载时注意内存使用情况
2. **超时设置**: 确保HTTP客户端有合适的超时设置
3. **并发控制**: 考虑添加下载并发控制以保护系统资源
4. **缓存策略**: 可以考虑添加文件缓存以提高性能
5. **监控告警**: 建议添加下载失败率的监控和告警
