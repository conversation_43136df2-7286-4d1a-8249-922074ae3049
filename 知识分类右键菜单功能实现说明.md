# 知识分类右键菜单功能实现说明

## 功能概述

为财务知识库的分类树添加了右键菜单功能，支持分类的增加、删除、编辑操作，并限制树层级为2级。

## 实现的功能

### 1. 右键菜单功能
- ✅ 在分类节点上右键显示菜单
- ✅ 在空白区域右键可添加根分类
- ✅ 菜单项根据当前节点状态动态显示/隐藏
- ✅ 点击其他区域自动隐藏菜单

### 2. 分类管理功能

#### 新增分类
- ✅ 支持添加根分类（在空白区域右键）
- ✅ 支持添加子分类（在一级分类上右键）
- ✅ 限制最多2级分类结构
- ✅ 表单验证（分类名称必填，长度限制）
- ✅ 对接后端API：`POST /yjzb/finance-category/save`

#### 编辑分类
- ✅ 获取分类详情并填充表单
- ✅ 支持修改分类名称、描述、排序
- ✅ 显示父分类信息
- ✅ 对接后端API：`POST /yjzb/finance-category/update`

#### 删除分类
- ✅ 删除前确认提示
- ✅ 检查是否有子分类或文档（有则不允许删除）
- ✅ 对接后端API：`POST /yjzb/finance-category/remove`

### 3. 层级限制
- ✅ 限制树结构最多2级
- ✅ 一级分类可以添加子分类
- ✅ 二级分类不能再添加子分类
- ✅ 在构建树时添加level字段标识层级

## 技术实现

### 前端实现

#### 1. 右键菜单组件
```vue
<!-- 右键菜单 -->
<div 
  v-if="contextMenuVisible" 
  class="context-menu"
  :style="{ left: contextMenuX + 'px', top: contextMenuY + 'px' }"
  @click.stop
>
  <div class="menu-item" @click="handleAddCategory" v-if="canAddChild">
    <el-icon><plus /></el-icon>
    <span>{{ currentNodeData ? '新增子分类' : '新增分类' }}</span>
  </div>
  <div class="menu-item" @click="handleEditCategory" v-if="currentNodeData">
    <el-icon><edit /></el-icon>
    <span>编辑分类</span>
  </div>
  <div class="menu-item danger" @click="handleDeleteCategory" v-if="currentNodeData && canDelete">
    <el-icon><delete /></el-icon>
    <span>删除分类</span>
  </div>
</div>
```

#### 2. 事件处理
- `@node-contextmenu="handleNodeContextmenu"` - 节点右键事件
- `@contextmenu="handleTreeContextmenu"` - 树空白区域右键事件
- 全局点击事件监听隐藏菜单

#### 3. 状态管理
```javascript
// 右键菜单相关状态
contextMenuVisible: false,
contextMenuX: 0,
contextMenuY: 0,
currentNode: null,
currentNodeData: null,

// 分类管理对话框状态
categoryDialogVisible: false,
categoryDialogTitle: '',
categoryForm: {
  id: null,
  knowledgeId: 1,
  parentId: null,
  categoryName: '',
  categoryDescription: '',
  sort: 1
}
```

#### 4. 计算属性
```javascript
// 是否可以添加子分类（限制2级）
canAddChild() {
  if (!this.currentNodeData) return true // 右键空白处可以添加根分类
  return this.currentNodeData.level === 1 // 只有根分类可以添加子分类
},

// 是否可以删除分类
canDelete() {
  if (!this.currentNodeData) return false
  return (!this.currentNodeData.children || this.currentNodeData.children.length === 0) && 
         (this.currentNodeData.count === 0)
}
```

### 后端API接口

#### 1. 分类管理接口
- `GET /yjzb/finance-category/tree` - 获取分类树
- `GET /yjzb/finance-category/detail` - 获取分类详情
- `POST /yjzb/finance-category/save` - 新增分类
- `POST /yjzb/finance-category/update` - 更新分类
- `POST /yjzb/finance-category/remove` - 删除分类

#### 2. 数据结构
```javascript
// 分类数据结构
{
  id: 1,
  knowledgeId: 1,
  parentId: null,
  categoryName: "财务制度",
  categoryDescription: "财务相关制度文档",
  sort: 1,
  level: 1, // 层级标识
  count: 5, // 文档数量
  children: []
}
```

## 样式设计

### 右键菜单样式
```scss
.context-menu {
  position: fixed;
  background: #fff;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 9999;
  min-width: 120px;
  padding: 4px 0;

  .menu-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
    color: #606266;
    transition: all 0.3s;

    &:hover {
      background-color: #f5f7fa;
      color: #409eff;
    }

    &.danger {
      color: #f56c6c;
      &:hover {
        background-color: #fef0f0;
      }
    }
  }
}
```

## 使用说明

1. **添加分类**：
   - 在空白区域右键选择"新增分类"添加根分类
   - 在一级分类上右键选择"新增子分类"添加二级分类

2. **编辑分类**：
   - 在任意分类节点上右键选择"编辑分类"
   - 修改分类名称、描述等信息

3. **删除分类**：
   - 在分类节点上右键选择"删除分类"
   - 只有没有子分类且没有文档的分类才能删除

## 注意事项

1. 分类树最多支持2级结构
2. 删除分类前会检查是否有子分类或关联文档
3. 所有操作都会实时更新分类树和文档列表
4. 表单验证确保数据完整性
5. 错误处理和用户提示完善

## 测试建议

1. 测试右键菜单在不同位置的显示
2. 测试层级限制是否生效
3. 测试删除限制是否正确
4. 测试表单验证
5. 测试API接口调用和错误处理
