# 通用指标管控系统项目设计（单应用架构：Vue.js + Java/Spring Boot + PostgreSQL）

## 一、系统架构总览

本系统采用单应用架构，所有业务逻辑、数据处理、AI集成接口均在主应用内实现。AI预测与分析服务作为独立外部服务，通过API与主应用对接。

```
+-------------------------------------------------------------+
|           用户界面 (Web UI - Vue.js)                        |
+-------------------------------------------------------------+
       |
       | HTTP/REST API
       v
+--------------------------------------------------------------------------------------------------------+
|                                主应用 (后端 - Java/Spring Boot)                                        |
|   +---------------------+   +---------------------+   +---------------------+   +---------------------+|
|   | 指标管理模块        |<->| 数据采集模块        |<->| 数据处理与计算模块  |<->| 预警模块            ||
|   +---------------------+   +---------------------+   +---------------------+   +---------------------+|
|             ^                                 ^                     ^                                    |
|             |                                 |                     |                                    |
|             +--------------------------------------------------------------------------------------------+
|             |                                                                                            |
|             v                                                                                            |
|   +---------------------+   +---------------------+   +---------------------+                          |
|   | 报表与可视化模块    |<->| AI集成模块          |---->| 外部 AI/ML 服务 API |                          |
|   +---------------------+   +---------------------+   +---------------------+                          |
|                                         |                                                                |
|                                         | SQL                                                            |
|                                         v                                                                |
|   +-------------------------------------------------------------------------------------------------+    |
|   |                       PostgreSQL 数据库                                                       |    |
|   |   - Metrics (指标定义)                                                                          |    |
|   |   - Metric_Values (指标数据)                                                                    |    |
|   |   - Alert_Rules (预警规则)                                                                      |    |
|   |   - Alert_Events (预警事件)                                                                     |    |
|   |   - AI_Models (AI模型元数据)                                                                    |    |
|   |   - AI_Predictions (AI预测结果)                                                                 |    |
|   +-------------------------------------------------------------------------------------------------+    |
|                                                                                                        |
+--------------------------------------------------------------------------------------------------------+
         |
         | (邮件/短信/微信等)
         v
+-----------------------+
|   通知服务 (第三方)   |
+-----------------------+
```

### 各模块简要说明

- **用户界面（Vue.js）**：负责所有用户交互、数据展示、图表渲染、表单提交等，通过RESTful API与后端通信。
- **主应用（Spring Boot）**：包含所有业务逻辑，各模块通过内部调用协作。
  - **指标管理模块**：定义、修改、删除各类指标及元数据，支持类型管理、启用/停用、筛选、查询等。
  - **数据采集模块**：定时/手动采集外部数据源数据，支持多源导入（Excel、数据库、财务系统等），数据校验清洗。
  - **数据处理与计算模块**：数据清洗、转换、复合指标计算。
  - **预警模块**：基于规则实时/准实时监控指标，触发预警，支持异常规则管理、通知配置、历史记录。
  - **AI集成模块**：对接外部AI/ML服务，获取预测与分析结果，支持LLM模型与MCP协议管理。
  - **报表与可视化模块**：为前端提供数据接口，支持多维度报表、图表、仪表盘、深度分析报告、知识库可视化。
  - **问数助手模块**：支持自然语言查询、趋势/条件/指标查询、多轮对话、历史查询记录、结果可视化。
  - **系统管理模块**：用户、菜单、权限、日志统一管理。
- **外部AI/ML服务**：独立Python服务，负责模型训练、预测、分析。
- **PostgreSQL数据库**：统一存储所有业务数据。
- **通知服务**：集成邮件、短信、微信/钉钉等第三方通知渠道。

---

## 二、核心数据结构设计（PostgreSQL）

### 1. 指标定义（metrics）

- 支持基础、枚举、百分比、复合等多类型指标
- 支持灵活的数据源配置（SQL、API等）
- 支持多维度、公式依赖

**表结构设计：**
```sql
CREATE TYPE metric_type_enum AS ENUM ('NUMERIC', 'PERCENTAGE', 'ENUM', 'COMPOSITE');

CREATE TABLE metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    code VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    type metric_type_enum NOT NULL,
    unit VARCHAR(50),
    data_source_config JSONB,
    dimensions JSONB,
    calculation_formula TEXT,
    formula_dependencies UUID[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2. 指标数据（metric_values）

- 存储每个指标每个时间点、维度下的具体值
- 支持原始与计算型数据区分

**表结构设计：**
```sql
CREATE TABLE metric_values (
    id BIGSERIAL PRIMARY KEY,
    metric_id UUID NOT NULL REFERENCES metrics(id),
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    value NUMERIC(18, 2) NOT NULL,
    dimensions JSONB,
    data_source_info JSONB,
    is_calculated BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
CREATE INDEX idx_metric_values_metric_time ON metric_values (metric_id, timestamp DESC);
```

### 3. 预警规则（alert_rules）

- 支持阈值、波动、趋势、异常等多种预警类型
- 支持多维度过滤、通知渠道配置

**表结构设计：**
```sql
CREATE TYPE alert_rule_type_enum AS ENUM ('THRESHOLD', 'FLUCTUATION', 'TREND', 'ANOMALY');
CREATE TYPE alert_level_enum AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL');

CREATE TABLE alert_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    metric_id UUID NOT NULL REFERENCES metrics(id),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    rule_type alert_rule_type_enum NOT NULL,
    config JSONB,
    dimensions_filter JSONB,
    alert_level alert_level_enum NOT NULL,
    notification_channels JSONB,
    check_frequency_minutes INTEGER NOT NULL,
    status VARCHAR(50) DEFAULT 'ACTIVE',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 4. 预警事件（alert_events）

- 记录每次预警触发的详细信息、处理状态

**表结构设计：**
```sql
CREATE TYPE alert_status_enum AS ENUM ('OPEN', 'ACKNOWLEDGED', 'RESOLVED');

CREATE TABLE alert_events (
    id BIGSERIAL PRIMARY KEY,
    rule_id UUID NOT NULL REFERENCES alert_rules(id),
    metric_id UUID NOT NULL REFERENCES metrics(id),
    event_time TIMESTAMP WITH TIME ZONE NOT NULL,
    actual_value NUMERIC(18, 6),
    triggered_condition TEXT,
    dimensions_context JSONB,
    alert_level alert_level_enum,
    status alert_status_enum DEFAULT 'OPEN',
    resolved_by VARCHAR(100),
    resolved_at TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
CREATE INDEX idx_alert_events_metric_time ON alert_events (metric_id, event_time DESC);
```

### 5. AI模型管理（ai_models）

- 记录每个指标关联的AI模型、API接口、训练参数等

**表结构设计：**
```sql
CREATE TABLE ai_models (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    metric_id UUID NOT NULL REFERENCES metrics(id),
    model_name VARCHAR(255) NOT NULL,
    model_type VARCHAR(100) NOT NULL,
    external_api_endpoint TEXT NOT NULL,
    training_params JSONB,
    evaluation_metrics JSONB,
    last_trained_at TIMESTAMP WITH TIME ZONE,
    next_retrain_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(50) DEFAULT 'ACTIVE',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 6. AI预测结果（ai_predictions）

- 存储AI模型的预测结果、置信区间、维度上下文

**表结构设计：**
```sql
CREATE TABLE ai_predictions (
    id BIGSERIAL PRIMARY KEY,
    metric_id UUID NOT NULL REFERENCES metrics(id),
    model_id UUID NOT NULL REFERENCES ai_models(id),
    prediction_time TIMESTAMP WITH TIME ZONE NOT NULL,
    predicted_value NUMERIC(18, 6) NOT NULL,
    lower_bound NUMERIC(18, 6),
    upper_bound NUMERIC(18, 6),
    dimensions JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
CREATE INDEX idx_ai_predictions_metric_time ON ai_predictions (metric_id, prediction_time DESC);
```

---

## 三、主要功能模块实现细节

### 1. 问数助手
- 支持自然语言对话查询财务指标、趋势、条件等，支持多轮对话。
- 查询结果以表格/图表展示，支持历史查询记录、分类检索。
- 数据动态更新，支持刷新提示。

### 2. 财务税费指标管控
- 数据导入：支持多源导入、校验、清洗。
- 费用管理：费用清单、下钻分析、同比环比、预测模型、监控与分析。
- 税款管理：纳税申报、税金计算、税负分析、合规与筹划建议。
- 财务知识库：制度、准则、政策、分析方法，支持检索与更新。
- 深度分析报告：定期生成，图表+文字，辅助决策。
- 可视化报表：多维度、趋势、分布、仪表盘。

### 3. 系统管理
- 用户、菜单、权限、日志统一管理，支持自定义菜单、精细化权限、日志追踪。

### 4. 指标管理
- 类型管理、指标启用/停用、筛选、查询、动态调整。

### 5. 异常检测与预警
- 异常规则管理、预警通知、历史记录、通知方式配置。

### 6. 大模型管理
- LLM模型管理：加载、更新、参数配置、切换。
- MCP管理：接口配置、权限、注册与发现、模块集成。

### 7. 数据采集与处理

- **采集任务**：支持定时/手动，按metrics.data_source_config配置采集外部数据（SQL、API等）。
- **数据清洗**：采集后进行格式转换、校验，存入metric_values。
- **复合指标计算**：支持定时任务和事件驱动两种方式，自动计算COMPOSITE类型指标。

### 8. AI预测与分析

- **AI集成模块**：定期或按需调用外部AI服务，传递历史数据，接收预测结果，存入ai_predictions。
- **分析解读**：支持异常归因、趋势解读等，结果可通知或前端展示。

### 9. 预警模块

- **定时检查**：按alert_rules.check_frequency_minutes定期检查指标数据。
- **规则判断**：支持多种预警类型，自动触发alert_events并通知相关人员。

### 10. 报表与可视化

- **前端查询**：Vue.js通过API获取metric_values、ai_predictions、alert_events等数据。
- **图表渲染**：前端采用ECharts等库，支持多维度、历史趋势、预警分布等可视化。

### 11. 通知服务

- **集成邮件、短信、微信/钉钉等**，支持多渠道通知。

---

## 四、技术选型

- **前端**：Vue.js（SPA单页应用，ECharts可视化）
- **后端**：Java/Spring Boot（与现有BladeX体系兼容，易于集成、维护）
- **数据库**：PostgreSQL（支持JSONB、复杂查询）
- **AI服务**：Python（Flask/Django，Scikit-learn、Prophet、TensorFlow等）
- **部署**：支持Docker容器化，或直接JAR包/PM2/Gunicorn等方式部署

---

## 五、与现有系统的集成与兼容性说明

- 本设计可与现有BladeX平台无缝集成，数据库表可独立或与现有表共存。
- 业务模块可作为BladeX的子模块开发，前端可通过路由集成到主界面。
- 未来如需微服务拆分，数据结构和接口设计已预留扩展空间。

---

## 六、非功能需求

- 性能：快速响应，支持大数据量处理。
- 安全：数据加密、权限认证、访问控制。
- 可用性：高可用、容错、7×24小时服务。
- 可维护性与可扩展性：模块化、易升级、接口预留。

---

## 七、后续开发与优化建议

1. **优先实现核心表结构和基础API，确保数据流通畅。**
2. **前后端接口严格遵循RESTful规范，接口文档同步维护。**
3. **AI服务可先用Mock数据，后续逐步对接真实模型。**
4. **预警、通知等可先实现基础功能，后续支持自定义扩展。**
5. **所有代码、配置、文档均需详细注释，便于维护和二次开发。**

---

如有任何不明白的地方，随时提问！后续所有开发、接口、表结构、功能实现都将以本设计为唯一标准。  
（本设计文档会持续迭代完善，所有变更都会在README和本文件同步记录。）

—— 项目产品经理 & 架构师

---

你可以直接在本页面提出任何问题或补充需求，我会主动帮你完善和落地。
