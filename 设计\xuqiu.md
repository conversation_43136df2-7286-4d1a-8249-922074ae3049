项目需求
1. 引言
1.1.1 目的
为阳江烟草 AI 大模型项目提供明确的需求定义，确保项目团队成员、用户及相关利益者对项目需求达成共识，作为后续系统设计、开发、测试和验收的依据。
1.1.2 背景
随着烟草行业竞争的加剧以及企业自身发展的需求，阳江烟草正处于数字化转型的关键时期，面临着日益增长的数据分析挑战。在企业内部运营层面，各部门亟需更加便捷、高效的数据获取与利用渠道，以此驱动决策的科学性与精准性，提升整体运营效能。
在此背景下，构建一个基于AI大模型技术的灵活数据分析平台显得尤为迫切。该平台旨在赋能全体员工，使其能够充分利用数据资源，挖掘数据价值，为企业决策提供有力支持；
此外，阳江烟草还面临管理部门难以有效获取和管控重点费用开支情况的问题。重点费用包括三公费用、研发费用、福利费等，这些费用的合理管控对于企业的成本控制和健康发展至关重要。当前，传统的方式难以满足深度分析和实时预警的需求，因此，希望通过引入人工智能技术，对上述重点费用指标进行深度分析，生成全面、精准的分析报告，同时能够及时对异常指标进行预警，助力管理层更好地把控费用开支，推动阳江烟草在数字化浪潮中实现高质量发展。
1.1.3 定义、缩略语和参考文献
列出文档中使用的专业术语、缩略语的定义，以及引用的参考文献，如相关行业标准、政策文件、已有的项目文档等。
2. 功能需求
2.1 简化问数助手
2.1.1 自然语言查询
- 用户能够以自然语言的方式，对预定义的几个关键财务指标进行查询，系统准确理解用户意图并返回相应的数据结果。
- 支持多轮对话模式，当用户的问题较为复杂或涉及上下文关联时，系统能够结合之前的对话内容进行准确回答。
- 支持的查询类型包括但不限于：
  - 指标查询：用户可以通过自然语言直接查询特定的财务指标，如“2025年第一季度的车辆运行费用是多少？”、“本月的会议费用同比增减情况”等。
  - 趋势查询：用户可以查询某项指标在一定时间范围内的变化趋势，如“最近半年的福利费用走势”、“近三年的维修趋势”等。
  - 条件查询：用户可以根据特定的条件进行查询，如“找出2024年同比增加20%以上的费用”等。
2.1.2 查询结果展示
- 以清晰、简洁的表格形式展示查询结果，同时提供必要的数据说明，如指标的计算口径、数据来源等。
- 对于复杂数据结果，提供可视化图表展示选项，包括柱状图、折线图、饼图等常见图表类型。主要用于趋势查询。
2.1.3 历史查询记录与管理
- 系统自动记录用户的查询历史，用户可以随时查看和回顾之前的查询记录，方便重复查询或查看历史数据的变化。
- 对查询记录进行分类整理，支持按照时间、指标类型、查询关键词等维度进行筛选和检索，方便用户快速找到所需的查询记录。
2.1.4 数据更新与实时性
- 系统能够动态获取和更新指标数据，确保用户查询到的是最新的数据信息。对于数据更新的时间频率，可根据实际业务需求进行设置，如定时更新、实时同步等。
- 当数据发生变化时，系统自动检测并提示用户数据已更新，用户可以选择刷新当前查询结果以获取最新的数据展示。
2.2 财务税费指标管控
2.2.1 数据导入
- 提供便捷的数据导入功能，支持从多种数据源（如Excel文件、数据库、财务系统等）导入财务数据，包括费用数据、税务数据等。在导入过程中，对数据进行校验和清洗，确保数据的准确性和完整性。
- 预留接口为后面对接数据共享中心。
2.2.2  费用管理
- 整体费用分析：直观展示所有财务费用清单，点击具体费用可以下钻进行细化各指标费用进行分析。可以同比环比查看。
- 费用预测模型：根据历史费用数据和相关的业务因素，建立费用预测模型，对未来的费用支出进行预测，帮助企业提前做好预算规划和成本控制。支持用户根据实际情况对预测模型进行调整和优化，以提高预测的准确性和可靠性。
- 具体各费用监控与分析：对各项费用指标进行实时监控，分析费用的支出情况、变化趋势以及与预算的执行情况。通过图表、报表等形式直观展示费用数据，帮助用户及时发现费用异常和潜在问题，并采取相应的措施进行管理和控制。
2.2.3  税款管理
- 对企业的税务数据进行集中管理，包括纳税申报数据、税金计算、税负分析等。提供税务相关的查询和报表功能，帮助用户及时了解税务状况，确保税务合规。同时，结合税收政策和企业的实际情况，为企业提供税务筹划和优化建议。
2.2.4  财务知识库管理
- 建立和维护一个丰富的财务知识库，包括财务制度、会计准则、税收政策、财务分析方法等内容。用户可以在知识库中搜索和查询相关的财务知识，获取专业的指导和参考，提高财务管理和分析的水平。支持对知识库内容的更新和完善，确保知识的时效性和准确性。
2.2.5 深度分析报告
- 定期生成财务税费深度分析报告，内容涵盖费用分析、税务分析、财务指标分析等方面。报告通过图表、文字等形式详细展示财务数据的现状、趋势和问题，并提供深入的分析和见解，为企业的决策提供有力支持。
2.2.6 可视化报表
- 建立和维护一个丰富的财务知识库，包括财务制度、会计准则、税收政策、财务分析方法等内容。用户可以在知识库中搜索和查询相关的财务知识，获取专业的指导和参考，提高财务管理和分析的水平。支持对知识库内容的更新和完善，确保知识的时效性和准确性。

2.2.7 资产负债表管理
- 提供资产负债表相关指标的完整列表展示，涵盖流动资产、非流动资产、流动负债、非流动负债、所有者权益等主要财务指标，具体指标参考《指标说明.txt》中的资产负债表指标部分。
- 用户可以浏览所有资产负债表指标，支持按分类、关键字等方式筛选和搜索指标。
- 支持对每个指标进行详细分析，包括当前值、历史数据、同比环比变化等。
- 用户可点击任意指标，查看该指标的历史趋势图（如折线图、柱状图等），直观了解指标的变化趋势。
- 系统集成AI智能解读功能，自动对指标趋势进行分析，生成趋势分析报告，识别潜在风险点，并给出针对性的风险评估和优化建议，帮助管理层及时发现问题并采取措施。
- 支持导出资产负债表分析报告，便于归档和汇报。

2.2.8 利润表管理
- 提供利润表相关指标的完整列表展示，涵盖营业收入、营业成本、营业利润、利润总额、净利润等主要财务指标，具体指标参考《指标说明.txt》中的利润表指标部分。
- 用户可以浏览所有利润表指标，支持按分类、关键字等方式筛选和搜索指标。
- 支持对每个利润表指标进行详细分析，包括当前值、历史数据、同比环比变化等。
- 用户可点击任意利润表指标，查看该指标的历史趋势图（如折线图、柱状图等），直观了解指标的变化趋势。
- 系统集成AI智能解读功能，自动对利润表各项指标的趋势进行分析，生成趋势分析报告，识别利润变动的主要驱动因素，进行风险评估，并给出提升盈利能力的优化建议。
- 支持导出利润表分析报告，便于归档和汇报。
2.3 系统管理
2.3.1 用户管理
- 对使用本系统的用户进行统一管理，包括用户注册、用户信息维护、用户状态管理等功能。
2.3.2 菜单管理
- 对系统的菜单进行配置和管理，根据用户的角色和权限，显示相应的菜单项和功能入口。支持自定义菜单的布局和顺序，方便用户快速找到所需的功能。
2.3.3 权限管理
- 建立完善的权限管理体系，对系统的功能模块、数据资源等进行精细化的权限控制。管理员可以根据用户的角色和工作需求，分配不同的权限，确保数据的安全性和保密性。
2.3.4 日志管理
- 记录系统运行过程中的各类操作日志和事件日志
2.4 指标管理
2.4.1 指标类型管理
- 对通用指标的类型进行分类和管理，定义不同类型指标的分类标准、命名规范、计算公式等属性。支持对指标类型的新增、修改、删除等操作，确保指标类型的完整性和准确性，为指标的管控提供基础。
2.4.2 指标管理
- 进行具体指标的创建、编辑、删除等管理工作。每个指标应包含明确的名称、定义、计算方法、数据来源、统计周期等信息。支持对指标的启用、停用状态进行控制，方便对指标体系进行动态调整和优化。同时，提供指标的查询和筛选功能，方便用户快速找到所需的指标。
2.5 异常检测管理
2.5.1 异常规则管理
- 允许用户根据企业的业务规则和管理要求，设定针对财务数据的异常检测规则。规则可以基于数据的阈值等条件进行定义，例如费用超支比例、税务数据异常等。支持对异常规则的启用、停用、修改等操作，确保异常检测的准确性和及时性。
2.5.2 预警管理
- 当系统根据异常检测规则发现财务数据异常时，通过预警管理功能及时通知相关人员。用户可以配置预警的通知方式（如站内消息、邮件、短信等）、接收人员、预警内容等信息。同时，对预警的历史记录进行查询和管理，方便跟踪异常问题的处理情况。
2.6 大模型管理
2.6.1 LLM模型管理
- 对系统中使用的LLM（Large Language Model）模型进行管理和维护，包括模型的加载、更新、参数配置等操作。确保LLM模型能够正常运行，并为AI智能助手等提供强大的自然语言处理能力支持。可根据实际应用需求，选择和切换不同的LLM模型，以满足不同的业务场景和性能要求。
2.6.2 MCP管理
- 负责对MCP（Model Control Protocol）相关的内容进行管理，包括MCP接口的配置、调用权限的设置、模型的注册与发现等功能。确保AI智能助手与其他功能模块之间通过MCP能够进行有效的通信和协作，实现数据的流转和功能的集成。
3. 非功能需求
3.1.1 性能需求
- 系统应具备快速响应能力，对于用户的查询请求，应在规定的时间内（如[X] 秒内）返回结果，确保用户能够高效地获取数据和分析报告。
- 在进行数据采集、分析和报告生成等操作时，系统应能够处理大规模的数据量（如明确具体的数据量范围），保证数据处理的效率和稳定性，不影响系统的正常运行。
3.1.2 安全需求
- 系统应具备严格的数据安全保障措施，确保财务数据的保密性、完整性和可用性。采用加密技术对数据传输和存储过程进行加密处理，防止数据泄露和篡改。
- 建立完善的用户认证和授权机制，根据用户的角色和权限，控制用户对系统的访问范围和操作权限，确保只有授权用户能够访问和操作相关的财务数据和功能模块。
3.1.3 可用性和可靠性需求
- 系统应具备高可用性，保证在规定的工作时间内（如 7×24 小时）稳定运行，提供持续的服务支持。通过采用冗余设计、负载均衡等技术手段，提高系统的可靠性和容错能力，确保在出现硬件故障、网络故障等异常情况下，系统能够快速恢复服务，避免数据丢失和业务中断。
3.1.4 可维护性和可扩展性需求
- 系统的架构设计应遵循模块化原则，各功能模块之间具有良好的独立性和可维护性，便于后续的系统维护、升级和功能扩展。在项目实施过程中，应充分考虑到系统的可扩展性，为未来增加新的财务指标、拓展新的分析功能或与其他系统的集成预留接口和空间。
4. 结论
总结本需求分析规格说明书的内容，强调需求分析在项目成功实施中的重要性，以及本说明书在指导项目开发过程中的关键作用，表明项目团队将依据本说明书开展后续的系统设计、开发、测试和验收工作，确保项目能够满足用户的期望和业务发展的需求。