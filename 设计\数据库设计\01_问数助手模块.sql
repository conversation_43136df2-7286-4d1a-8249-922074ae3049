-- =====================================================
-- 问数助手模块数据库表结构
-- 模块说明：支持自然语言查询、多轮对话、查询历史管理等功能
-- 创建时间：2024年
-- 数据库：PostgreSQL
-- =====================================================

-- =====================================================
-- 1. 查询历史表
-- =====================================================
CREATE TABLE yjzb_query_history (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    query_text TEXT NOT NULL,
    query_type VARCHAR(50),
    result_data JSONB,
    query_time TIMESTAMP(6) NOT NULL,
    response_time INTEGER,
    status VARCHAR(20) DEFAULT 'SUCCESS',
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6) DEFAULT NOW(),
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);

-- 添加表注释
COMMENT ON TABLE yjzb_query_history IS '查询历史表 - 存储用户查询记录和结果';

-- 添加字段注释
COMMENT ON COLUMN yjzb_query_history.id IS '主键ID';
COMMENT ON COLUMN yjzb_query_history.user_id IS '用户ID';
COMMENT ON COLUMN yjzb_query_history.query_text IS '查询文本内容';
COMMENT ON COLUMN yjzb_query_history.query_type IS '查询类型（指标查询、趋势查询、条件查询等）';
COMMENT ON COLUMN yjzb_query_history.result_data IS '查询结果数据（JSON格式）';
COMMENT ON COLUMN yjzb_query_history.query_time IS '查询时间';
COMMENT ON COLUMN yjzb_query_history.response_time IS '响应时间（毫秒）';
COMMENT ON COLUMN yjzb_query_history.status IS '查询状态（SUCCESS、FAILED、TIMEOUT）';
COMMENT ON COLUMN yjzb_query_history.create_user IS '创建人ID';
COMMENT ON COLUMN yjzb_query_history.create_dept IS '创建部门ID';
COMMENT ON COLUMN yjzb_query_history.create_time IS '创建时间';
COMMENT ON COLUMN yjzb_query_history.update_user IS '更新人ID';
COMMENT ON COLUMN yjzb_query_history.update_time IS '更新时间';
COMMENT ON COLUMN yjzb_query_history.status IS '数据状态（1-正常，0-禁用）';
COMMENT ON COLUMN yjzb_query_history.is_deleted IS '删除标记（0-未删除，1-已删除）';

-- =====================================================
-- 2. 对话记录表
-- =====================================================
CREATE TABLE yjzb_conversation_records (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    session_id VARCHAR(100) NOT NULL,
    message_type VARCHAR(20) NOT NULL,
    message_content TEXT NOT NULL,
    timestamp TIMESTAMP(6) DEFAULT NOW(),
    context_data JSONB,
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6) DEFAULT NOW(),
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);

-- 添加表注释
COMMENT ON TABLE yjzb_conversation_records IS '对话记录表 - 存储用户与AI助手的对话内容';

-- 添加字段注释
COMMENT ON COLUMN yjzb_conversation_records.id IS '主键ID';
COMMENT ON COLUMN yjzb_conversation_records.user_id IS '用户ID';
COMMENT ON COLUMN yjzb_conversation_records.session_id IS '会话ID';
COMMENT ON COLUMN yjzb_conversation_records.message_type IS '消息类型（USER-用户消息，ASSISTANT-AI助手回复）';
COMMENT ON COLUMN yjzb_conversation_records.message_content IS '消息内容';
COMMENT ON COLUMN yjzb_conversation_records.timestamp IS '消息时间戳';
COMMENT ON COLUMN yjzb_conversation_records.context_data IS '上下文数据（JSON格式）';
COMMENT ON COLUMN yjzb_conversation_records.create_user IS '创建人ID';
COMMENT ON COLUMN yjzb_conversation_records.create_dept IS '创建部门ID';
COMMENT ON COLUMN yjzb_conversation_records.create_time IS '创建时间';
COMMENT ON COLUMN yjzb_conversation_records.update_user IS '更新人ID';
COMMENT ON COLUMN yjzb_conversation_records.update_time IS '更新时间';
COMMENT ON COLUMN yjzb_conversation_records.status IS '数据状态（1-正常，0-禁用）';
COMMENT ON COLUMN yjzb_conversation_records.is_deleted IS '删除标记（0-未删除，1-已删除）';

-- =====================================================
-- 3. 查询分类表
-- =====================================================
CREATE TABLE yjzb_query_categories (
    id BIGSERIAL PRIMARY KEY,
    category_name VARCHAR(100) NOT NULL,
    category_code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    parent_id BIGINT REFERENCES yjzb_query_categories(id),
    sort_order INTEGER DEFAULT 0,
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6) DEFAULT NOW(),
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);

-- 添加表注释
COMMENT ON TABLE yjzb_query_categories IS '查询分类表 - 管理查询类型分类';

-- 添加字段注释
COMMENT ON COLUMN yjzb_query_categories.id IS '主键ID';
COMMENT ON COLUMN yjzb_query_categories.category_name IS '分类名称';
COMMENT ON COLUMN yjzb_query_categories.category_code IS '分类编码（唯一）';
COMMENT ON COLUMN yjzb_query_categories.description IS '分类描述';
COMMENT ON COLUMN yjzb_query_categories.parent_id IS '父分类ID（支持层级分类）';
COMMENT ON COLUMN yjzb_query_categories.sort_order IS '排序顺序';
COMMENT ON COLUMN yjzb_query_categories.create_user IS '创建人ID';
COMMENT ON COLUMN yjzb_query_categories.create_dept IS '创建部门ID';
COMMENT ON COLUMN yjzb_query_categories.create_time IS '创建时间';
COMMENT ON COLUMN yjzb_query_categories.update_user IS '更新人ID';
COMMENT ON COLUMN yjzb_query_categories.update_time IS '更新时间';
COMMENT ON COLUMN yjzb_query_categories.status IS '数据状态（1-正常，0-禁用）';
COMMENT ON COLUMN yjzb_query_categories.is_deleted IS '删除标记（0-未删除，1-已删除）';

-- =====================================================
-- 4. 创建索引
-- =====================================================

-- 查询历史表索引
CREATE INDEX idx_yjzb_query_history_user_time ON yjzb_query_history(user_id, query_time DESC);
CREATE INDEX idx_yjzb_query_history_type ON yjzb_query_history(query_type);

-- 对话记录表索引
CREATE INDEX idx_yjzb_conversation_records_session ON yjzb_conversation_records(session_id, timestamp);
CREATE INDEX idx_yjzb_conversation_records_user ON yjzb_conversation_records(user_id);

-- =====================================================
-- 5. 初始化基础数据
-- =====================================================

-- 插入查询分类基础数据
INSERT INTO yjzb_query_categories (category_name, category_code, description, sort_order, create_user, create_dept, create_time, status, is_deleted) VALUES
('指标查询', 'INDICATOR_QUERY', '查询特定财务指标数值', 1, 1, 1, NOW(), 1, 0),
('趋势查询', 'TREND_QUERY', '查询指标变化趋势', 2, 1, 1, NOW(), 1, 0),
('条件查询', 'CONDITION_QUERY', '根据条件筛选数据', 3, 1, 1, NOW(), 1, 0),
('对比查询', 'COMPARISON_QUERY', '同比环比等对比分析', 4, 1, 1, NOW(), 1, 0);

-- =====================================================
-- 问数助手模块表结构创建完成
-- ===================================================== 