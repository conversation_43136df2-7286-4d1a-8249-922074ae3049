-- =====================================================
-- 异常检测管理模块数据库表结构
-- 模块说明：异常规则管理、预警管理等功能
-- 创建时间：2024年
-- 数据库：PostgreSQL
-- =====================================================

-- =====================================================
-- 1. 异常规则表
-- =====================================================
CREATE TABLE yjzb_exception_rules (
    id BIGSERIAL PRIMARY KEY,
    rule_name VARCHAR(200) NOT NULL,
    rule_type VARCHAR(50) NOT NULL,
    indicator_id BIGINT REFERENCES yjzb_indicator_types(id),
    threshold_value NUMERIC(18,6),
    comparison_operator VARCHAR(10),
    alert_level VARCHAR(20) NOT NULL,
    notification_channels JSONB,
    check_frequency_minutes INTEGER DEFAULT 60,
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6) DEFAULT NOW(),
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);

-- 添加表注释
COMMENT ON TABLE yjzb_exception_rules IS '异常规则表 - 定义异常检测规则和预警条件';

-- 添加字段注释
COMMENT ON COLUMN yjzb_exception_rules.id IS '主键ID';
COMMENT ON COLUMN yjzb_exception_rules.rule_name IS '规则名称';
COMMENT ON COLUMN yjzb_exception_rules.rule_type IS '规则类型（THRESHOLD-阈值、FLUCTUATION-波动、TREND-趋势、ANOMALY-异常）';
COMMENT ON COLUMN yjzb_exception_rules.indicator_id IS '关联指标ID（关联指标类型表）';
COMMENT ON COLUMN yjzb_exception_rules.threshold_value IS '阈值数值';
COMMENT ON COLUMN yjzb_exception_rules.comparison_operator IS '比较操作符（>、<、>=、<=、=、!=）';
COMMENT ON COLUMN yjzb_exception_rules.alert_level IS '预警级别（LOW-低、MEDIUM-中、HIGH-高、CRITICAL-严重）';
COMMENT ON COLUMN yjzb_exception_rules.notification_channels IS '通知渠道配置（JSON格式，包含邮件、短信、微信等）';
COMMENT ON COLUMN yjzb_exception_rules.check_frequency_minutes IS '检查频率（分钟）';
COMMENT ON COLUMN yjzb_exception_rules.create_user IS '创建人ID';
COMMENT ON COLUMN yjzb_exception_rules.create_dept IS '创建部门ID';
COMMENT ON COLUMN yjzb_exception_rules.create_time IS '创建时间';
COMMENT ON COLUMN yjzb_exception_rules.update_user IS '更新人ID';
COMMENT ON COLUMN yjzb_exception_rules.update_time IS '更新时间';
COMMENT ON COLUMN yjzb_exception_rules.status IS '数据状态（1-正常，0-禁用）';
COMMENT ON COLUMN yjzb_exception_rules.is_deleted IS '删除标记（0-未删除，1-已删除）';

-- =====================================================
-- 2. 预警事件表
-- =====================================================
CREATE TABLE yjzb_alert_events (
    id BIGSERIAL PRIMARY KEY,
    rule_id BIGINT NOT NULL REFERENCES yjzb_exception_rules(id),
    indicator_id BIGINT NOT NULL REFERENCES yjzb_indicator_types(id),
    event_time TIMESTAMP(6) NOT NULL,
    actual_value NUMERIC(18,6),
    triggered_condition TEXT,
    alert_level VARCHAR(20) NOT NULL,
    status VARCHAR(20) DEFAULT 'OPEN',
    resolved_by VARCHAR(100),
    resolved_at TIMESTAMP(6),
    notes TEXT,
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6) DEFAULT NOW(),
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);

-- 添加表注释
COMMENT ON TABLE yjzb_alert_events IS '预警事件表 - 记录触发的预警事件和处理状态';

-- 添加字段注释
COMMENT ON COLUMN yjzb_alert_events.id IS '主键ID';
COMMENT ON COLUMN yjzb_alert_events.rule_id IS '规则ID（关联异常规则表）';
COMMENT ON COLUMN yjzb_alert_events.indicator_id IS '指标ID（关联指标类型表）';
COMMENT ON COLUMN yjzb_alert_events.event_time IS '事件发生时间';
COMMENT ON COLUMN yjzb_alert_events.actual_value IS '实际数值';
COMMENT ON COLUMN yjzb_alert_events.triggered_condition IS '触发条件描述';
COMMENT ON COLUMN yjzb_alert_events.alert_level IS '预警级别（LOW-低、MEDIUM-中、HIGH-高、CRITICAL-严重）';
COMMENT ON COLUMN yjzb_alert_events.status IS '事件状态（OPEN-未处理、ACKNOWLEDGED-已确认、RESOLVED-已解决）';
COMMENT ON COLUMN yjzb_alert_events.resolved_by IS '处理人';
COMMENT ON COLUMN yjzb_alert_events.resolved_at IS '处理时间';
COMMENT ON COLUMN yjzb_alert_events.notes IS '处理备注';
COMMENT ON COLUMN yjzb_alert_events.create_user IS '创建人ID';
COMMENT ON COLUMN yjzb_alert_events.create_dept IS '创建部门ID';
COMMENT ON COLUMN yjzb_alert_events.create_time IS '创建时间';
COMMENT ON COLUMN yjzb_alert_events.update_user IS '更新人ID';
COMMENT ON COLUMN yjzb_alert_events.update_time IS '更新时间';
COMMENT ON COLUMN yjzb_alert_events.status IS '数据状态（1-正常，0-禁用）';
COMMENT ON COLUMN yjzb_alert_events.is_deleted IS '删除标记（0-未删除，1-已删除）';

-- =====================================================
-- 3. 创建索引
-- =====================================================

-- 预警事件表索引
CREATE INDEX idx_yjzb_alert_events_rule_time ON yjzb_alert_events(rule_id, event_time DESC);
CREATE INDEX idx_yjzb_alert_events_indicator_time ON yjzb_alert_events(indicator_id, event_time DESC);
CREATE INDEX idx_yjzb_alert_events_status ON yjzb_alert_events(status);
CREATE INDEX idx_yjzb_alert_events_alert_level ON yjzb_alert_events(alert_level);

-- 异常规则表索引
CREATE INDEX idx_yjzb_exception_rules_indicator ON yjzb_exception_rules(indicator_id);
CREATE INDEX idx_yjzb_exception_rules_type ON yjzb_exception_rules(rule_type);
CREATE INDEX idx_yjzb_exception_rules_status ON yjzb_exception_rules(status);

-- =====================================================
-- 异常检测管理模块表结构创建完成
-- ===================================================== 