-- =====================================================
-- 大模型管理模块数据库表结构
-- 模块说明：LLM模型管理、MCP管理、AI预测结果管理等功能
-- 创建时间：2024年
-- 数据库：PostgreSQL
-- =====================================================

-- =====================================================
-- 1. LLM模型表
-- =====================================================
CREATE TABLE yjzb_llm_models (
    id BIGSERIAL PRIMARY KEY,
    model_name VARCHAR(200) NOT NULL,
    model_type VARCHAR(100) NOT NULL,
    model_version VARCHAR(50),
    api_endpoint TEXT,
    api_key TEXT,
    parameters JSONB,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6) DEFAULT NOW(),
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);

-- 添加表注释
COMMENT ON TABLE yjzb_llm_models IS 'LLM模型表 - 管理大语言模型配置信息';

-- 添加字段注释
COMMENT ON COLUMN yjzb_llm_models.id IS '主键ID';
COMMENT ON COLUMN yjzb_llm_models.model_name IS '模型名称';
COMMENT ON COLUMN yjzb_llm_models.model_type IS '模型类型（如：GPT、BERT、LLaMA等）';
COMMENT ON COLUMN yjzb_llm_models.model_version IS '模型版本号';
COMMENT ON COLUMN yjzb_llm_models.api_endpoint IS 'API接口地址';
COMMENT ON COLUMN yjzb_llm_models.api_key IS 'API密钥';
COMMENT ON COLUMN yjzb_llm_models.parameters IS '模型参数配置（JSON格式）';
COMMENT ON COLUMN yjzb_llm_models.status IS '模型状态（ACTIVE-激活、INACTIVE-停用、MAINTENANCE-维护中）';
COMMENT ON COLUMN yjzb_llm_models.create_user IS '创建人ID';
COMMENT ON COLUMN yjzb_llm_models.create_dept IS '创建部门ID';
COMMENT ON COLUMN yjzb_llm_models.create_time IS '创建时间';
COMMENT ON COLUMN yjzb_llm_models.update_user IS '更新人ID';
COMMENT ON COLUMN yjzb_llm_models.update_time IS '更新时间';
COMMENT ON COLUMN yjzb_llm_models.status IS '数据状态（1-正常，0-禁用）';
COMMENT ON COLUMN yjzb_llm_models.is_deleted IS '删除标记（0-未删除，1-已删除）';

-- =====================================================
-- 2. MCP配置表
-- =====================================================
CREATE TABLE yjzb_mcp_configs (
    id BIGSERIAL PRIMARY KEY,
    config_name VARCHAR(200) NOT NULL,
    config_type VARCHAR(100) NOT NULL,
    endpoint_url TEXT,
    authentication_info JSONB,
    permissions JSONB,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6) DEFAULT NOW(),
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);

-- 添加表注释
COMMENT ON TABLE yjzb_mcp_configs IS 'MCP配置表 - 管理模型控制协议配置';

-- 添加字段注释
COMMENT ON COLUMN yjzb_mcp_configs.id IS '主键ID';
COMMENT ON COLUMN yjzb_mcp_configs.config_name IS '配置名称';
COMMENT ON COLUMN yjzb_mcp_configs.config_type IS '配置类型（如：SERVER、CLIENT、TOOL等）';
COMMENT ON COLUMN yjzb_mcp_configs.endpoint_url IS '端点URL地址';
COMMENT ON COLUMN yjzb_mcp_configs.authentication_info IS '认证信息（JSON格式）';
COMMENT ON COLUMN yjzb_mcp_configs.permissions IS '权限配置（JSON格式）';
COMMENT ON COLUMN yjzb_mcp_configs.status IS '配置状态（ACTIVE-激活、INACTIVE-停用）';
COMMENT ON COLUMN yjzb_mcp_configs.create_user IS '创建人ID';
COMMENT ON COLUMN yjzb_mcp_configs.create_dept IS '创建部门ID';
COMMENT ON COLUMN yjzb_mcp_configs.create_time IS '创建时间';
COMMENT ON COLUMN yjzb_mcp_configs.update_user IS '更新人ID';
COMMENT ON COLUMN yjzb_mcp_configs.update_time IS '更新时间';
COMMENT ON COLUMN yjzb_mcp_configs.status IS '数据状态（1-正常，0-禁用）';
COMMENT ON COLUMN yjzb_mcp_configs.is_deleted IS '删除标记（0-未删除，1-已删除）';

-- =====================================================
-- 3. AI预测结果表
-- =====================================================
CREATE TABLE yjzb_ai_predictions (
    id BIGSERIAL PRIMARY KEY,
    model_id BIGINT NOT NULL REFERENCES yjzb_llm_models(id),
    indicator_id BIGINT NOT NULL REFERENCES yjzb_indicator_types(id),
    prediction_period VARCHAR(20) NOT NULL,
    predicted_value NUMERIC(18,6) NOT NULL,
    confidence_interval JSONB,
    prediction_date TIMESTAMP(6) DEFAULT NOW(),
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6) DEFAULT NOW(),
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);

-- 添加表注释
COMMENT ON TABLE yjzb_ai_predictions IS 'AI预测结果表 - 存储AI模型的预测结果';

-- 添加字段注释
COMMENT ON COLUMN yjzb_ai_predictions.id IS '主键ID';
COMMENT ON COLUMN yjzb_ai_predictions.model_id IS '模型ID（关联LLM模型表）';
COMMENT ON COLUMN yjzb_ai_predictions.indicator_id IS '指标ID（关联指标类型表）';
COMMENT ON COLUMN yjzb_ai_predictions.prediction_period IS '预测期间（格式：YYYY-MM）';
COMMENT ON COLUMN yjzb_ai_predictions.predicted_value IS '预测数值';
COMMENT ON COLUMN yjzb_ai_predictions.confidence_interval IS '置信区间（JSON格式，包含上下限）';
COMMENT ON COLUMN yjzb_ai_predictions.prediction_date IS '预测生成日期';
COMMENT ON COLUMN yjzb_ai_predictions.create_user IS '创建人ID';
COMMENT ON COLUMN yjzb_ai_predictions.create_dept IS '创建部门ID';
COMMENT ON COLUMN yjzb_ai_predictions.create_time IS '创建时间';
COMMENT ON COLUMN yjzb_ai_predictions.update_user IS '更新人ID';
COMMENT ON COLUMN yjzb_ai_predictions.update_time IS '更新时间';
COMMENT ON COLUMN yjzb_ai_predictions.status IS '数据状态（1-正常，0-禁用）';
COMMENT ON COLUMN yjzb_ai_predictions.is_deleted IS '删除标记（0-未删除，1-已删除）';

-- =====================================================
-- 4. 创建索引
-- =====================================================

-- AI预测结果表索引
CREATE INDEX idx_yjzb_ai_predictions_model_indicator ON yjzb_ai_predictions(model_id, indicator_id);
CREATE INDEX idx_yjzb_ai_predictions_period ON yjzb_ai_predictions(prediction_period);
CREATE INDEX idx_yjzb_ai_predictions_date ON yjzb_ai_predictions(prediction_date DESC);

-- LLM模型表索引
CREATE INDEX idx_yjzb_llm_models_type ON yjzb_llm_models(model_type);
CREATE INDEX idx_yjzb_llm_models_status ON yjzb_llm_models(status);

-- MCP配置表索引
CREATE INDEX idx_yjzb_mcp_configs_type ON yjzb_mcp_configs(config_type);
CREATE INDEX idx_yjzb_mcp_configs_status ON yjzb_mcp_configs(status);

-- =====================================================
-- 大模型管理模块表结构创建完成
-- ===================================================== 