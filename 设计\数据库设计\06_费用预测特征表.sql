-- 阳江AI指标管控系统 - 费用预测特征表与计算视图（PostgreSQL）
--
-- 目的：为费用预测模型提供标准化特征数据结构。
-- 特征包含：
--  - 当前指标数据（current_value）
--  - 滞后项：lag1、lag2、lag3
--  - 移动平均：ma3（含当前月在内的近3个月平均）
--  - 去年同期数（same_period_last_year_value，等同于lag12）
--  - 当年累计数（current_year_cumulative_value）
--  - 当年预算数（annual_budget_value，优先使用 midyear_budget 其次 initial_budget）
--
-- 说明：
-- 1) 本文件基于 PostgreSQL 设计，period 字段使用 'YYYY-MM' 规范。
-- 2) 依赖表：
--    - yjzb_indicator_values(indicator_id, period, value, ...)
--    - yjzb_indicator_annual_budget(indicator_id, year, initial_budget, midyear_budget, ...)
-- 3) 如需按需物化，可将视图改为物化视图并定时刷新。

-- ============================================================
-- 一、建表：yjzb_fee_forecast_features（用于持久化存储）
-- ============================================================
create table if not exists yjzb_fee_forecast_features (
    id                               bigserial primary key,
    indicator_id                     bigint not null,
    period                           varchar(20) not null, -- YYYY-MM
    current_value                    numeric(18,6),
    lag1_value                       numeric(18,6),
    lag2_value                       numeric(18,6),
    lag3_value                       numeric(18,6),
    ma3_value                        numeric(18,6),
    same_period_last_year_value      numeric(18,6), -- 去年同期，等价于 lag12
    current_year_cumulative_value    numeric(18,6),
    annual_budget_value              numeric(18,6),

    -- 审计与状态字段（与项目通用规范一致）
    create_user                      bigint,
    create_dept                      bigint,
    create_time                      timestamp(6) default now(),
    update_user                      bigint,
    update_time                      timestamp(6),
    status                           int default 1,
    is_deleted                       int default 0,

    constraint uk_yjzb_fee_features unique (indicator_id, period)
);

comment on table yjzb_fee_forecast_features is '费用预测特征表：为每个指标在每个月生成特征数据';
comment on column yjzb_fee_forecast_features.indicator_id is '指标ID（关联 yjzb_indicator.id）';
comment on column yjzb_fee_forecast_features.period is '数据期间（YYYY-MM）';
comment on column yjzb_fee_forecast_features.current_value is '当月指标值';
comment on column yjzb_fee_forecast_features.lag1_value is '上月指标值';
comment on column yjzb_fee_forecast_features.lag2_value is '上2月指标值';
comment on column yjzb_fee_forecast_features.lag3_value is '上3月指标值';
comment on column yjzb_fee_forecast_features.ma3_value is '近3个月移动平均（含当前月）';
comment on column yjzb_fee_forecast_features.same_period_last_year_value is '去年同期指标值（lag12）';
comment on column yjzb_fee_forecast_features.current_year_cumulative_value is '当年累计值（从1月到当前月累加）';
comment on column yjzb_fee_forecast_features.annual_budget_value is '当年预算（优先midyear_budget，其次initial_budget）';

create index if not exists idx_yjzb_fee_features_indicator on yjzb_fee_forecast_features(indicator_id);
create index if not exists idx_yjzb_fee_features_period on yjzb_fee_forecast_features(period);

-- 可选：若已存在指标主表，可建立外键（如未创建，可注释掉）
-- alter table yjzb_fee_forecast_features
--   add constraint fk_fee_features_indicator
--   foreign key (indicator_id) references yjzb_indicator(id) on delete cascade;


-- ============================================================
-- 二、计算视图：v_yjzb_fee_forecast_features（从原始表实时计算）
-- ============================================================
drop view if exists v_yjzb_fee_forecast_features;
create view v_yjzb_fee_forecast_features as
with iv as (
    select
        iv.indicator_id,
        iv.period,
        (to_date(iv.period || '-01', 'YYYY-MM-DD')) as month_date,
        extract(year from to_date(iv.period || '-01', 'YYYY-MM-DD'))::int as year_num,
        extract(month from to_date(iv.period || '-01', 'YYYY-MM-DD'))::int as month_num,
        iv.value as current_value
    from yjzb_indicator_values iv
    where coalesce(iv.is_deleted, 0) = 0
),
lagged as (
    select
        indicator_id,
        period,
        month_date,
        year_num,
        month_num,
        current_value,
        lag(current_value, 1)  over (partition by indicator_id order by month_date) as lag1_value,
        lag(current_value, 2)  over (partition by indicator_id order by month_date) as lag2_value,
        lag(current_value, 3)  over (partition by indicator_id order by month_date) as lag3_value,
        avg(current_value)     over (partition by indicator_id order by month_date rows between 2 preceding and current row) as ma3_value,
        lag(current_value, 12) over (partition by indicator_id order by month_date) as same_period_last_year_value,
        sum(current_value)     over (partition by indicator_id, year_num order by month_date rows between unbounded preceding and current row) as current_year_cumulative_value
    from iv
)
select
    l.indicator_id,
    l.period,
    l.current_value,
    l.lag1_value,
    l.lag2_value,
    l.lag3_value,
    l.ma3_value,
    l.same_period_last_year_value,
    l.current_year_cumulative_value,
    coalesce(b.midyear_budget, b.initial_budget, 0)::numeric(18,6) as annual_budget_value
from lagged l
left join yjzb_indicator_annual_budget b
  on b.indicator_id = l.indicator_id
 and b.year = l.year_num
where 1=1;

comment on view v_yjzb_fee_forecast_features is '费用预测特征视图：基于原始指标值与年度预算实时计算派生特征';


-- ============================================================
-- 三、一次性回填示例：将视图结果灌入持久化表（可重复执行，冲突时更新）
--    注意：生产中建议使用定时任务或存储过程刷新。
-- ============================================================
insert into yjzb_fee_forecast_features (
    indicator_id, period, current_value, lag1_value, lag2_value, lag3_value,
    ma3_value, same_period_last_year_value, current_year_cumulative_value, annual_budget_value,
    create_time, status, is_deleted
) 
select 
    v.indicator_id,
    v.period,
    v.current_value,
    v.lag1_value,
    v.lag2_value,
    v.lag3_value,
    v.ma3_value,
    v.same_period_last_year_value,
    v.current_year_cumulative_value,
    v.annual_budget_value,
    now(),
    1,
    0
from v_yjzb_fee_forecast_features v
on conflict (indicator_id, period) do update set
    current_value                 = excluded.current_value,
    lag1_value                    = excluded.lag1_value,
    lag2_value                    = excluded.lag2_value,
    lag3_value                    = excluded.lag3_value,
    ma3_value                     = excluded.ma3_value,
    same_period_last_year_value   = excluded.same_period_last_year_value,
    current_year_cumulative_value = excluded.current_year_cumulative_value,
    annual_budget_value           = excluded.annual_budget_value,
    update_time                   = now();


-- 使用提示：
-- 1) 先确保 yjzb_indicator_values / yjzb_indicator_annual_budget 已有数据；
-- 2) 执行本 SQL 文件；
-- 3) 如需仅使用实时计算，可直接查询视图 v_yjzb_fee_forecast_features 而无需回填表；
-- 4) 若需物化：
--    create materialized view mv_yjzb_fee_forecast_features as select * from v_yjzb_fee_forecast_features;
--    并通过 cron/调度刷新： refresh materialized view concurrently mv_yjzb_fee_forecast_features;


