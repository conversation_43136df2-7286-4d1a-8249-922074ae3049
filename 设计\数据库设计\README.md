# 阳江AI指标管控系统 - 数据库设计文档

## 概述

本文件夹包含阳江AI指标管控系统的完整数据库设计，按功能模块拆分为独立的SQL文件，便于管理和维护。

## 文件结构

```
数据库设计/
├── README.md                           # 本说明文档
├── 01_问数助手模块.sql                  # 问数助手相关表结构
├── 02_财务税费指标管控模块.sql          # 财务税费指标管控相关表结构
├── 03_指标管理模块.sql                  # 指标管理相关表结构
├── 04_异常检测管理模块.sql              # 异常检测管理相关表结构
├── 05_大模型管理模块.sql                # 大模型管理相关表结构
└── 06_费用预测特征表.sql                # 费用预测模型特征表与计算视图
```

## 模块说明

### 1. 问数助手模块 (01_问数助手模块.sql)
- **功能**：支持自然语言查询、多轮对话、查询历史管理
- **包含表**：
  - `yjzb_query_history` - 查询历史表
  - `yjzb_conversation_records` - 对话记录表
  - `yjzb_query_categories` - 查询分类表

### 2. 财务税费指标管控模块 (02_财务税费指标管控模块.sql)
- **功能**：费用管理、税务管理、财务知识库、分析报告、资产负债表、利润表
- **包含表**：
  - `yjzb_expense_data` - 费用数据表
  - `yjzb_tax_data` - 税务数据表
  - `yjzb_finance_knowledge` - 财务知识库表
  - `yjzb_analysis_reports` - 分析报告表
  - `yjzb_balance_sheet_data` - 资产负债表数据表
  - `yjzb_income_statement_data` - 利润表数据表

### 3. 指标管理模块 (03_指标管理模块.sql)
- **功能**：指标类型管理、指标数据管理
- **包含表**：
  - `yjzb_indicator_types` - 指标类型表
  - `yjzb_indicator_values` - 指标数据表

### 4. 异常检测管理模块 (04_异常检测管理模块.sql)
- **功能**：异常规则管理、预警管理
- **包含表**：
  - `yjzb_exception_rules` - 异常规则表
  - `yjzb_alert_events` - 预警事件表

### 5. 大模型管理模块 (05_大模型管理模块.sql)
- **功能**：LLM模型管理、MCP管理、AI预测结果管理
- **包含表**：
  - `yjzb_llm_models` - LLM模型表
  - `yjzb_mcp_configs` - MCP配置表
  - `yjzb_ai_predictions` - AI预测结果表

### 6. 费用预测特征表 (06_费用预测特征表.sql)
- **功能**：基于现有 `yjzb_indicator_values` 与 `yjzb_indicator_annual_budget` 生成预测所需特征
- **包含对象**：
  - 表：`yjzb_fee_forecast_features`（持久化特征表，含以下字段）
    - `current_value`（当前指标数据）
    - `lag1_value`/`lag2_value`/`lag3_value`
    - `ma3_value`（近3月移动平均，含当月）
    - `same_period_last_year_value`（去年同期，即 lag12）
    - `current_year_cumulative_value`（当年累计）
    - `annual_budget_value`（当年预算，优先中期预算）
  - 视图：`v_yjzb_fee_forecast_features`（从原始表实时计算）
  - 一次性回填语句：支持 UPSERT 到持久化表

## 数据库特性

### 技术栈
- **数据库**：PostgreSQL
- **版本**：支持PostgreSQL 12及以上版本

### 设计特点
1. **模块化设计**：按功能模块拆分，便于维护和扩展
2. **表名前缀**：所有表都使用 `yjzb_` 前缀，便于识别和管理
3. **完整注释**：每个表和字段都有详细的中文注释
4. **通用字段**：所有表都包含统一的审计字段
5. **索引优化**：为常用查询字段创建了合适的索引
6. **外键约束**：确保数据完整性和一致性
7. **JSONB支持**：充分利用PostgreSQL的JSONB特性

### 通用字段说明
所有表都包含以下通用字段：
- `create_user` - 创建人ID
- `create_dept` - 创建部门ID
- `create_time` - 创建时间
- `update_user` - 更新人ID
- `update_time` - 更新时间
- `status` - 数据状态（1-正常，0-禁用）
- `is_deleted` - 删除标记（0-未删除，1-已删除）

## 使用说明

### 执行顺序
建议按以下顺序执行SQL文件：
1. `03_指标管理模块.sql` - 先创建基础指标表
2. `01_问数助手模块.sql` - 问数助手功能
3. `02_财务税费指标管控模块.sql` - 财务数据管理
4. `04_异常检测管理模块.sql` - 异常检测功能
5. `05_大模型管理模块.sql` - AI模型管理
6. `06_费用预测特征表.sql` - 费用预测特征表与计算视图（依赖 03 与预算数据）

### 注意事项
1. 执行前请确保PostgreSQL数据库已正确安装和配置
2. 建议在测试环境中先执行，确认无误后再在生产环境执行
3. 执行前请备份现有数据（如有）
4. 确保数据库用户有足够的权限创建表、索引等对象

### 使用提示（特征表）
1. 确保 `yjzb_indicator_values` 与 `yjzb_indicator_annual_budget` 已写入数据；
2. 执行 `06_费用预测特征表.sql` 创建表、视图并进行一次性回填；
3. 如需实时计算，直接查询 `v_yjzb_fee_forecast_features`；
4. 如需物化以提升查询性能，可创建 `materialized view` 并通过任务定时刷新。

## 维护说明

### 版本控制
- 每次修改表结构时，请更新对应SQL文件
- 重大变更时，请创建新的版本文件
- 保持README.md文档的同步更新

### 扩展建议
1. 新增功能模块时，请创建独立的SQL文件
2. 文件命名格式：`XX_模块名称.sql`
3. 更新本README.md文档，添加新模块说明

## 联系信息

如有问题或建议，请联系项目开发团队。

---
*最后更新时间：2024年* 