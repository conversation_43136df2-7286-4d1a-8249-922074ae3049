/*
 Navicat Premium Data Transfer

 Source Server         : gzyc-nlp-2024.rwlb.rds.aliyuncs.com
 Source Server Type    : PostgreSQL
 Source Server Version : 140011
 Source Host           : gzyc-nlp-2024.rwlb.rds.aliyuncs.com:1921
 Source Catalog        : yjyc
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 140011
 File Encoding         : 65001

 Date: 08/07/2025 17:07:23
*/


-- ----------------------------
-- Table structure for metric_values
-- ----------------------------
DROP TABLE IF EXISTS "public"."metric_values";
CREATE TABLE "public"."metric_values" (
  "id" int8 NOT NULL DEFAULT nextval('metric_values_id_seq'::regclass),
  "metric_id" uuid NOT NULL,
  "timestamp" timestamptz(6) NOT NULL,
  "value" numeric(18,2) NOT NULL,
  "dimensions" jsonb,
  "data_source_info" jsonb,
  "is_calculated" bool DEFAULT false,
  "created_at" timestamptz(6) DEFAULT now(),
  "metric_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL
)
;

-- ----------------------------
-- Primary Key structure for table metric_values
-- ----------------------------
ALTER TABLE "public"."metric_values" ADD CONSTRAINT "metric_values_pkey" PRIMARY KEY ("id");
