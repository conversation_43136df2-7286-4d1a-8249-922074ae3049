INSERT INTO "public"."metric_values" (
  metric_id, timestamp, value, dimensions, data_source_info, is_calculated, created_at, metric_name
) VALUES
('b1a1e1c0-0001-0000-0000-000000000001', '2024-01-01 00:00:00+08', 128532.45, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '利润总额'),
('b1a1e1c0-0002-0000-0000-000000000001', '2024-01-01 00:00:00+08', 34567.89, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '税费'),
('b1a1e1c0-0003-0000-0000-000000000001', '2024-01-01 00:00:00+08', 523456.12, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售数量'),
('b1a1e1c0-0004-0000-0000-000000000001', '2024-01-01 00:00:00+08', 845678.23, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售收入'),
('b1a1e1c0-0005-0000-0000-000000000001', '2024-01-01 00:00:00+08', 678901.34, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售成本'),
('b1a1e1c0-0001-0000-0000-000000000002', '2024-02-01 00:00:00+08', 132145.67, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '利润总额'),
('b1a1e1c0-0002-0000-0000-000000000002', '2024-02-01 00:00:00+08', 35234.56, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '税费'),
('b1a1e1c0-0003-0000-0000-000000000002', '2024-02-01 00:00:00+08', 534567.23, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售数量'),
('b1a1e1c0-0004-0000-0000-000000000002', '2024-02-01 00:00:00+08', 856789.34, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售收入'),
('b1a1e1c0-0005-0000-0000-000000000002', '2024-02-01 00:00:00+08', 689012.45, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售成本'),
('b1a1e1c0-0001-0000-0000-000000000003', '2024-03-01 00:00:00+08', 135678.89, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '利润总额'),
('b1a1e1c0-0002-0000-0000-000000000003', '2024-03-01 00:00:00+08', 35890.12, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '税费'),
('b1a1e1c0-0003-0000-0000-000000000003', '2024-03-01 00:00:00+08', 545678.34, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售数量'),
('b1a1e1c0-0004-0000-0000-000000000003', '2024-03-01 00:00:00+08', 867890.45, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售收入'),
('b1a1e1c0-0005-0000-0000-000000000003', '2024-03-01 00:00:00+08', 699123.56, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售成本'),
('b1a1e1c0-0001-0000-0000-000000000004', '2024-04-01 00:00:00+08', 140123.45, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '利润总额'),
('b1a1e1c0-0002-0000-0000-000000000004', '2024-04-01 00:00:00+08', 36543.21, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '税费'),
('b1a1e1c0-0003-0000-0000-000000000004', '2024-04-01 00:00:00+08', 556789.45, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售数量'),
('b1a1e1c0-0004-0000-0000-000000000004', '2024-04-01 00:00:00+08', 879012.56, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售收入'),
('b1a1e1c0-0005-0000-0000-000000000004', '2024-04-01 00:00:00+08', 709234.67, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售成本'),
('b1a1e1c0-0001-0000-0000-000000000005', '2024-05-01 00:00:00+08', 143456.78, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '利润总额'),
('b1a1e1c0-0002-0000-0000-000000000005', '2024-05-01 00:00:00+08', 37210.98, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '税费'),
('b1a1e1c0-0003-0000-0000-000000000005', '2024-05-01 00:00:00+08', 567890.56, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售数量'),
('b1a1e1c0-0004-0000-0000-000000000005', '2024-05-01 00:00:00+08', 890123.67, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售收入'),
('b1a1e1c0-0005-0000-0000-000000000005', '2024-05-01 00:00:00+08', 719345.78, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售成本'),
('b1a1e1c0-0001-0000-0000-000000000006', '2024-06-01 00:00:00+08', 146789.01, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '利润总额'),
('b1a1e1c0-0002-0000-0000-000000000006', '2024-06-01 00:00:00+08', 37878.75, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '税费'),
('b1a1e1c0-0003-0000-0000-000000000006', '2024-06-01 00:00:00+08', 578901.67, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售数量'),
('b1a1e1c0-0004-0000-0000-000000000006', '2024-06-01 00:00:00+08', 901234.78, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售收入'),
('b1a1e1c0-0005-0000-0000-000000000006', '2024-06-01 00:00:00+08', 729456.89, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售成本'),
('b1a1e1c0-0001-0000-0000-000000000007', '2024-07-01 00:00:00+08', 150123.45, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '利润总额'),
('b1a1e1c0-0002-0000-0000-000000000007', '2024-07-01 00:00:00+08', 38567.98, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '税费'),
('b1a1e1c0-0003-0000-0000-000000000007', '2024-07-01 00:00:00+08', 589012.78, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售数量'),
('b1a1e1c0-0004-0000-0000-000000000007', '2024-07-01 00:00:00+08', 912345.89, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售收入'),
('b1a1e1c0-0005-0000-0000-000000000007', '2024-07-01 00:00:00+08', 739567.01, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售成本'),
('b1a1e1c0-0001-0000-0000-000000000008', '2024-08-01 00:00:00+08', 153456.78, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '利润总额'),
('b1a1e1c0-0002-0000-0000-000000000008', '2024-08-01 00:00:00+08', 39210.87, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '税费'),
('b1a1e1c0-0003-0000-0000-000000000008', '2024-08-01 00:00:00+08', 598765.67, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售数量'),
('b1a1e1c0-0004-0000-0000-000000000008', '2024-08-01 00:00:00+08', 923456.90, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售收入'),
('b1a1e1c0-0005-0000-0000-000000000008', '2024-08-01 00:00:00+08', 749678.12, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售成本'),
('b1a1e1c0-0001-0000-0000-000000000009', '2024-09-01 00:00:00+08', 156789.01, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '利润总额'),
('b1a1e1c0-0002-0000-0000-000000000009', '2024-09-01 00:00:00+08', 39876.54, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '税费'),
('b1a1e1c0-0003-0000-0000-000000000009', '2024-09-01 00:00:00+08', 609012.78, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售数量'),
('b1a1e1c0-0004-0000-0000-000000000009', '2024-09-01 00:00:00+08', 934567.01, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售收入'),
('b1a1e1c0-0005-0000-0000-000000000009', '2024-09-01 00:00:00+08', 759789.23, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售成本'),
('b1a1e1c0-0001-0000-0000-000000000010', '2024-10-01 00:00:00+08', 160123.45, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '利润总额'),
('b1a1e1c0-0002-0000-0000-000000000010', '2024-10-01 00:00:00+08', 40543.21, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '税费'),
('b1a1e1c0-0003-0000-0000-000000000010', '2024-10-01 00:00:00+08', 619234.89, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售数量'),
('b1a1e1c0-0004-0000-0000-000000000010', '2024-10-01 00:00:00+08', 945678.12, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售收入'),
('b1a1e1c0-0005-0000-0000-000000000010', '2024-10-01 00:00:00+08', 769901.34, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售成本'),
('b1a1e1c0-0001-0000-0000-000000000011', '2024-11-01 00:00:00+08', 163456.78, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '利润总额'),
('b1a1e1c0-0002-0000-0000-000000000011', '2024-11-01 00:00:00+08', 41210.98, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '税费'),
('b1a1e1c0-0003-0000-0000-000000000011', '2024-11-01 00:00:00+08', 629345.90, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售数量'),
('b1a1e1c0-0004-0000-0000-000000000011', '2024-11-01 00:00:00+08', 956789.13, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售收入'),
('b1a1e1c0-0005-0000-0000-000000000011', '2024-11-01 00:00:00+08', 779923.45, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售成本'),
('b1a1e1c0-0001-0000-0000-000000000012', '2024-12-01 00:00:00+08', 166789.01, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '利润总额'),
('b1a1e1c0-0002-0000-0000-000000000012', '2024-12-01 00:00:00+08', 41878.75, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '税费'),
('b1a1e1c0-0003-0000-0000-000000000012', '2024-12-01 00:00:00+08', 639456.91, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售数量'),
('b1a1e1c0-0004-0000-0000-000000000012', '2024-12-01 00:00:00+08', 967890.14, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售收入'),
('b1a1e1c0-0005-0000-0000-000000000012', '2024-12-01 00:00:00+08', 789923.56, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售成本'),
('b1a1e1c0-0001-0000-0000-000000000013', '2025-01-01 00:00:00+08', 170123.45, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '利润总额'),
('b1a1e1c0-0002-0000-0000-000000000013', '2025-01-01 00:00:00+08', 42543.21, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '税费'),
('b1a1e1c0-0003-0000-0000-000000000013', '2025-01-01 00:00:00+08', 649567.01, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售数量'),
('b1a1e1c0-0004-0000-0000-000000000013', '2025-01-01 00:00:00+08', 978901.15, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售收入'),
('b1a1e1c0-0005-0000-0000-000000000013', '2025-01-01 00:00:00+08', 799934.67, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售成本'),
('b1a1e1c0-0001-0000-0000-000000000014', '2025-02-01 00:00:00+08', 173456.78, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '利润总额'),
('b1a1e1c0-0002-0000-0000-000000000014', '2025-02-01 00:00:00+08', 43210.98, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '税费'),
('b1a1e1c0-0003-0000-0000-000000000014', '2025-02-01 00:00:00+08', 659678.12, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售数量'),
('b1a1e1c0-0004-0000-0000-000000000014', '2025-02-01 00:00:00+08', 989012.16, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售收入'),
('b1a1e1c0-0005-0000-0000-000000000014', '2025-02-01 00:00:00+08', 809945.78, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售成本'),
('b1a1e1c0-0001-0000-0000-000000000015', '2025-03-01 00:00:00+08', 176789.01, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '利润总额'),
('b1a1e1c0-0002-0000-0000-000000000015', '2025-03-01 00:00:00+08', 43878.75, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '税费'),
('b1a1e1c0-0003-0000-0000-000000000015', '2025-03-01 00:00:00+08', 669789.13, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售数量'),
('b1a1e1c0-0004-0000-0000-000000000015', '2025-03-01 00:00:00+08', 999123.17, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售收入'),
('b1a1e1c0-0005-0000-0000-000000000015', '2025-03-01 00:00:00+08', 819956.89, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售成本'),
('b1a1e1c0-0001-0000-0000-000000000016', '2025-04-01 00:00:00+08', 180123.45, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '利润总额'),
('b1a1e1c0-0002-0000-0000-000000000016', '2025-04-01 00:00:00+08', 44543.21, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '税费'),
('b1a1e1c0-0003-0000-0000-000000000016', '2025-04-01 00:00:00+08', 679890.14, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售数量'),
('b1a1e1c0-0004-0000-0000-000000000016', '2025-04-01 00:00:00+08', 1009234.18, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售收入'),
('b1a1e1c0-0005-0000-0000-000000000016', '2025-04-01 00:00:00+08', 829967.01, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售成本'),
('b1a1e1c0-0001-0000-0000-000000000017', '2025-05-01 00:00:00+08', 183456.78, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '利润总额'),
('b1a1e1c0-0002-0000-0000-000000000017', '2025-05-01 00:00:00+08', 45210.98, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '税费'),
('b1a1e1c0-0003-0000-0000-000000000017', '2025-05-01 00:00:00+08', 689901.15, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售数量'),
('b1a1e1c0-0004-0000-0000-000000000017', '2025-05-01 00:00:00+08', 1019345.21, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售收入'),
('b1a1e1c0-0005-0000-0000-000000000017', '2025-05-01 00:00:00+08', 839978.12, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售成本'),
('b1a1e1c0-0001-0000-0000-000000000018', '2025-06-01 00:00:00+08', 178901.23, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '利润总额'),
('b1a1e1c0-0002-0000-0000-000000000018', '2025-06-01 00:00:00+08', 49876.54, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '税费'),
('b1a1e1c0-0003-0000-0000-000000000018', '2025-06-01 00:00:00+08', 598765.43, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售数量'),
('b1a1e1c0-0004-0000-0000-000000000018', '2025-06-01 00:00:00+08', 999876.54, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售收入'),
('b1a1e1c0-0005-0000-0000-000000000018', '2025-06-01 00:00:00+08', 799876.54, '{"region": "阳江"}', '{"source": "mock"}', false, now(), '卷烟销售成本'); 