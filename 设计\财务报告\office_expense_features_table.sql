-- 办公费用二级分类特征工程数据表结构
-- Office Expense Secondary Category Feature Engineering Data Table Structure

-- 英文列名列表（用逗号隔开）：
-- year_month, secondary_category, monthly_total_amount, monthly_average_amount, monthly_transaction_count, 
-- year, month, quarter, is_month_start, is_month_end, is_quarter_start, is_quarter_end, is_first_half_year, 
-- days_in_month, prev_month_total_amount, prev_month_average_amount, prev_month_transaction_count, 
-- prev_2month_total_amount, prev_2month_average_amount, prev_2month_transaction_count, 
-- prev_3month_total_amount, prev_3month_average_amount, prev_3month_transaction_count, 
-- same_period_last_year_total_amount, same_period_last_year_average_amount, same_period_last_year_transaction_count, 
-- growth_rate_vs_prev_month, growth_rate_vs_prev_2month, growth_rate_vs_same_period_last_year, 
-- prev_3month_average_value, prev_3month_slope, annual_budget, current_year_cumulative_amount, 
-- budget_completion_rate, theoretical_progress, budget_variance

-- MySQL建表语句
CREATE TABLE office_expense_features (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    year_month VARCHAR(7) NOT NULL COMMENT '年月（格式：YYYY-MM）',
    secondary_category VARCHAR(100) NOT NULL COMMENT '费用二级分类',
    monthly_total_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '月度总金额',
    monthly_average_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '月度平均金额',
    monthly_transaction_count INT DEFAULT 0 COMMENT '月度交易次数',
    year INT NOT NULL COMMENT '年份',
    month TINYINT NOT NULL COMMENT '月份（1-12）',
    quarter TINYINT NOT NULL COMMENT '季度（1-4）',
    is_month_start TINYINT(1) DEFAULT 0 COMMENT '是否月初（0:否, 1:是）',
    is_month_end TINYINT(1) DEFAULT 0 COMMENT '是否月末（0:否, 1:是）',
    is_quarter_start TINYINT(1) DEFAULT 0 COMMENT '是否季初（0:否, 1:是）',
    is_quarter_end TINYINT(1) DEFAULT 0 COMMENT '是否季末（0:否, 1:是）',
    is_first_half_year TINYINT(1) DEFAULT 0 COMMENT '是否上半年（0:否, 1:是）',
    days_in_month TINYINT NOT NULL COMMENT '当月天数',
    prev_month_total_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '上月总金额',
    prev_month_average_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '上月平均金额',
    prev_month_transaction_count DECIMAL(10,2) DEFAULT 0.00 COMMENT '上月交易次数',
    prev_2month_total_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '上2月总金额',
    prev_2month_average_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '上2月平均金额',
    prev_2month_transaction_count DECIMAL(10,2) DEFAULT 0.00 COMMENT '上2月交易次数',
    prev_3month_total_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '上3月总金额',
    prev_3month_average_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '上3月平均金额',
    prev_3month_transaction_count DECIMAL(10,2) DEFAULT 0.00 COMMENT '上3月交易次数',
    same_period_last_year_total_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '去年同期总金额',
    same_period_last_year_average_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '去年同期平均金额',
    same_period_last_year_transaction_count DECIMAL(10,2) DEFAULT 0.00 COMMENT '去年同期交易次数',
    growth_rate_vs_prev_month DECIMAL(10,6) DEFAULT 0.00 COMMENT '较上月增长率',
    growth_rate_vs_prev_2month DECIMAL(10,6) DEFAULT 0.00 COMMENT '较上2月增长率',
    growth_rate_vs_same_period_last_year DECIMAL(10,6) DEFAULT 0.00 COMMENT '较去年同期增长率',
    prev_3month_average_value DECIMAL(15,2) DEFAULT 0.00 COMMENT '前3月平均值',
    prev_3month_slope DECIMAL(15,6) DEFAULT 0.00 COMMENT '前3月斜率',
    annual_budget DECIMAL(15,2) DEFAULT 0.00 COMMENT '年度预算',
    current_year_cumulative_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '当年累计金额',
    budget_completion_rate DECIMAL(10,6) DEFAULT 0.00 COMMENT '预算完成率',
    theoretical_progress DECIMAL(10,6) DEFAULT 0.00 COMMENT '理论进度',
    budget_variance DECIMAL(10,6) DEFAULT 0.00 COMMENT '预算偏差',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_year_month (year_month),
    INDEX idx_secondary_category (secondary_category),
    INDEX idx_year_month_category (year_month, secondary_category),
    INDEX idx_year (year),
    INDEX idx_quarter (year, quarter)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='办公费用二级分类特征工程数据表'; 