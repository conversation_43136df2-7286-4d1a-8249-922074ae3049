-- 财务知识库数据库设计
-- 项目：阳江AI指标管控系统
-- 模块：财务知识库
-- 数据库：PostgreSQL

-- 1. 财务知识库表 (yjzb_finance_knowledge)
-- 管理知识库，与dify的知识库dataset_id绑定关联
CREATE TABLE yjzb_finance_knowledge (
    id BIGINT PRIMARY KEY,
    tenant_id VARCHAR(12) DEFAULT '000000',
    name VARCHAR(100) NOT NULL,
    description TEXT,
    dataset_id VARCHAR(100),
    status INTEGER DEFAULT 1,
    sort INTEGER DEFAULT 0,
    remark VARCHAR(500),
    is_deleted INTEGER DEFAULT 0,
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_user BIGINT,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. 知识分类表 (yjzb_finance_category)
-- 管理分类，支持层级结构
CREATE TABLE yjzb_finance_category (
    id BIGINT PRIMARY KEY,
    tenant_id VARCHAR(12) DEFAULT '000000',
    knowledge_id BIGINT NOT NULL,
    parent_id BIGINT DEFAULT 0,
    category_name VARCHAR(100) NOT NULL,
    category_code VARCHAR(50),
    category_tags TEXT,
    level_num INTEGER DEFAULT 1,
    full_path VARCHAR(500),
    sort INTEGER DEFAULT 0,
    status INTEGER DEFAULT 1,
    remark VARCHAR(500),
    is_deleted INTEGER DEFAULT 0,
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_user BIGINT,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 添加外键约束
ALTER TABLE yjzb_finance_category
    ADD CONSTRAINT fk_category_knowledge
    FOREIGN KEY (knowledge_id) REFERENCES yjzb_finance_knowledge(id);

-- 3. 知识库文件表 (yjzb_finance_document)
-- 管理文件，与dify的知识库文件document_id绑定关联
CREATE TABLE yjzb_finance_document (
    id BIGINT PRIMARY KEY,
    tenant_id VARCHAR(12) DEFAULT '000000',
    knowledge_id BIGINT NOT NULL,
    category_id BIGINT,
    document_id VARCHAR(100),
    file_name VARCHAR(500) NOT NULL,
    file_original_name VARCHAR(500),
    file_path VARCHAR(500),
    file_size BIGINT,
    file_type VARCHAR(200),
    file_extension VARCHAR(10),
    file_description TEXT,
    file_tags TEXT,
    view_count INTEGER DEFAULT 0,
    download_count INTEGER DEFAULT 0,
    upload_status INTEGER DEFAULT 0,
    sync_status INTEGER DEFAULT 0,
    sync_error_msg TEXT,
    status INTEGER DEFAULT 1,
    sort INTEGER DEFAULT 0,
    remark VARCHAR(500),
    is_deleted INTEGER DEFAULT 0,
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_user BIGINT,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 添加外键约束
ALTER TABLE yjzb_finance_document
    ADD CONSTRAINT fk_document_knowledge
    FOREIGN KEY (knowledge_id) REFERENCES yjzb_finance_knowledge(id);

ALTER TABLE yjzb_finance_document
    ADD CONSTRAINT fk_document_category
    FOREIGN KEY (category_id) REFERENCES yjzb_finance_category(id);


-- 创建索引
CREATE INDEX idx_finance_knowledge_dataset ON yjzb_finance_knowledge(dataset_id);

CREATE INDEX idx_finance_category_knowledge ON yjzb_finance_category(knowledge_id);
CREATE INDEX idx_finance_category_parent ON yjzb_finance_category(parent_id);

CREATE INDEX idx_finance_document_knowledge ON yjzb_finance_document(knowledge_id);
CREATE INDEX idx_finance_document_category ON yjzb_finance_document(category_id);
CREATE INDEX idx_finance_document_document_id ON yjzb_finance_document(document_id);



-- ==================== 添加表和字段注释 ====================

-- 表注释
COMMENT ON TABLE yjzb_finance_knowledge IS '财务知识库表';
COMMENT ON TABLE yjzb_finance_category IS '知识分类表';
COMMENT ON TABLE yjzb_finance_document IS '知识库文件表';

-- yjzb_finance_knowledge 字段注释
COMMENT ON COLUMN yjzb_finance_knowledge.id IS '主键ID';
COMMENT ON COLUMN yjzb_finance_knowledge.tenant_id IS '租户ID，默认000000';
COMMENT ON COLUMN yjzb_finance_knowledge.name IS '知识库名称';
COMMENT ON COLUMN yjzb_finance_knowledge.description IS '知识库描述';
COMMENT ON COLUMN yjzb_finance_knowledge.dataset_id IS 'Dify知识库ID';
COMMENT ON COLUMN yjzb_finance_knowledge.status IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN yjzb_finance_knowledge.sort IS '排序';
COMMENT ON COLUMN yjzb_finance_knowledge.remark IS '备注';
COMMENT ON COLUMN yjzb_finance_knowledge.is_deleted IS '是否已删除：0-未删除，1-已删除';
COMMENT ON COLUMN yjzb_finance_knowledge.create_user IS '创建人ID';
COMMENT ON COLUMN yjzb_finance_knowledge.create_dept IS '创建部门ID';
COMMENT ON COLUMN yjzb_finance_knowledge.create_time IS '创建时间';
COMMENT ON COLUMN yjzb_finance_knowledge.update_user IS '更新人ID';
COMMENT ON COLUMN yjzb_finance_knowledge.update_time IS '更新时间';

-- yjzb_finance_category 字段注释
COMMENT ON COLUMN yjzb_finance_category.id IS '主键ID';
COMMENT ON COLUMN yjzb_finance_category.tenant_id IS '租户ID';
COMMENT ON COLUMN yjzb_finance_category.knowledge_id IS '所属知识库ID';
COMMENT ON COLUMN yjzb_finance_category.parent_id IS '上级分类ID，0表示顶级分类';
COMMENT ON COLUMN yjzb_finance_category.category_name IS '分类名称';
COMMENT ON COLUMN yjzb_finance_category.category_code IS '分类编码';
COMMENT ON COLUMN yjzb_finance_category.category_tags IS '分类标签（JSON数组格式）';
COMMENT ON COLUMN yjzb_finance_category.level_num IS '层级深度';
COMMENT ON COLUMN yjzb_finance_category.full_path IS '完整路径';
COMMENT ON COLUMN yjzb_finance_category.sort IS '排序';
COMMENT ON COLUMN yjzb_finance_category.status IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN yjzb_finance_category.remark IS '备注';
COMMENT ON COLUMN yjzb_finance_category.is_deleted IS '是否已删除';
COMMENT ON COLUMN yjzb_finance_category.create_user IS '创建人';
COMMENT ON COLUMN yjzb_finance_category.create_dept IS '创建部门';
COMMENT ON COLUMN yjzb_finance_category.create_time IS '创建时间';
COMMENT ON COLUMN yjzb_finance_category.update_user IS '更新人';
COMMENT ON COLUMN yjzb_finance_category.update_time IS '更新时间';

-- yjzb_finance_document 字段注释
COMMENT ON COLUMN yjzb_finance_document.id IS '主键ID';
COMMENT ON COLUMN yjzb_finance_document.tenant_id IS '租户ID';
COMMENT ON COLUMN yjzb_finance_document.knowledge_id IS '所属知识库ID';
COMMENT ON COLUMN yjzb_finance_document.category_id IS '所属分类ID';
COMMENT ON COLUMN yjzb_finance_document.document_id IS 'Dify文档ID';
COMMENT ON COLUMN yjzb_finance_document.file_name IS '文件名称';
COMMENT ON COLUMN yjzb_finance_document.file_original_name IS '原始文件名';
COMMENT ON COLUMN yjzb_finance_document.file_path IS '文件路径';
COMMENT ON COLUMN yjzb_finance_document.file_size IS '文件大小（字节）';
COMMENT ON COLUMN yjzb_finance_document.file_type IS '文件类型';
COMMENT ON COLUMN yjzb_finance_document.file_extension IS '文件扩展名';
COMMENT ON COLUMN yjzb_finance_document.file_description IS '文件说明';
COMMENT ON COLUMN yjzb_finance_document.file_tags IS '文件标签（JSON数组格式）';
COMMENT ON COLUMN yjzb_finance_document.view_count IS '浏览次数';
COMMENT ON COLUMN yjzb_finance_document.download_count IS '下载次数';
COMMENT ON COLUMN yjzb_finance_document.upload_status IS '上传状态：0-上传中，1-上传成功，2-上传失败';
COMMENT ON COLUMN yjzb_finance_document.sync_status IS '同步状态：0-未同步，1-同步成功，2-同步失败';
COMMENT ON COLUMN yjzb_finance_document.sync_error_msg IS '同步错误信息';
COMMENT ON COLUMN yjzb_finance_document.status IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN yjzb_finance_document.sort IS '排序';
COMMENT ON COLUMN yjzb_finance_document.remark IS '备注';
COMMENT ON COLUMN yjzb_finance_document.is_deleted IS '是否已删除';
COMMENT ON COLUMN yjzb_finance_document.create_user IS '创建人';
COMMENT ON COLUMN yjzb_finance_document.create_dept IS '创建部门';
COMMENT ON COLUMN yjzb_finance_document.create_time IS '创建时间';
COMMENT ON COLUMN yjzb_finance_document.update_user IS '更新人';
COMMENT ON COLUMN yjzb_finance_document.update_time IS '更新时间';


-- 插入初始数据
INSERT INTO yjzb_finance_knowledge (id, name, description, status, sort, create_user, create_time) VALUES
(1, '财务制度知识库', '包含各类财务制度、规范和操作指南', 1, 1, 1, CURRENT_TIMESTAMP);

INSERT INTO yjzb_finance_category (id, knowledge_id, parent_id, category_name, category_code, level_num, full_path, sort, create_user, create_time) VALUES
(1, 1, 0, '财务制度', 'CWZD', 1, '/财务制度', 1, 1, CURRENT_TIMESTAMP),
(2, 1, 1, '会计制度', 'KJZD', 2, '/财务制度/会计制度', 1, 1, CURRENT_TIMESTAMP),
(3, 1, 1, '审计制度', 'SJZD', 2, '/财务制度/审计制度', 2, 1, CURRENT_TIMESTAMP),
(4, 1, 1, '内控制度', 'NKZD', 2, '/财务制度/内控制度', 3, 1, CURRENT_TIMESTAMP),
(5, 1, 0, '税务政策', 'SWZC', 1, '/税务政策', 2, 1, CURRENT_TIMESTAMP),
(6, 1, 5, '企业所得税', 'QYSDS', 2, '/税务政策/企业所得税', 1, 1, CURRENT_TIMESTAMP),
(7, 1, 5, '增值税', 'ZZS', 2, '/税务政策/增值税', 2, 1, CURRENT_TIMESTAMP),
(8, 1, 5, '个人所得税', 'GRSDS', 2, '/税务政策/个人所得税', 3, 1, CURRENT_TIMESTAMP),
(9, 1, 0, '费用管理', 'FYGL', 1, '/费用管理', 3, 1, CURRENT_TIMESTAMP),
(10, 1, 9, '报销制度', 'BXZD', 2, '/费用管理/报销制度', 1, 1, CURRENT_TIMESTAMP),
(11, 1, 9, '预算管理', 'YSGL', 2, '/费用管理/预算管理', 2, 1, CURRENT_TIMESTAMP),
(12, 1, 9, '成本控制', 'CBKZ', 2, '/费用管理/成本控制', 3, 1, CURRENT_TIMESTAMP),
(13, 1, 0, '法规解读', 'FGJD', 1, '/法规解读', 4, 1, CURRENT_TIMESTAMP),
(14, 1, 13, '财务法规', 'CWFG', 2, '/法规解读/财务法规', 1, 1, CURRENT_TIMESTAMP),
(15, 1, 13, '税法解读', 'SFJD', 2, '/法规解读/税法解读', 2, 1, CURRENT_TIMESTAMP),
(16, 1, 0, '操作指南', 'CZZN', 1, '/操作指南', 5, 1, CURRENT_TIMESTAMP),
(17, 1, 16, '系统操作', 'XTCZ', 2, '/操作指南/系统操作', 1, 1, CURRENT_TIMESTAMP),
(18, 1, 16, '业务流程', 'YWLC', 2, '/操作指南/业务流程', 2, 1, CURRENT_TIMESTAMP),
(19, 1, 16, '常见问题', 'CJWT', 2, '/操作指南/常见问题', 3, 1, CURRENT_TIMESTAMP);

